/********** query start ***************
create query p4 (vertex<user> uid) for graph recommend {
  typedef tuple<vertex m, vertex p, bool hasInterest> matches_at_0__TupTy;
  SetAccum<matches_at_0__TupTy> @matches_at_0;
  MapAccum<vertex<product>, bool> @postMap;
  Seed = { uid };
  VS_s =
    SELECT s
    FROM  Seed:s
    ACCUM foreach tup in s.@matches_at_0 do
              vertex m = tup.m,
              vertex p = tup.p,
              bool   hasInterest = tup.hasInterest,
            IF (m.type =="product") THEN
              p.@postMap += (m -> hasInterest)
            END
          end;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace recommend{ 
class UDF_p4 :public gpelib4::BaseUDF {
struct matches_at_0__TupTy {
  VERTEX m;
  VERTEX p;
  bool hasInterest;

  matches_at_0__TupTy() {
    hasInterest = false;
  }

  matches_at_0__TupTy(VERTEX m_, VERTEX p_, bool hasInterest_){
    m = m_;
    p = p_;
    hasInterest = hasInterest_;
  }
  operator std::tuple<VERTEX, VERTEX, bool>() const {
    return std::make_tuple(m,p,hasInterest);
  }

  matches_at_0__TupTy(const std::tuple<VERTEX, VERTEX, bool>& __GQUERY__other__576037) {
    m = std::get<0>(__GQUERY__other__576037);
    p = std::get<1>(__GQUERY__other__576037);
    hasInterest = std::get<2>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const matches_at_0__TupTy& m) {
    os<<"[";
    os<<"m "<<m.m<<"|";
    os<<"p "<<m.p<<"|";
    os<<"hasInterest "<<m.hasInterest<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const matches_at_0__TupTy& m) {
    os<<"[";
    os<<"m "<<m.m<<"|";
    os<<"p "<<m.p<<"|";
    os<<"hasInterest "<<m.hasInterest<<"]";
      return os ;
  }


  bool operator==(matches_at_0__TupTy const &__GQUERY__other__576037) const {
    return
      m == __GQUERY__other__576037.m &&
      p == __GQUERY__other__576037.p &&
      hasInterest == __GQUERY__other__576037.hasInterest;
  }


  matches_at_0__TupTy& operator+=( const matches_at_0__TupTy& __GQUERY__other__576037) {
      m= __GQUERY__other__576037.m;
      p= __GQUERY__other__576037.p;
      hasInterest |= __GQUERY__other__576037.hasInterest;
    return *this;
  }

  friend std::size_t hash_value(const matches_at_0__TupTy& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.m);
    boost::hash_combine(seed, other.p);
    boost::hash_combine(seed, other.hasInterest);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("m");
      (m).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("p");
      (p).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("hasInterest");
      writer.WriteBool(hasInterest);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(matches_at_0__TupTy const &__GQUERY__other__576037) const {
      if (m < __GQUERY__other__576037.m) return true;
      if (m > __GQUERY__other__576037.m) return false;
      if (p < __GQUERY__other__576037.p) return true;
      if (p > __GQUERY__other__576037.p) return false;
      if (hasInterest < __GQUERY__other__576037.hasInterest) return true;
      if (hasInterest > __GQUERY__other__576037.hasInterest) return false;
      return false;
  }


  bool operator>(matches_at_0__TupTy const &__GQUERY__other__576037) const {
      if (m > __GQUERY__other__576037.m) return true;
      if (m < __GQUERY__other__576037.m) return false;
      if (p > __GQUERY__other__576037.p) return true;
      if (p < __GQUERY__other__576037.p) return false;
      if (hasInterest > __GQUERY__other__576037.hasInterest) return true;
      if (hasInterest < __GQUERY__other__576037.hasInterest) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (m, p, hasInterest);
   }

};

struct __GQUERY__Delta__576037 {
   // accumulators:
   MapAccum<VERTEX, bool >  postMap_1;
   bool __GQUERY__hasChanged___576037postMap_1;
   bool __GQUERY__set___576037postMap_1;

   SetAccum<matches_at_0__TupTy >  matches_at_0_1;
   bool __GQUERY__hasChanged___576037matches_at_0_1;
   bool __GQUERY__set___576037matches_at_0_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037postMap_1 = false;
      __GQUERY__set___576037postMap_1 = false;
      __GQUERY__hasChanged___576037matches_at_0_1 = false;
      __GQUERY__set___576037matches_at_0_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037postMap_1) {
        postMap_1 += __GQUERY__other__576037.postMap_1;
        __GQUERY__hasChanged___576037postMap_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037postMap_1) {
        postMap_1 = __GQUERY__other__576037.postMap_1;
        __GQUERY__set___576037postMap_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037matches_at_0_1) {
        matches_at_0_1 += __GQUERY__other__576037.matches_at_0_1;
        __GQUERY__hasChanged___576037matches_at_0_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037matches_at_0_1) {
        matches_at_0_1 = __GQUERY__other__576037.matches_at_0_1;
        __GQUERY__set___576037matches_at_0_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   MapAccum<VERTEX, bool >  postMap_1;
   SetAccum<matches_at_0__TupTy >  matches_at_0_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      postMap_1 = __GQUERY__other__576037.postMap_1;
      matches_at_0_1 = __GQUERY__other__576037.matches_at_0_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(postMap_1, matches_at_0_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037postMap_1) postMap_1 += __GQUERY__d__576037.postMap_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037postMap_1) postMap_1 = __GQUERY__d__576037.postMap_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037matches_at_0_1) matches_at_0_1 += __GQUERY__d__576037.matches_at_0_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037matches_at_0_1) matches_at_0_1 = __GQUERY__d__576037.matches_at_0_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_p4 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
    if (request.jsoptions_.isMember("uid")) {
      VertexLocalId_t localId;
      if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
        _uid = VERTEX(std::atoll(request.jsoptions_["uid"][0]["id"].asString().c_str()));
      } else {
        std::stringstream ss;
        ss << _schema_VTY_user;
        ss << "_" << request.jsoptions_["uid"][0]["id"].asString();
        if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
          _uid = VERTEX(localId);
        } else {
          std::string msg("Failed to convert user vertex id for parameter uid");
          HF_set_error(request, msg, true);
          return;
        }
      }
      uid_flag = true;
    } else {
      _uid = VERTEX(-1);
      uid_flag = false;
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_p4 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , VERTEX uid, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_uid = uid;
uid_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_p4 () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_uid, GV_SYS_uid_flag, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_Seed_SIZE, GV_SYS_Seed_ORDERBY, GV_SYS_Seed_LASTSET, GV_SYS_VS_s_SIZE, GV_SYS_VS_s_ORDERBY, GV_SYS_VS_s_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_Seed_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Seed_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Seed_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_s_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_s_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS_s_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_uid, new  gpelib4::BroadcastVariable<VERTEX> (_uid));
   gvs->Register (GV_SYS_uid_flag, new  gpelib4::BroadcastVariable<bool> (uid_flag));


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_p4::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void VertexMap_3 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Enter function VertexMap_3 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->matches_at_0_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_tup = *it;

   VERTEX lvar_m;
  bool lvar_m_flag = false;
lvar_m = ( (foreach_tup).m);
lvar_m_flag = true;

   VERTEX lvar_p;
  bool lvar_p_flag = false;
lvar_p = ( (foreach_tup).p);
lvar_p_flag = true;

   bool lvar_hasInterest = false;
  bool lvar_hasInterest_flag = false;
lvar_hasInterest = ( (foreach_tup).hasInterest);
lvar_hasInterest_flag = true;

   if (((GSQL_UTIL::GetVertexEdgeTypeName(_serviceapi, context->GraphAPI(), lvar_m)) == (string("product")))) {
   __GQUERY__Delta__576037 p_other_delta = __GQUERY__Delta__576037 ();
   p_other_delta.__GQUERY__isOther__576037 = true;

   if (lvar_m_flag && lvar_hasInterest_flag) {
  p_other_delta.postMap_1 += ( std::make_pair(lvar_m,( lvar_hasInterest)));
     p_other_delta.__GQUERY__hasChanged___576037postMap_1 = true;
   }
   context->Write (lvar_p, p_other_delta);

  }
if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Exit function VertexMap_3 src: " << src << std::endl;
}

void Reduce_3 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Enter function Reduce_3 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Exit function Reduce_3 v: " << v << std::endl;
}


void (UDF_p4::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_p4::*vertexmap) (const VERTEX& src,
                   V_ATTR*                src_attr,
                   const V_VALUE&         src_val,
                   gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void VertexMap (const VertexLocalId_t& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(vertexmap)) (VERTEX(src), src_attr, src_val, ctx);
}



void (UDF_p4::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("p4");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_p4 DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

{
  const VERTEX& _activate_v = HF_retrieve_param_856409387<VERTEX>(context, GV_PARAM_uid, GV_SYS_uid_flag, "uid");
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}

                 PC = 3;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Seed";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Seed_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Seed_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "Seed", 5);
                     timer_.saveVSetCode(30L, "Seed = { uid };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Seed_LASTSET) = 30L;
                   }
                 break;

           PC = 3; break;
         }

         case 3:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_p4::VertexMap_3;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_p4::Reduce_3;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_s";
                 PC = 4;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_p4 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(38L, "VS_s", 6);
                     timer_.saveVSetCode(38L, "VS_s =\n    SELECT s\n    FROM  Seed:s\n    ACCUM foreach tup in s.@matches_at_0 do\n              vertex m = tup.m,\n              vertex p = tup.p,\n              bool   hasInterest = tup.hasInterest,\n            IF (m.type ==\"product\") THEN\n              p.@postMap += (m -> hasInterest)\n            END\n          end;");
                     timer_.addDependency(38L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Seed_LASTSET));
                     timer_.start("VS_s", 6, context->CalcActiveVertexCount(), 38L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_s_LASTSET) = 38L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_s_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_s_ORDERBY) = -1;
                   break;
                 }

           PC = 4; break;
         }

         case 4:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 3:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_s_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_s_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

private:
   VERTEX _uid;
   bool uid_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_user = 0;
   V_VALUE* vvalptr = nullptr;
};

  bool call_p4(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query p4 in all vetext active mode." << std::endl;
    }
    UDIMPL::recommend::UDF_p4 udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "p4") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "p4") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "p4") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "p4") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_p4(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,VERTEX uid) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::recommend::UDF_p4 udf(request, serviceapi, _graphupdates_ , uid, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_p4_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_p4(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,VERTEX uid){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_p4(graphAPI, request, serviceapi,_graphupdates_ , uid);
}
  bool call_p4(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query p4 in all vetext active mode." << std::endl;
  }

if (values.size() != 1) {
    HF_set_error(request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
VERTEX uid = values[0]->GetVidInternal();


  UDIMPL::recommend::UDF_p4 udf(request, serviceapi, graphupdates, uid, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
