/********** query start ***************
create query test001ya () for graph poc_graph {
  SumAccum<int> @score;
  ListAccum<string> @skill;
  MemberSeed (_) = { members.* };
  CompanySeed (_) = { company.* };
  member_skillset =
    SELECT src
    FROM MemberSeed:src -(member_skill:e)-> skill:skl
    ACCUM src.@skill += skl.id;
  company_skillset =
    SELECT cpn
    FROM CompanySeed:cpn -(member_work_company)-> members:mem
    ACCUM
      FOREACH skl IN mem.@skill DO
        cpn.@skill += skl
      END
    POST-ACCUM
      FOREACH skl IN cpn.@skill DO
        CASE skl
          WHEN "s1" THEN cpn.@score += 1
          WHEN "s2" THEN cpn.@score += 10
          WHEN "s3" THEN cpn.@score += 100
          WHEN "s4" THEN cpn.@score += 1000
          ELSE cpn.@score += 10000
        END
      END;
  PRINT company_skillset;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace poc_graph{ 
class UDF_test001ya :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:
   SumAccum<int64_t >  score_1;
   bool __GQUERY__hasChanged___576037score_1;
   bool __GQUERY__set___576037score_1;

   ListAccum<string >  skill_1;
   bool __GQUERY__hasChanged___576037skill_1;
   bool __GQUERY__set___576037skill_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037score_1 = false;
      __GQUERY__set___576037score_1 = false;
      __GQUERY__hasChanged___576037skill_1 = false;
      __GQUERY__set___576037skill_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037score_1) {
        score_1 += __GQUERY__other__576037.score_1;
        __GQUERY__hasChanged___576037score_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037score_1) {
        score_1 = __GQUERY__other__576037.score_1;
        __GQUERY__set___576037score_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037skill_1) {
        skill_1 += __GQUERY__other__576037.skill_1;
        __GQUERY__hasChanged___576037skill_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037skill_1) {
        skill_1 = __GQUERY__other__576037.skill_1;
        __GQUERY__set___576037skill_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   SumAccum<int64_t >  score_1;
   ListAccum<string >  skill_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      score_1 = __GQUERY__other__576037.score_1;
      skill_1 = __GQUERY__other__576037.skill_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(score_1, skill_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037score_1) score_1 += __GQUERY__d__576037.score_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037score_1) score_1 = __GQUERY__d__576037.score_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037skill_1) skill_1 += __GQUERY__d__576037.skill_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037skill_1) skill_1 = __GQUERY__d__576037.skill_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_test001ya (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
_schema_VATT_company_576037_nCount = VTY_company_attrMeta.GetAttributePosition("nCount", true);
topology4::AttributesMeta& VTY_skill_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_skill_576037_id = VTY_skill_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_test001ya (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
_schema_VATT_company_576037_nCount = VTY_company_attrMeta.GetAttributePosition("nCount", true);
topology4::AttributesMeta& VTY_skill_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_skill_576037_id = VTY_skill_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_test001ya () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_company_skillset_SIZE, GV_SYS_company_skillset_ORDERBY, GV_SYS_company_skillset_LASTSET, GV_SYS_CompanySeed_SIZE, GV_SYS_CompanySeed_ORDERBY, GV_SYS_CompanySeed_LASTSET, GV_SYS_member_skillset_SIZE, GV_SYS_member_skillset_ORDERBY, GV_SYS_member_skillset_LASTSET, GV_SYS_MemberSeed_SIZE, GV_SYS_MemberSeed_ORDERBY, GV_SYS_MemberSeed_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_company_skillset_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_company_skillset_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_company_skillset_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_CompanySeed_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_CompanySeed_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_CompanySeed_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_member_skillset_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_member_skillset_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_member_skillset_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_MemberSeed_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_MemberSeed_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_MemberSeed_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_test001ya::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_6_company_skillset (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function Write_6_company_skillset v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int company_skillset_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_company != -1 && company_skillset_typeIDVar == _schema_VTY_company)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_company_576037_id != -1) {
         writer.WriteNameString("id");
         (string_compress(v_attr, _schema_VATT_company_576037_id)).json_printer(writer, _request, context->GraphAPI(), true);
         }
         if (_schema_VATT_company_576037_company_name != -1) {
         writer.WriteNameString("company_name");
         writer.WriteString(v_attr->GetString(_schema_VATT_company_576037_company_name));
         }
         if (_schema_VATT_company_576037_nCount != -1) {
         writer.WriteNameString("nCount");
         writer.WriteInt(v_attr->GetInt(_schema_VATT_company_576037_nCount, 0));
         }
         writer.WriteName("@score");
         v_val->score_1.json_printer(writer, _request, context->GraphAPI(), true);
         writer.WriteName("@skill");
         v_val->skill_1.json_printer(writer, _request, context->GraphAPI(), true);
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function Write_6_company_skillset v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_4 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function EdgeMap_4 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_skill)) return;

   //attributes' local var declaration
   string skl_id_string = string();
   bool skl_id_string_flag = false;

   //get tgt's attribute
   int tgt_typeIDVar = context->GraphAPI()->GetVertexType(tgt);
     if (tgt_typeIDVar == _schema_VTY_skill) {
       skl_id_string = tgt_attr->GetString(_schema_VATT_skill_576037_id);
     skl_id_string_flag = true;
     }


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   if (skl_id_string_flag) {
  src_delta.skill_1 += ( skl_id_string);
     src_delta.__GQUERY__hasChanged___576037skill_1 = true;
   }



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function EdgeMap_4 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_4 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function Reduce_4 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function Reduce_4 v: " << v << std::endl;
}
void EdgeMap_5 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function EdgeMap_5 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_members)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = tgt_val->skill_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_skl = *it;

   src_delta.skill_1 += ( foreach_skl);
   src_delta.__GQUERY__hasChanged___576037skill_1 = true;
if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function EdgeMap_5 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_5 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function Reduce_5 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   {
  if (delta.__GQUERY__isSrc__576037) {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = l_val->skill_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_skl = *it;

     if (delta.__GQUERY__isSrc__576037) {
if (foreach_skl == string("s1")) { 
   if (delta.__GQUERY__isSrc__576037) {
  l_val->score_1 += ( 1l);
  
   }
 }
   else if (foreach_skl == string("s2")) { 
   if (delta.__GQUERY__isSrc__576037) {
  l_val->score_1 += ( 10l);
  
   }
 }
   else if (foreach_skl == string("s3")) { 
   if (delta.__GQUERY__isSrc__576037) {
  l_val->score_1 += ( 100l);
  
   }
 }
   else if (foreach_skl == string("s4")) { 
   if (delta.__GQUERY__isSrc__576037) {
  l_val->score_1 += ( 1000l);
  
   }
 }
   else { 
   if (delta.__GQUERY__isSrc__576037) {
  l_val->score_1 += ( 10000l);
  
   }
 }
  }

if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }

  }
}
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function Reduce_5 v: " << v << std::endl;
}


void (UDF_test001ya::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_test001ya::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("test001ya");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_test001ya DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_members,true);

                 PC = 3;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "MemberSeed";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_MemberSeed_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_MemberSeed_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(23L, "MemberSeed", 4);
                     timer_.saveVSetCode(23L, "MemberSeed (_) = { members.* };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_MemberSeed_LASTSET) = 23L;
                   }
                 break;

           PC = 3; break;
         }

         case 3:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "MemberSeed") {
                     context->StoreBitSets("MemberSeed", *context->GetBitSets());
                 }
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_company,true);

                 PC = 4;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "CompanySeed";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_CompanySeed_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_CompanySeed_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "CompanySeed", 5);
                     timer_.saveVSetCode(30L, "CompanySeed (_) = { company.* };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_CompanySeed_LASTSET) = 30L;
                   }
                 break;

           PC = 4; break;
         }

         case 4:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "CompanySeed") {
                     context->StoreBitSets("CompanySeed", *context->GetBitSets());
                 }
                 gutil::BitSets& __GQUERY__bs_MemberSeed = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "MemberSeed") { 
                     __GQUERY__bs_MemberSeed = *context->GetBitSets();
                 } else if (context->HasBitSets("MemberSeed")) {
                     context->LoadBitSets("MemberSeed", __GQUERY__bs_MemberSeed);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_MemberSeed);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_MemberSeed;

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_member_skill);

                 edgemap   = &UDF_test001ya::EdgeMap_4;
                 context->set_udfedgemapsetting(2);

                 reduce    = &UDF_test001ya::Reduce_4;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "member_skillset";
                 PC = 5;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(38L, "member_skillset", 6);
                     timer_.saveVSetCode(38L, "member_skillset =\n    SELECT src\n    FROM MemberSeed:src -(member_skill:e)-> skill:skl\n    ACCUM src.@skill += skl.id;");
                     timer_.addDependency(38L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_MemberSeed_LASTSET));
                     timer_.start("member_skillset", 6, context->CalcActiveVertexCount(), 38L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_member_skillset_LASTSET) = 38L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_member_skillset_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_member_skillset_ORDERBY) = -1;
                   break;
                 }

           PC = 5; break;
         }

         case 5:
         {
                 gutil::BitSets& __GQUERY__bs_CompanySeed = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "CompanySeed") { 
                     __GQUERY__bs_CompanySeed = *context->GetBitSets();
                 } else if (context->HasBitSets("CompanySeed")) {
                     context->LoadBitSets("CompanySeed", __GQUERY__bs_CompanySeed);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_CompanySeed);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_CompanySeed;

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_member_work_company);

                 edgemap   = &UDF_test001ya::EdgeMap_5;
                 context->set_udfedgemapsetting(4);

                 reduce    = &UDF_test001ya::Reduce_5;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "company_skillset";
                 PC = 6;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_test001ya INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(80L, "company_skillset", 10);
                     timer_.saveVSetCode(80L, "company_skillset =\n    SELECT cpn\n    FROM CompanySeed:cpn -(member_work_company)-> members:mem\n    ACCUM\n      FOREACH skl IN mem.@skill DO\n        cpn.@skill += skl\n      END\n    POST-ACCUM\n      FOREACH skl IN cpn.@skill DO\n        CASE skl\n          WHEN \"s1\" THEN cpn.@score += 1\n          WHEN \"s2\" THEN cpn.@score += 10\n          WHEN \"s3\" THEN cpn.@score += 100\n          WHEN \"s4\" THEN cpn.@score += 1000\n          ELSE cpn.@score += 10000\n        END\n      END;");
                     timer_.addDependency(80L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_CompanySeed_LASTSET));
                     timer_.start("company_skillset", 10, context->CalcActiveVertexCount(), 80L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_company_skillset_LASTSET) = 80L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_company_skillset_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_company_skillset_ORDERBY) = -1;
                   break;
                 }

           PC = 6; break;
         }

         case 6:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_test001ya::Write_6_company_skillset;
                 context->set_udfprintsetting(1);

                 writer.WriteName("company_skillset");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 7;
                 break;

           PC = 7; break;
         }

         case 7:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 4:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_member_skillset_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_member_skillset_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 5:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_company_skillset_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_company_skillset_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_company = 0;
const int _schema_VTY_members = 1;
const int _schema_VTY_skill = 2;
const int _schema_ETY_member_work_company = 0;
const int _schema_ETY_member_skill = 3;
int _schema_VATT_company_576037_id = -1;
int _schema_VATT_company_576037_company_name = -1;
int _schema_VATT_company_576037_nCount = -1;
int _schema_VATT_skill_576037_id = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_test001ya(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query test001ya in all vetext active mode." << std::endl;
    }
    UDIMPL::poc_graph::UDF_test001ya udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "test001ya") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test001ya") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test001ya") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "test001ya") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_test001ya(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::poc_graph::UDF_test001ya udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_test001ya_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_test001ya(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_test001ya(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_test001ya(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query test001ya in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::poc_graph::UDF_test001ya udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
