/********** query start ***************
create or replace query GrossBooking (vertex<User> rider, int step,
    int startDate=0, int endDate=1999999999) for graph Uber {
  SumAccum<float> @@grossBooking;
  OrAccum<bool> @visited;
  int iterNum = 0;
  case when step%2 == 1 then
    iterNum = step + 1;
  else
    iterNum = step;
  end;
  Start (ANY) = { rider };
  Start = select t from Start:s -((User_to_Device|User_to_Payment):e)-:t
      post_accum t.@visited += true;
  while (Start.size() > 0) limit iterNum do
    Start = select t from Start:s-((User_to_Device|User_to_Payment|User_Ride_Trip):e)-:t
      where t.@visited == false and t != rider
      accum @@grossBooking += t.usedFare
      post-accum t.@visited += true;
  end;
  print @@grossBooking;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace Uber{ 
class UDF_GrossBooking :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:
   OrAccum<bool >  visited_1;
   bool __GQUERY__hasChanged___576037visited_1;
   bool __GQUERY__set___576037visited_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037visited_1 = false;
      __GQUERY__set___576037visited_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037visited_1) {
        visited_1 += __GQUERY__other__576037.visited_1;
        __GQUERY__hasChanged___576037visited_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037visited_1) {
        visited_1 = __GQUERY__other__576037.visited_1;
        __GQUERY__set___576037visited_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   OrAccum<bool >  visited_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      visited_1 = __GQUERY__other__576037.visited_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(visited_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037visited_1) visited_1 += __GQUERY__d__576037.visited_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037visited_1) visited_1 = __GQUERY__d__576037.visited_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_GrossBooking (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Trip_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Trip_576037_usedFare = VTY_Trip_attrMeta.GetAttributePosition("usedFare", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
    if (request.jsoptions_.isMember("rider")) {
      VertexLocalId_t localId;
      if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
        _rider = VERTEX(std::atoll(request.jsoptions_["rider"][0]["id"].asString().c_str()));
      } else {
        std::stringstream ss;
        ss << _schema_VTY_User;
        ss << "_" << request.jsoptions_["rider"][0]["id"].asString();
        if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
          _rider = VERTEX(localId);
        } else {
          std::string msg("Failed to convert user vertex id for parameter rider");
          HF_set_error(request, msg, true);
          return;
        }
      }
      rider_flag = true;
    } else {
      _rider = VERTEX(-1);
      rider_flag = false;
    }
      if (request.jsoptions_.isMember("step")) {
        _step = request.jsoptions_["step"][0].asInt64();
        step_flag = true;
      } else {
        // parameter is not given (null case)
        _step = 0;
        step_flag = false;
      }
      if (request.jsoptions_.isMember("startDate")) {
        _startDate = request.jsoptions_["startDate"][0].asInt64();
        startDate_flag = true;
      } else {
        // parameter is not given (null case)
        _startDate = 0l;
        startDate_flag = true;
      }
      if (request.jsoptions_.isMember("endDate")) {
        _endDate = request.jsoptions_["endDate"][0].asInt64();
        endDate_flag = true;
      } else {
        // parameter is not given (null case)
        _endDate = 1999999999l;
        endDate_flag = true;
      }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_GrossBooking (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , VERTEX rider, int64_t step, int64_t startDate, int64_t endDate, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Trip_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Trip_576037_usedFare = VTY_Trip_attrMeta.GetAttributePosition("usedFare", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_rider = rider;
rider_flag = true;
_step = step;
step_flag = true;
_startDate = startDate;
startDate_flag = true;
_endDate = endDate;
endDate_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_GrossBooking () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_rider, GV_SYS_rider_flag, GV_PARAM_step, GV_SYS_step_flag, GV_PARAM_startDate, GV_SYS_startDate_flag, GV_PARAM_endDate, GV_SYS_endDate_flag, GV_GV_i_1, GV_GACC_grossBooking_1, GV_GV_iterNum_1, GV_SYS_i9, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_Start_SIZE, GV_SYS_Start_ORDERBY, GV_SYS_Start_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_Start_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Start_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Start_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_rider, new  gpelib4::BroadcastVariable<VERTEX> (_rider));
   gvs->Register (GV_SYS_rider_flag, new  gpelib4::BroadcastVariable<bool> (rider_flag));
   gvs->Register (GV_PARAM_step, new  gpelib4::BroadcastVariable<int64_t> (_step));
   gvs->Register (GV_SYS_step_flag, new  gpelib4::BroadcastVariable<bool> (step_flag));
   gvs->Register (GV_PARAM_startDate, new  gpelib4::BroadcastVariable<int64_t> (_startDate));
   gvs->Register (GV_SYS_startDate_flag, new  gpelib4::BroadcastVariable<bool> (startDate_flag));
   gvs->Register (GV_PARAM_endDate, new  gpelib4::BroadcastVariable<int64_t> (_endDate));
   gvs->Register (GV_SYS_endDate_flag, new  gpelib4::BroadcastVariable<bool> (endDate_flag));

   // global variables
   gvs->Register (GV_GV_i_1, new gpelib4::StateVariable<int64_t> (int64_t()));
   gvs->Register (GV_GV_iterNum_1, new gpelib4::StateVariable<int64_t> (int64_t()));

   // loop indices
   gvs->Register (GV_SYS_i9, new gpelib4::StateVariable<uint32_t> (0));

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_grossBooking_1, new  gpelib4::SumVariable<SumAccum<float > > (SumAccum<float >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_GrossBooking::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_8 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Enter function EdgeMap_8 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;


   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function EdgeMap_8 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_8 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Enter function Reduce_8 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = (V_VALUE (v_val) += delta);

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isTgt__576037) {
  l_val.visited_1 += ( true);
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function Reduce_8 v: " << v << std::endl;
}
void EdgeMap_10 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Enter function EdgeMap_10 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration
   float t_usedFare_float = 0;
   bool t_usedFare_float_flag = false;

   //get tgt's attribute
   int tgt_typeIDVar = context->GraphAPI()->GetVertexType(tgt);
     if (tgt_typeIDVar == _schema_VTY_Trip) {
       t_usedFare_float = tgt_attr->GetDouble(_schema_VATT_Trip_576037_usedFare, 0.0);
     t_usedFare_float_flag = true;
     }

   // WHERE
   if (!(((tgt_val.visited_1.data_) == (false) 
&& (tgt) != (HF_retrieve_param_856409387<VERTEX>(context, GV_PARAM_rider, GV_SYS_rider_flag, "rider"))))) return;


   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   if (t_usedFare_float_flag) {
  context->GlobalVariable_Reduce<SumAccum<float > > (GV_GACC_grossBooking_1, SumAccum<float >  (( t_usedFare_float)));
   }



   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function EdgeMap_10 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_10 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Enter function Reduce_10 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = (V_VALUE (v_val) += delta);

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isTgt__576037) {
  l_val.visited_1 += ( true);
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function Reduce_10 v: " << v << std::endl;
}


void (UDF_GrossBooking::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_GrossBooking::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("GrossBooking");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_GrossBooking DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                     context->GlobalVariable_GetValue<SumAccum<float > > (GV_GACC_grossBooking_1).clear();
                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_iterNum_1)=( 0l);
                 PC = 4;
                 break;
           PC = 4; break;
         }

         case 4:
         {

                 if (!((HF_robustModulo(context, HF_retrieve_param_856409387<int64_t>(context, GV_PARAM_step, GV_SYS_step_flag, "step"), 2l)) == (1l))) {
                   PC = 6;
                   break;
                 }
           PC = 5; break;
         }

         case 5:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_iterNum_1)=( (HF_retrieve_param_856409387<int64_t>(context, GV_PARAM_step, GV_SYS_step_flag, "step")+1l));
                 PC = 7;
                 break;
           PC = 6; break;
         }

         case 6:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_iterNum_1)=( HF_retrieve_param_856409387<int64_t>(context, GV_PARAM_step, GV_SYS_step_flag, "step"));
                 PC = 7;
                 break;
           PC = 7; break;
         }

         case 7:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

{
  const VERTEX& _activate_v = HF_retrieve_param_856409387<VERTEX>(context, GV_PARAM_rider, GV_SYS_rider_flag, "rider");
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}

                 PC = 8;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Start";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(192L, "Start", 17);
                     timer_.saveVSetCode(192L, "Start (ANY) = { rider };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET) = 192L;
                   }
                 break;

           PC = 8; break;
         }

         case 8:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_User_to_Device);

                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_User_to_Payment);

                 edgemap   = &UDF_GrossBooking::EdgeMap_8;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_GrossBooking::Reduce_8;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Start";
                 PC = 9;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(190L, "Start", 19);
                     timer_.saveVSetCode(190L, "Start =\n\tselect t\n\tfrom Start:s -((User_to_Device|User_to_Payment):e)- _:t\n\tpost-accum t.@visited += true;");
                     timer_.addDependency(190L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET));
                     timer_.start("Start", 19, context->CalcActiveVertexCount(), 190L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET) = 190L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
                   break;
                 }

           PC = 9; break;
         }

         case 9:
         {

                 { // loop start
                   int i9 = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_i9);

                   if (!((((context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE)) > (0l))) && (i9 < context->GlobalVariable_GetValue<int64_t> (GV_GV_iterNum_1)))) {
                     PC = 11;
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_i9) = 0;
                       break;
                   }
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_i9) = i9 + 1;
if (UNLIKELY((++__loop_count__ & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  context->Abort();
  return;
}
                 }

           PC = 10; break;
         }

         case 10:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_User_Ride_Trip);

                 edgemap   = &UDF_GrossBooking::EdgeMap_10;
                 context->set_udfedgemapsetting(6);

                 reduce    = &UDF_GrossBooking::Reduce_10;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Start";
                 PC = 9;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_GrossBooking INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(351L, "Start", 26);
                     timer_.saveVSetCode(351L, "Start =\n\tselect t\n\tfrom Start:s -(User_Ride_Trip:e)- _:t\n\twhere  t.@visited == false and t != rider\n\taccum       @@grossBooking += t.usedFare\n\t\t\n\tpost-accum t.@visited += true;");
                     timer_.addDependency(351L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET));
                     timer_.start("Start", 26, context->CalcActiveVertexCount(), 351L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET) = 351L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
                   break;
                 }

           PC = 11; break;
         }

         case 11:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@grossBooking");
writer.WriteFloat(( (context->GlobalVariable_GetValue<SumAccum<float > > (GV_GACC_grossBooking_1)).data_));

                 } //END
                 writer.WriteEndObject();
                 PC = 12;
                 break;

           PC = 12; break;
         }

         case 12:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 8:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 10:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

int64_t HF_robustModulo(gpelib4::BaseContext* context, int64_t a, int64_t b) {
  if (b == 0) {
    std::string msg("Runtime Error: divider is zero.");
    HF_set_error(_request, msg, true);
    context->Abort();
    return 0;
  }
  return a % b;
}

private:
   VERTEX _rider;
   bool rider_flag;
   int64_t _step;
   bool step_flag;
   int64_t _startDate;
   bool startDate_flag;
   int64_t _endDate;
   bool endDate_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Trip = 0;
const int _schema_VTY_User = 2;
const int _schema_ETY_User_Ride_Trip = 1;
const int _schema_ETY_User_to_Device = 2;
const int _schema_ETY_User_to_Payment = 3;
int _schema_VATT_Trip_576037_usedFare = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_GrossBooking(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query GrossBooking in all vetext active mode." << std::endl;
    }
    UDIMPL::Uber::UDF_GrossBooking udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "GrossBooking") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "GrossBooking") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "GrossBooking") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "GrossBooking") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_GrossBooking(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,VERTEX rider,int64_t step,int64_t startDate,int64_t endDate) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::Uber::UDF_GrossBooking udf(request, serviceapi, _graphupdates_ , rider, step, startDate, endDate, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_GrossBooking_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_GrossBooking(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,VERTEX rider,int64_t step,int64_t startDate,int64_t endDate){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_GrossBooking(graphAPI, request, serviceapi,_graphupdates_ , rider, step, startDate, endDate);
}
  bool call_GrossBooking(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query GrossBooking in all vetext active mode." << std::endl;
  }

if (values.size() != 4) {
    HF_set_error(request, "Invalid parameter size: 4|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
VERTEX rider = values[0]->GetVidInternal();

int64_t step = values[1]->GetIntInternal();
int64_t startDate = values[2]->GetIntInternal();
int64_t endDate = values[3]->GetIntInternal();

  UDIMPL::Uber::UDF_GrossBooking udf(request, serviceapi, graphupdates, rider, step, startDate, endDate, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
