/********** query start ***************
CREATE OR REPLACE QUERY test1() FOR GRAPH poc_graph {
  MapAccum<STRING, INT> @vneighbors;
  nodes = { members.* };

  Result = SELECT n 
            FROM nodes:n
            POST-ACCUM
              FOREACH v IN n.neighbors() DO
                 n.@vneighbors += (v.type -> 1),
                 n.registrationDate = 666
              END;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace poc_graph{ 
class UDF_test1 :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:
   MapAccum<string, int64_t >  vneighbors_1;
   bool __GQUERY__hasChanged___576037vneighbors_1;
   bool __GQUERY__set___576037vneighbors_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037vneighbors_1 = false;
      __GQUERY__set___576037vneighbors_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037vneighbors_1) {
        vneighbors_1 += __GQUERY__other__576037.vneighbors_1;
        __GQUERY__hasChanged___576037vneighbors_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037vneighbors_1) {
        vneighbors_1 = __GQUERY__other__576037.vneighbors_1;
        __GQUERY__set___576037vneighbors_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   MapAccum<string, int64_t >  vneighbors_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      vneighbors_1 = __GQUERY__other__576037.vneighbors_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(vneighbors_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037vneighbors_1) vneighbors_1 += __GQUERY__d__576037.vneighbors_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037vneighbors_1) vneighbors_1 = __GQUERY__d__576037.vneighbors_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_test1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }
      graphupdates = serviceapi.CreateGraphUpdates(&_request);

}




   UDF_test1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;
      graphupdates = _graphupdates_;

}

   ~UDF_test1 () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_nodes_SIZE, GV_SYS_nodes_ORDERBY, GV_SYS_nodes_LASTSET, GV_SYS_Result_SIZE, GV_SYS_Result_ORDERBY, GV_SYS_Result_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_nodes_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_nodes_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_nodes_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Result_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Result_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Result_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_test1::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void VertexMap_2 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Enter function VertexMap_2 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Exit function VertexMap_2 src: " << src << std::endl;
}

void Reduce_2 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Enter function Reduce_2 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   {
  if (delta.__GQUERY__isSrc__576037) {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = HF_get_neighbors(context, v, "", [&](EdgesCollection& ec) { return true; });
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_v = *it;

   if (delta.__GQUERY__isSrc__576037) {
  l_val->vneighbors_1 += ( std::make_pair(GSQL_UTIL::GetVertexEdgeTypeName(_serviceapi, context->GraphAPI(), foreach_v),( 1l)));
  
   }

   if (delta.__GQUERY__isSrc__576037) {
  topology4::AttributeUpdatePointer attrUpdate_v = graphupdates->GetVertexAttributeUpdate (context->GraphAPI()->GetVertexType(v));
  
  int registrationDate_typeIDVar = context->GraphAPI()->GetVertexType(v);
   if(registrationDate_typeIDVar == _schema_VTY_members) {
  if (attrUpdate_v->IsUpdatable(_schema_VATT_members_576037_registrationDate))
   {attrUpdate_v->Set(_schema_VATT_members_576037_registrationDate, (uint64_t(( 666l))));
  } else {
  attrUpdate_v->ThrowDiscriminatorError(_schema_VATT_members_576037_registrationDate);
  }
   }
  
  { topology4::DeltaVertexId vVid (context->GraphAPI()->GetVertexType(v), v);
  graphupdates->UpsertVertex (vVid, attrUpdate_v); }
  
   }

if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }

  }
}
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Exit function Reduce_2 v: " << v << std::endl;
}


void (UDF_test1::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_test1::*vertexmap) (const VERTEX& src,
                   V_ATTR*                src_attr,
                   const V_VALUE&         src_val,
                   gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void VertexMap (const VertexLocalId_t& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(vertexmap)) (VERTEX(src), src_attr, src_val, ctx);
}



void (UDF_test1::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("test1");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_test1 DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_members,true);

                 PC = 2;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "nodes";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_nodes_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_nodes_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(17L, "nodes", 3);
                     timer_.saveVSetCode(17L, "nodes = { members.* };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_nodes_LASTSET) = 17L;
                   }
                 break;

           PC = 2; break;
         }

         case 2:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_test1::VertexMap_2;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_test1::Reduce_2;
                 context->set_udfreducesetting(3);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Result";
                 PC = 3;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_test1 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "Result", 5);
                     timer_.saveVSetCode(30L, "Result = SELECT n \n            FROM nodes:n\n            POST-ACCUM\n              FOREACH v IN n.neighbors() DO\n                 n.@vneighbors += (v.type -> 1),\n                 n.registrationDate = 666\n              END;");
                     timer_.addDependency(30L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_nodes_LASTSET));
                     timer_.start("Result", 5, context->CalcActiveVertexCount(), 30L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result_LASTSET) = 30L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Result_ORDERBY) = -1;
                   break;
                 }

           PC = 3; break;
         }

         case 3:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 2:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_Result_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
if (!isQueryCalled) {
if (context->IsAborted() || _request.error_) {
  graphupdates->Rollback(true);
} else {
  _request.ResetQueryState();
  _serviceapi->QueryResponse_VIdtoUId(&_request);
  if (!graphupdates->Commit(true)) {
    throw gutil::GsqlException("Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
  }
}
}
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}


BagAccum<VERTEX> HF_get_neighbors(gpelib4::GlobalVariableContext* context, const VERTEX& src, const std::string& str, std::function<bool(EdgesCollection&)> condition) {
  gvector<uint32_t> edgeTypeIds;
  if (str.length() != 0) {
    edgeTypeIds.emplace_back(GSQL_UTIL::GetEdgeTypeId(_serviceapi->GetTopologyMeta(), _request, str));
  } else {
    gvector<uint32_t> allEdgeTypeIds {_schema_ETY_member_work_company, _schema_ETY_member_follow_company, _schema_ETY_member_member, _schema_ETY_member_skill, _schema_ETY_member_to_all, _schema_ETY_all_to_skill, _schema_ETY_all_to_all};
    edgeTypeIds = std::move(allEdgeTypeIds);
  }
  return HF_get_neighbors(context, src, edgeTypeIds, condition);
}

BagAccum<VERTEX> HF_get_neighbors(gpelib4::GlobalVariableContext* context, const VERTEX& src, const SetAccum<std::string>& set, std::function<bool(EdgesCollection&)> condition) {
  gvector<uint32_t> edgeTypeIds;  for (auto it = set.begin(); it != set.end(); ++it) {
    edgeTypeIds.emplace_back(GSQL_UTIL::GetEdgeTypeId(_serviceapi->GetTopologyMeta(), _request, *it));
  }
  return HF_get_neighbors(context, src, edgeTypeIds, condition);
}

BagAccum<VERTEX> HF_get_neighbors(gpelib4::GlobalVariableContext* context, const VERTEX& src, const ListAccum<std::string>& list, std::function<bool(EdgesCollection&)> condition) {
  gvector<uint32_t> edgeTypeIds;  for (auto it = list.begin(); it != list.end(); ++it) {
    edgeTypeIds.emplace_back(GSQL_UTIL::GetEdgeTypeId(_serviceapi->GetTopologyMeta(), _request, *it));
  }
  return HF_get_neighbors(context, src, edgeTypeIds, condition);
}

BagAccum<VERTEX> HF_get_neighbors(gpelib4::GlobalVariableContext* context, const VERTEX& src, gvector<uint32_t> edgeTypeIds, std::function<bool(EdgesCollection&)> condition) {
  BagAccum<VERTEX> set_s_ETY_str;
  gapi4::UDFGraphAPI* gapi = context->GraphAPI();
  EdgesCollection ec;
  gapi4::EdgesFilter_ByTypes filter(edgeTypeIds);
  gapi->GetEdges(src, &filter, ec);
  while(ec.NextEdge()) {
    if (!(context->GlobalVariable_GetValue<SetAccum<uint32_t> >(GV_SYS_VTs)).contains(context->GraphAPI()->GetVertexType(ec.GetCurrentToVId()))) continue;
    if (condition(ec)) {
    set_s_ETY_str += ec.GetCurrentToVId();
}
  }
  return set_s_ETY_str;
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;

   topology4::GraphUpdatesPointer graphupdates;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_members = 1;
const int _schema_ETY_member_work_company = 0;
const int _schema_ETY_member_follow_company = 1;
const int _schema_ETY_member_member = 2;
const int _schema_ETY_member_skill = 3;
const int _schema_ETY_member_to_all = 4;
const int _schema_ETY_all_to_skill = 5;
const int _schema_ETY_all_to_all = 6;
int _schema_VATT_members_576037_registrationDate = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_test1(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query test1 in all vetext active mode." << std::endl;
    }
    UDIMPL::poc_graph::UDF_test1 udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "test1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "test1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_test1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::poc_graph::UDF_test1 udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_test1_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_test1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_test1(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_test1(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query test1 in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::poc_graph::UDF_test1 udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
