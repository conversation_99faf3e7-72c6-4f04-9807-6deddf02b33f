/********** query start ***************
CREATE OR REPLACE QUERY a() FOR GRAPH ldbc_snb {
  SetAccum<int> @@isSrc;
  SetAccum<int> @isSrc;

  v= SELECT t
            FROM   Person:s- (LIKES>)-Post - (<CONTAINER_OF)-:f - (HAS_TAG>) - :t
            WHERE  s.firstName == "Viktor" AND s.lastName == "Akhiezer"
                   AND t.id == 1084
            ACCUM t.@isSrc += s.id, @@isSrc += 1
            POST-ACCUM t.@isSrc += 1, @@isSrc += 2;

  print v;
  print @@isSrc;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_a :public gpelib4::BaseUDF {
struct gsqlpe_src_attrib_install_tup__1 {
  uint64_t id;

  gsqlpe_src_attrib_install_tup__1() {
    id = 0;
  }

  gsqlpe_src_attrib_install_tup__1(uint64_t id_){
    id = id_;
  }
  operator uint64_t() const {
    return id;
}


  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const gsqlpe_src_attrib_install_tup__1& m) {
    os<<"[";
    os<<"id "<<m.id<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const gsqlpe_src_attrib_install_tup__1& m) {
    os<<"[";
    os<<"id "<<m.id<<"]";
      return os ;
  }


  bool operator==(gsqlpe_src_attrib_install_tup__1 const &__GQUERY__other__576037) const {
    return
      id == __GQUERY__other__576037.id;
  }


  gsqlpe_src_attrib_install_tup__1& operator+=( const gsqlpe_src_attrib_install_tup__1& __GQUERY__other__576037) {
      id += __GQUERY__other__576037.id;
    return *this;
  }

  friend std::size_t hash_value(const gsqlpe_src_attrib_install_tup__1& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.id);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("id");
      writer.WriteUnsignedInt(id);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(gsqlpe_src_attrib_install_tup__1 const &__GQUERY__other__576037) const {
      if (id < __GQUERY__other__576037.id) return true;
      if (id > __GQUERY__other__576037.id) return false;
      return false;
  }


  bool operator>(gsqlpe_src_attrib_install_tup__1 const &__GQUERY__other__576037) const {
      if (id > __GQUERY__other__576037.id) return true;
      if (id < __GQUERY__other__576037.id) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (id);
   }

};

struct __GQUERY__Delta__576037 {
   // accumulators:
   SetAccum<VERTEX >  srcIdOnlyPropagAcc_1_1;
   bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1;
   bool __GQUERY__set___576037srcIdOnlyPropagAcc_1_1;

   SetAccum<VERTEX >  srcIdOnlyPropagAcc_2_1;
   bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1;
   bool __GQUERY__set___576037srcIdOnlyPropagAcc_2_1;

   SetAccum<int64_t >  isSrc_1;
   bool __GQUERY__hasChanged___576037isSrc_1;
   bool __GQUERY__set___576037isSrc_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = false;
      __GQUERY__set___576037srcIdOnlyPropagAcc_1_1 = false;
      __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = false;
      __GQUERY__set___576037srcIdOnlyPropagAcc_2_1 = false;
      __GQUERY__hasChanged___576037isSrc_1 = false;
      __GQUERY__set___576037isSrc_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1) {
        srcIdOnlyPropagAcc_1_1 += __GQUERY__other__576037.srcIdOnlyPropagAcc_1_1;
        __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037srcIdOnlyPropagAcc_1_1) {
        srcIdOnlyPropagAcc_1_1 = __GQUERY__other__576037.srcIdOnlyPropagAcc_1_1;
        __GQUERY__set___576037srcIdOnlyPropagAcc_1_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1) {
        srcIdOnlyPropagAcc_2_1 += __GQUERY__other__576037.srcIdOnlyPropagAcc_2_1;
        __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037srcIdOnlyPropagAcc_2_1) {
        srcIdOnlyPropagAcc_2_1 = __GQUERY__other__576037.srcIdOnlyPropagAcc_2_1;
        __GQUERY__set___576037srcIdOnlyPropagAcc_2_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037isSrc_1) {
        isSrc_1 += __GQUERY__other__576037.isSrc_1;
        __GQUERY__hasChanged___576037isSrc_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037isSrc_1) {
        isSrc_1 = __GQUERY__other__576037.isSrc_1;
        __GQUERY__set___576037isSrc_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   SetAccum<VERTEX >  srcIdOnlyPropagAcc_1_1;
   SetAccum<VERTEX >  srcIdOnlyPropagAcc_2_1;
   SetAccum<int64_t >  isSrc_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      srcIdOnlyPropagAcc_1_1 = __GQUERY__other__576037.srcIdOnlyPropagAcc_1_1;
      srcIdOnlyPropagAcc_2_1 = __GQUERY__other__576037.srcIdOnlyPropagAcc_2_1;
      isSrc_1 = __GQUERY__other__576037.isSrc_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(srcIdOnlyPropagAcc_1_1, srcIdOnlyPropagAcc_2_1, isSrc_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1) srcIdOnlyPropagAcc_1_1 += __GQUERY__d__576037.srcIdOnlyPropagAcc_1_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037srcIdOnlyPropagAcc_1_1) srcIdOnlyPropagAcc_1_1 = __GQUERY__d__576037.srcIdOnlyPropagAcc_1_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1) srcIdOnlyPropagAcc_2_1 += __GQUERY__d__576037.srcIdOnlyPropagAcc_2_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037srcIdOnlyPropagAcc_2_1) srcIdOnlyPropagAcc_2_1 = __GQUERY__d__576037.srcIdOnlyPropagAcc_2_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037isSrc_1) isSrc_1 += __GQUERY__d__576037.isSrc_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037isSrc_1) isSrc_1 = __GQUERY__d__576037.isSrc_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_a (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
_schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_a (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
_schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_a () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_GACC_gsqlpe_src_attrib_install_map__1_1, GV_GV_i_1, GV_GACC_isSrc_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_VS__2_SIZE, GV_SYS_VS__2_ORDERBY, GV_SYS_VS__2_LASTSET, GV_SYS_v_SIZE, GV_SYS_v_ORDERBY, GV_SYS_v_LASTSET, GV_SYS_VS__1_SIZE, GV_SYS_VS__1_ORDERBY, GV_SYS_VS__1_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_VS__2_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS__2_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS__2_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_v_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_v_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_v_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS__1_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS__1_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS__1_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params

   // global variables
   gvs->Register (GV_GV_i_1, new gpelib4::StateVariable<int64_t> (int64_t()));

   // loop indices

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_gsqlpe_src_attrib_install_map__1_1, new  gpelib4::SumVariable<MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > > (MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 >  ()));
   gvs->Register (GV_GACC_isSrc_1, new  gpelib4::SumVariable<SetAccum<int64_t > > (SetAccum<int64_t >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_a::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_9_v (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function Write_9_v v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int v_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_Tag != -1 && v_typeIDVar == _schema_VTY_Tag)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Tag_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Tag_576037_id, 0));
         }
         if (_schema_VATT_Tag_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_Tag_576037_name));
         }
         if (_schema_VATT_Tag_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_Tag_576037_url));
         }
         writer.WriteName("@isSrc");
         v_val->isSrc_1.json_printer(writer, _request, context->GraphAPI(), true);
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function Write_9_v v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_5 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function EdgeMap_5 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(src) == _schema_VTY_Person && context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_Post)) return;

   //attributes' local var declaration
   string s_lastName_string = string();
   bool s_lastName_string_flag = false;
   string s_firstName_string = string();
   bool s_firstName_string_flag = false;

   //get src's attribute
   int src_typeIDVar = context->GraphAPI()->GetVertexType(src);
     if (src_typeIDVar == _schema_VTY_Person) {
       s_firstName_string = src_attr->GetString(_schema_VATT_Person_576037_firstName);
     s_firstName_string_flag = true;
     s_lastName_string = src_attr->GetString(_schema_VATT_Person_576037_lastName);
     s_lastName_string_flag = true;
     }

   // WHERE
   if (!((s_firstName_string_flag && (s_firstName_string) == (string("Viktor")) 
&& s_lastName_string_flag && (s_lastName_string) == (string("Akhiezer"))))) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   tgt_delta.srcIdOnlyPropagAcc_1_1 += ( src);
   tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = true;


   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function EdgeMap_5 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_5 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function Reduce_5 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration
   uint64_t s_id_uint64_t = 0;
   bool s_id_uint64_t_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_Person) {
       s_id_uint64_t = v_attr->GetUInt(_schema_VATT_Person_576037_id, 0);
     s_id_uint64_t_flag = true;
     }


if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

     if (delta.__GQUERY__isSrc__576037) {
if (delta.__GQUERY__isSrc__576037 && (GSQL_UTIL::GetVertexEdgeTypeName(_serviceapi, context->GraphAPI(), v)) == (string("Person"))) {

   if (delta.__GQUERY__isSrc__576037 && s_id_uint64_t_flag) {
  context->GlobalVariable_Reduce<MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > > (GV_GACC_gsqlpe_src_attrib_install_map__1_1, ( MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > (v,( gsqlpe_src_attrib_install_tup__1(( s_id_uint64_t))))));
   }

  }
  }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function Reduce_5 v: " << v << std::endl;
}
void EdgeMap_6 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function EdgeMap_6 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   tgt_delta.srcIdOnlyPropagAcc_2_1 += ( src_val->srcIdOnlyPropagAcc_1_1);
   tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = true;


   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function EdgeMap_6 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_6 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function Reduce_6 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->srcIdOnlyPropagAcc_1_1.clear();
  
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function Reduce_6 v: " << v << std::endl;
}
void EdgeMap_7 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function EdgeMap_7 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration
   uint64_t t_id_uint64_t = 0;
   bool t_id_uint64_t_flag = false;

   //get tgt's attribute
   int tgt_typeIDVar = context->GraphAPI()->GetVertexType(tgt);
     if (tgt_typeIDVar == _schema_VTY_Tag) {
       t_id_uint64_t = tgt_attr->GetUInt(_schema_VATT_Tag_576037_id, 0);
     t_id_uint64_t_flag = true;
     }

   // WHERE
   if (!(t_id_uint64_t_flag && (t_id_uint64_t) == (1084l))) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->srcIdOnlyPropagAcc_2_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_s = *it;

   uint64_t lvar_s__id__gsqlpe__1 = 0;
  bool lvar_s__id__gsqlpe__1_flag = false;
lvar_s__id__gsqlpe__1 = ( (context->GlobalVariable_GetValue<MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > > (GV_GACC_gsqlpe_src_attrib_install_map__1_1)).get(( foreach_s)).id);
lvar_s__id__gsqlpe__1_flag = true;

   if (lvar_s__id__gsqlpe__1_flag) {
  tgt_delta.isSrc_1 += ( lvar_s__id__gsqlpe__1);
     tgt_delta.__GQUERY__hasChanged___576037isSrc_1 = true;
   }

   context->GlobalVariable_Reduce<SetAccum<int64_t > > (GV_GACC_isSrc_1, SetAccum<int64_t >  (( 1l)));
if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function EdgeMap_7 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_7 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function Reduce_7 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isTgt__576037) {
  l_val->isSrc_1 += ( 1l);
  
   }

   if (delta.__GQUERY__isTgt__576037) {
  context->GlobalVariable_Reduce<SetAccum<int64_t > > (GV_GACC_isSrc_1, SetAccum<int64_t >  (( 2l)));
   }

   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->srcIdOnlyPropagAcc_2_1.clear();
  
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function Reduce_7 v: " << v << std::endl;
}


void (UDF_a::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_a::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("a");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_a DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                     context->GlobalVariable_GetValue<MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > > (GV_GACC_gsqlpe_src_attrib_install_map__1_1).clear();
                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                     context->GlobalVariable_GetValue<SetAccum<int64_t > > (GV_GACC_isSrc_1).clear();
                 PC = 4;
                 break;
           PC = 4; break;
         }

         case 4:
         {

                 PC = 5;
                 break;
           PC = 5; break;
         }

         case 5:
         {

                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_Person,true);

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_LIKES);

                 edgemap   = &UDF_a::EdgeMap_5;
                 context->set_udfedgemapsetting(1);

                 reduce    = &UDF_a::Reduce_5;
                 context->set_udfreducesetting(3);

{
gvector<gvector<gindex::IndexPredicate>> hints_src_5;
{
 gvector<gindex::IndexPredicate> hint_src_5;
if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_firstName != -1) {
hint_src_5.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_firstName, {string("Viktor")}
));
}
hints_src_5.push_back(std::move(hint_src_5));
}
{
 gvector<gindex::IndexPredicate> hint_src_5;
if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_lastName != -1) {
hint_src_5.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_lastName, {string("Akhiezer")}
));
}
hints_src_5.push_back(std::move(hint_src_5));
}
gindex::IndexHint indexHint_src_5(std::move(hints_src_5));
context->SetSrcIndexHint(std::move(indexHint_src_5));
}
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS__1";
                 PC = 6;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(276L, "VS__1", 23);
                     timer_.saveVSetCode(276L, "VS__1 =\n\tselect x_1\n\tfrom Person:s -(LIKES>:x)- Post:x_1\n\twhere  s.firstName == \"Viktor\" and s.lastName == \"Akhiezer\"\n\taccum  x_1.@srcIdOnlyPropagAcc_1 += s\n\tpost-accum \n\t\t      // install attributes needed in last hop\n\t\t      if s.type == \"Person\" then @@gsqlpe_src_attrib_install_map__1 += (s -> gsqlpe_src_attrib_install_tup__1(s.id)) end;");
                     timer_.start("VS__1", 23, context->CalcActiveVertexCount(), 276L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__1_LASTSET) = 276L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__1_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS__1_ORDERBY) = -1;
                   break;
                 }

           PC = 6; break;
         }

         case 6:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_CONTAINER_OF_REVERSE);

                 edgemap   = &UDF_a::EdgeMap_6;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_a::Reduce_6;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS__2";
                 PC = 7;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(528L, "VS__2", 32);
                     timer_.saveVSetCode(528L, "VS__2 =\n\tselect f\n\tfrom VS__1:x_1 -(CONTAINER_OF_REVERSE>:x_3)- _:f\n\taccum  f.@srcIdOnlyPropagAcc_2 += x_1.@srcIdOnlyPropagAcc_1\n\tpost-accum x_1.@srcIdOnlyPropagAcc_1.clear();");
                     timer_.addDependency(528L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__1_LASTSET));
                     timer_.start("VS__2", 32, context->CalcActiveVertexCount(), 528L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__2_LASTSET) = 528L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__2_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS__2_ORDERBY) = -1;
                   break;
                 }

           PC = 7; break;
         }

         case 7:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_HAS_TAG);

                 edgemap   = &UDF_a::EdgeMap_7;
                 context->set_udfedgemapsetting(2);

                 reduce    = &UDF_a::Reduce_7;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "v";
                 PC = 8;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_a INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(741L, "v", 38);
                     timer_.saveVSetCode(741L, "v =\n\tselect t\n\tfrom VS__2:f -(HAS_TAG>:x_5)- _:t\n\twhere  t.id == 1084\n\taccum  foreach s in f.@srcIdOnlyPropagAcc_2 do\n\t\t\n\t\t\n\t\t      // retrieve source attributes\n\t\t      uint s__id__gsqlpe__1 =  @@gsqlpe_src_attrib_install_map__1.get(s).id,\n     t.@isSrc += s__id__gsqlpe__1, @@isSrc += 1\n\t\t\n\t\tend\n\tpost-accum t.@isSrc += 1, @@isSrc += 2\npost-accum f.@srcIdOnlyPropagAcc_2.clear();");
                     timer_.addDependency(741L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__2_LASTSET));
                     timer_.start("v", 38, context->CalcActiveVertexCount(), 741L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_v_LASTSET) = 741L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_v_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_v_ORDERBY) = -1;
                   break;
                 }

           PC = 8; break;
         }

         case 8:
         {

                     ( (context->GlobalVariable_GetValue<MapAccum<VERTEX, gsqlpe_src_attrib_install_tup__1 > > (GV_GACC_gsqlpe_src_attrib_install_map__1_1)).clear());
;
;
                     PC = 9;
                     break;

           PC = 9; break;
         }

         case 9:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_a::Write_9_v;
                 context->set_udfprintsetting(1);

                 writer.WriteName("v");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 10;
                 break;

           PC = 10; break;
         }

         case 10:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@isSrc");
(( (context->GlobalVariable_GetValue<SetAccum<int64_t > > (GV_GACC_isSrc_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 11;
                 break;

           PC = 11; break;
         }

         case 11:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 5:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS__1_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__1_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 6:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS__2_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS__2_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 7:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_v_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_v_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Post = 1;
const int _schema_VTY_Person = 8;
const int _schema_VTY_Tag = 9;
const int _schema_ETY_CONTAINER_OF_REVERSE = 1;
const int _schema_ETY_HAS_TAG = 10;
const int _schema_ETY_LIKES = 21;
int _schema_VATT_Person_576037_id = -1;
int _schema_VATT_Person_576037_firstName = -1;
int _schema_VATT_Person_576037_lastName = -1;
int _schema_VATT_Tag_576037_id = -1;
int _schema_VATT_Tag_576037_name = -1;
int _schema_VATT_Tag_576037_url = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_a(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query a in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_a udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "a") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "a") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "a") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "a") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_a(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_a udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_a_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_a(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_a(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_a(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query a in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::ldbc_snb::UDF_a udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
