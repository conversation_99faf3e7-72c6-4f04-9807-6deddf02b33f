/********** query start ***************
CREATE DISTRIBUTED QUERY testVsetDist()
FOR GRAPH ldbc_snb SYNTAX v2 {
  // declare and print
  vset_o = {Person.*};
  PRINT vset_o;
  vset_t = {Person.*, Company.*};
  PRINT vset_t;
  vset_oo = {Person.*, University.*};
  PRINT vset_oo;
  // declare and select from
  res_o = SELECT src FROM vset_o : src;
  PRINT res_o;
  res_t = SELECT src FROM vset_t : src;
  PRINT res_t;
  res_oo = SELECT src FROM vset_oo : src;
  PRINT res_oo;
  // declare and select from where
  res_sfw_o = SELECT src FROM vset_o : src WHERE src.gender == "female";
  PRINT res_sfw_o;
  res_sfw_t = SELECT src FROM vset_t : src WHERE src.gender == "female";
  PRINT res_sfw_t;
  res_sfw_oo = SELECT src FROM vset_oo : src WHERE src.gender == "female";
  PRINT res_sfw_oo;
}
********** query end ***************/
#include "ldbc_snb-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "olgp/gpe/packagemanager.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace ldbc_snb {
    class GPR_testVsetDist {
      // These type aliases are for gsql package function
      using __GQUERY__pkg1_f2_629813_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*, const string&);
      using __GQUERY__pkg1_pkg2_f1_674634_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*, const string&);
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testVsetDist (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
        _schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_f2_629813_fptr = _pkglibs_map[std::string("libpkgudf_pkg1")]->GetGSQLFunction<__GQUERY__pkg1_f2_629813_fptr_t>(std::string("callfunc_f2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_f2_629813_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.f2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_f1_674634_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_f1_674634_fptr_t>(std::string("callfunc_f1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_f1_674634_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.f1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      //query calling query constructor
      GPR_testVsetDist (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
        _schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_f2_629813_fptr = _pkglibs_map[std::string("libpkgudf_pkg1")]->GetGSQLFunction<__GQUERY__pkg1_f2_629813_fptr_t>(std::string("callfunc_f2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_f2_629813_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.f2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_f1_674634_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_f1_674634_fptr_t>(std::string("callfunc_f1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_f1_674634_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.f1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      
      ///class destructor
      ~GPR_testVsetDist () {}
      
      ///vertex actions for write
      void Write_3(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int vset_o_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == vset_o_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_6(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int vset_t_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Company != -1 && _schema_VTY_Company == vset_t_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Company_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Company_576037_id, 0));
          }
          if (_schema_VATT_Company_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_name));
          }
          if (_schema_VATT_Company_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Person != -1 && _schema_VTY_Person == vset_t_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_9(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int vset_oo_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_University != -1 && _schema_VTY_University == vset_oo_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_University_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_University_576037_id, 0));
          }
          if (_schema_VATT_University_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_name));
          }
          if (_schema_VATT_University_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Person != -1 && _schema_VTY_Person == vset_oo_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_11(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_o_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_o_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_13(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_t_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Company != -1 && _schema_VTY_Company == res_t_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Company_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Company_576037_id, 0));
          }
          if (_schema_VATT_Company_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_name));
          }
          if (_schema_VATT_Company_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_t_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_15(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_oo_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_University != -1 && _schema_VTY_University == res_oo_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_University_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_University_576037_id, 0));
          }
          if (_schema_VATT_University_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_name));
          }
          if (_schema_VATT_University_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_oo_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_17(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_sfw_o_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_sfw_o_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_19(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_sfw_t_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_sfw_t_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_21(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_sfw_oo_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_sfw_oo_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      void VertexMap_2(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_5(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_name_string = string();
        bool src_name_string_flag = false;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Company) {
          if (srcVertex.GetAttr().IsValid()) {
            src_name_string = srcVertex.GetAttr().GetString(_schema_VATT_Company_576037_name);
            src_name_string_flag = true;
          }
        } else if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((((_schema_VTY_Company >= 0 && graphAPI->GetVertexType(src) == (unsigned) _schema_VTY_Company) && (src_name_string_flag && pkg1_f2_629813_fptr(_request.current_role_infos_.get(), pkg1_f2_629813_is_granted_cache, src_name_string))) || ((_schema_VTY_Person >= 0 && graphAPI->GetVertexType(src) == (unsigned) _schema_VTY_Person) && (src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_8(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((((_schema_VTY_Person >= 0 && graphAPI->GetVertexType(src) == (unsigned) _schema_VTY_Person) && (src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string))) || (_schema_VTY_Person >= 0 && graphAPI->GetVertexType(src) != (unsigned) _schema_VTY_Person)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_10(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_12(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_14(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_16(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_gender_string_flag && (src_gender_string == string("female"))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_18(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_gender_string_flag && (src_gender_string == string("female"))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_20(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_gender_string_flag && (src_gender_string == string("female"))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("testVsetDist")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_t_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_oo_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_oo_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_oo_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_t_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_t_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_t_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_o_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_o_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_o_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_t_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_o_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_o_vector_;
        {
          /*
          vset_o = { Person .* };
          */
          int64_t __vset_vset_o_size = __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_o_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value() -= __vset_vset_o_size;
          
        }
        {
          /*
          vset_o = select src from vset_o : src where ( pkg1.pkg2.f1 ( src.firstName ) );
          */
          int64_t __vset_vset_o_size = __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_o_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value() -= __vset_vset_o_size;
          __GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_.Value() = false;
          
        }
        {
          /*
          print vset_o;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_3_";
          if (__GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_3_, 
            {&__GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_3_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("vset_o");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_vset_o_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_vset_o_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("vset_o");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          vset_t = { Person .*, Company .* };
          */
          int64_t __vset_vset_t_size = __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_4_";
          if (!manager.RunCMD(
            action_4_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_t_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_4_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value() -= __vset_vset_t_size;
          
        }
        {
          /*
          vset_t = select src from vset_t : src where ( src.type == "Company" and pkg1.f2 ( src.name ) ) or ( src.type == "Person" and pkg1.pkg2.f1 ( src.firstName ) );
          */
          int64_t __vset_vset_t_size = __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_5_map";
          if (!manager.RunCMD(
            action_5_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_t_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_5_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value() -= __vset_vset_t_size;
          __GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_.Value() = false;
          
        }
        {
          /*
          print vset_t;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_6_";
          if (__GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_6_, 
            {&__GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_6_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("vset_t");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_vset_t_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_vset_t_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("vset_t");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          vset_oo = { Person .*, University .* };
          */
          int64_t __vset_vset_oo_size = __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_7_";
          if (!manager.RunCMD(
            action_7_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_7_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value() -= __vset_vset_oo_size;
          
        }
        {
          /*
          vset_oo = select src from vset_oo : src where ( src.type == "Person" and pkg1.pkg2.f1 ( src.firstName ) ) or ( src.type != "Person" );
          */
          int64_t __vset_vset_oo_size = __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_8_map";
          if (!manager.RunCMD(
            action_8_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_8_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value() -= __vset_vset_oo_size;
          __GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_.Value() = false;
          
        }
        {
          /*
          print vset_oo;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_9_";
          if (__GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_9_, 
            {&__GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_9_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("vset_oo");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_vset_oo_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_vset_oo_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("vset_oo");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_o = select src from vset_o : src;
          */
          int64_t __vset_res_o_size = __GQUERY_GV_Global_Variable__vset_res_o_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_10_map";
          if (!manager.RunCMD(
            action_10_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_o_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_10_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_o_SIZE__.Value() -= __vset_res_o_size;
          __GQUERY_GV_Global_Variable__vset_res_o_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_o;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_11_";
          if (__GQUERY_GV_Global_Variable__vset_res_o_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_11_, 
            {&__GQUERY_GV_Global_Variable__vset_res_o_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_11_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_o_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_o");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_o_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_o_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_o");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_t = select src from vset_t : src;
          */
          int64_t __vset_res_t_size = __GQUERY_GV_Global_Variable__vset_res_t_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_12_map";
          if (!manager.RunCMD(
            action_12_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_t_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_12_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_t_SIZE__.Value() -= __vset_res_t_size;
          __GQUERY_GV_Global_Variable__vset_res_t_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_t;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_13_";
          if (__GQUERY_GV_Global_Variable__vset_res_t_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_13_, 
            {&__GQUERY_GV_Global_Variable__vset_res_t_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_13_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_t_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_t");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_t_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_t_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_t");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_oo = select src from vset_oo : src;
          */
          int64_t __vset_res_oo_size = __GQUERY_GV_Global_Variable__vset_res_oo_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_14_map";
          if (!manager.RunCMD(
            action_14_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_oo_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_14_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_oo_SIZE__.Value() -= __vset_res_oo_size;
          __GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_oo;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_15_";
          if (__GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_15_, 
            {&__GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_15_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_oo");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_oo_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_oo_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_oo");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_sfw_o = select src from vset_o : src where src.gender == "female";
          */
          int64_t __vset_res_sfw_o_size = __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_16_map";
          if (!manager.RunCMD(
            action_16_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_16_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__.Value() -= __vset_res_sfw_o_size;
          __GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_sfw_o;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_17_";
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_17_, 
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_17_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_sfw_o");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_sfw_o_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_sfw_o_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_sfw_o");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_sfw_t = select src from vset_t : src where src.gender == "female";
          */
          int64_t __vset_res_sfw_t_size = __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_18_map";
          if (!manager.RunCMD(
            action_18_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_18_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__.Value() -= __vset_res_sfw_t_size;
          __GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_sfw_t;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_19_";
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_19_, 
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_19_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_sfw_t");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_sfw_t_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_sfw_t_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_sfw_t");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          res_sfw_oo = select src from vset_oo : src where src.gender == "female";
          */
          int64_t __vset_res_sfw_oo_size = __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_20_map";
          if (!manager.RunCMD(
            action_20_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_20_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__.Value() -= __vset_res_sfw_oo_size;
          __GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_.Value() = false;
          
        }
        {
          /*
          print res_sfw_oo;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "master starts action_21_";
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_21_, 
            {&__GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "action_21_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("res_sfw_oo");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_res_sfw_oo_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_sfw_oo_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("res_sfw_oo");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vset_t = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vset_oo = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_oo = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_t = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_o = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sfw_t = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sfw_oo = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sfw_o = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vset_o = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_t_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_oo_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_oo_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_oo_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_t_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_t_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_t_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_o_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_o_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_o_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_t_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_oo_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sfw_o_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vset_o_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg5(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request));
          gpr::GPR_Container* __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg8(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request));
          gpr::GPR_Container* __GQUERY__delta_container10 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg10(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container10, &_request));
          gpr::GPR_Container* __GQUERY__delta_container12 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg12(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container12, &_request));
          gpr::GPR_Container* __GQUERY__delta_container14 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg14(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container14, &_request));
          gpr::GPR_Container* __GQUERY__delta_container16 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg16(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container16, &_request));
          gpr::GPR_Container* __GQUERY__delta_container18 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg18(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container18, &_request));
          gpr::GPR_Container* __GQUERY__delta_container20 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg20(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container20, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_o_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output;
                //run action
                __GQUERY__vSet_vset_o->SetAllActiveFlag(false);
                __GQUERY__vSet_vset_o->SetActiveFlagByType(_schema_VTY_Person, true);
                __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value() = __GQUERY__vSet_vset_o->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output.Value() = __GQUERY__vSet_vset_o->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_o_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_2, this),//action function
                  __GQUERY__vSet_vset_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_vset_o, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_vset_o_SIZE__.Value() = __GQUERY__vSet_vset_o->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output.Value() = __GQUERY__vSet_vset_o->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_o_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_3_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_3_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_3, this),//action function
                  __GQUERY__vSet_vset_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_vset_o_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_3_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_4_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_4_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_t_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output;
                //run action
                __GQUERY__vSet_vset_t->SetAllActiveFlag(false);
                __GQUERY__vSet_vset_t->SetActiveFlagByType(_schema_VTY_Person, true);
                __GQUERY__vSet_vset_t->SetActiveFlagByType(_schema_VTY_Company, true);
                __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value() = __GQUERY__vSet_vset_t->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_4_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output.Value() = __GQUERY__vSet_vset_t->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_5_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_5_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_t_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg5 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_5, this),//action function
                  __GQUERY__vSet_vset_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_vset_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_vset_t_SIZE__.Value() = __GQUERY__vSet_vset_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output.Value() = __GQUERY__vSet_vset_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_5_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_t_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_6_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_6_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_6, this),//action function
                  __GQUERY__vSet_vset_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_vset_t_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_6_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_7_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_7_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output;
                //run action
                __GQUERY__vSet_vset_oo->SetAllActiveFlag(false);
                __GQUERY__vSet_vset_oo->SetActiveFlagByType(_schema_VTY_Person, true);
                __GQUERY__vSet_vset_oo->SetActiveFlagByType(_schema_VTY_University, true);
                __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value() = __GQUERY__vSet_vset_oo->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_7_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output.Value() = __GQUERY__vSet_vset_oo->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_8_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_8_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__) __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg8 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_8, this),//action function
                  __GQUERY__vSet_vset_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_vset_oo, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE__.Value() = __GQUERY__vSet_vset_oo->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output.Value() = __GQUERY__vSet_vset_oo->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_8_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vset_oo_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_9_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_9_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_9, this),//action function
                  __GQUERY__vSet_vset_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_vset_oo_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_9_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_10_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_10_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_o_SIZE__) __GQUERY_GV_Global_Variable__vset_res_o_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container10 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg10 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container10, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_10, this),//action function
                  __GQUERY__vSet_vset_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_o_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_o, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_o_SIZE__.Value() = __GQUERY__vSet_res_o->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_o_SIZE___output.Value() = __GQUERY__vSet_res_o->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_10_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_o_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_o_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_11_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_11_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_o_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_11, this),//action function
                  __GQUERY__vSet_res_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_o_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_11_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_12_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_12_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_t_SIZE__) __GQUERY_GV_Global_Variable__vset_res_t_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container12 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg12 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container12, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_12, this),//action function
                  __GQUERY__vSet_vset_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_t_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_t_SIZE__.Value() = __GQUERY__vSet_res_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_t_SIZE___output.Value() = __GQUERY__vSet_res_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_12_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_t_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_t_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_13_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_13_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_t_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_13, this),//action function
                  __GQUERY__vSet_res_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_t_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_13_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_14_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_14_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_oo_SIZE__) __GQUERY_GV_Global_Variable__vset_res_oo_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container14 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg14 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container14, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_14, this),//action function
                  __GQUERY__vSet_vset_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_oo_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_oo, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_oo_SIZE__.Value() = __GQUERY__vSet_res_oo->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_oo_SIZE___output.Value() = __GQUERY__vSet_res_oo->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_14_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_oo_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_15_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_15_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_15, this),//action function
                  __GQUERY__vSet_res_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_oo_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_15_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_16_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_16_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__) __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container16 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg16 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container16, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_16, this),//action function
                  __GQUERY__vSet_vset_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<string> hint_VATT_Person_gender0 = {string("female")};
                    if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_sfw_o, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE__.Value() = __GQUERY__vSet_res_sfw_o->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE___output.Value() = __GQUERY__vSet_res_sfw_o->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_16_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_sfw_o_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_17_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_17_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_17, this),//action function
                  __GQUERY__vSet_res_sfw_o.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_o_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_17_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_18_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_18_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__) __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container18 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg18 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container18, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_18, this),//action function
                  __GQUERY__vSet_vset_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<string> hint_VATT_Person_gender0 = {string("female")};
                    if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_sfw_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE__.Value() = __GQUERY__vSet_res_sfw_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE___output.Value() = __GQUERY__vSet_res_sfw_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_18_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_sfw_t_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_19_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_19_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_19, this),//action function
                  __GQUERY__vSet_res_sfw_t.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_t_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_19_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              else if (worker->GetAction() == action_20_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_20_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__) __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container20 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg20 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container20, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::VertexMap_20, this),//action function
                  __GQUERY__vSet_vset_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<string> hint_VATT_Person_gender0 = {string("female")};
                    if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_res_sfw_oo, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE__.Value() = __GQUERY__vSet_res_sfw_oo->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE___output.Value() = __GQUERY__vSet_res_sfw_oo->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_20_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_sfw_oo_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_21_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker start action_21_";
                //deserialize input gvs
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testVsetDist::Write_21, this),//action function
                  __GQUERY__vSet_res_sfw_oo.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable__vset_res_sfw_oo_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|" << "worker finish action_21_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      inline static SetAccum<string > HF_get_attr_setstring_V(V_ATTR* attr, int index) {
        SetAccum<string >  result;
        auto reader = attr->GetStringValueReader(index);
        while (reader.MoveNext()) {
          result += reader.value_;
        }
        return result;
      }
      
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_Company = 2;
      const int _schema_VTY_University = 3;
      const int _schema_VTY_Person = 8;
      int _schema_VATT_Company_576037_id = -1;
      int _schema_VATT_Company_576037_name = -1;
      int _schema_VATT_Company_576037_url = -1;
      int _schema_VATT_University_576037_id = -1;
      int _schema_VATT_University_576037_name = -1;
      int _schema_VATT_University_576037_url = -1;
      int _schema_VATT_Person_576037_id = -1;
      int _schema_VATT_Person_576037_firstName = -1;
      int _schema_VATT_Person_576037_lastName = -1;
      int _schema_VATT_Person_576037_gender = -1;
      int _schema_VATT_Person_576037_birthday = -1;
      int _schema_VATT_Person_576037_creationDate = -1;
      int _schema_VATT_Person_576037_locationIP = -1;
      int _schema_VATT_Person_576037_browserUsed = -1;
      int _schema_VATT_Person_576037_speaks = -1;
      int _schema_VATT_Person_576037_email = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_ = "action_3_";
      const std::string action_4_ = "action_4_";
      const std::string action_5_map = "action_5_map";
      const std::string action_5_reduce = "action_5_reduce";
      const std::string action_5_post = "action_5_post";
      const std::string action_6_ = "action_6_";
      const std::string action_7_ = "action_7_";
      const std::string action_8_map = "action_8_map";
      const std::string action_8_reduce = "action_8_reduce";
      const std::string action_8_post = "action_8_post";
      const std::string action_9_ = "action_9_";
      const std::string action_10_map = "action_10_map";
      const std::string action_10_reduce = "action_10_reduce";
      const std::string action_10_post = "action_10_post";
      const std::string action_11_ = "action_11_";
      const std::string action_12_map = "action_12_map";
      const std::string action_12_reduce = "action_12_reduce";
      const std::string action_12_post = "action_12_post";
      const std::string action_13_ = "action_13_";
      const std::string action_14_map = "action_14_map";
      const std::string action_14_reduce = "action_14_reduce";
      const std::string action_14_post = "action_14_post";
      const std::string action_15_ = "action_15_";
      const std::string action_16_map = "action_16_map";
      const std::string action_16_reduce = "action_16_reduce";
      const std::string action_16_post = "action_16_post";
      const std::string action_17_ = "action_17_";
      const std::string action_18_map = "action_18_map";
      const std::string action_18_reduce = "action_18_reduce";
      const std::string action_18_post = "action_18_post";
      const std::string action_19_ = "action_19_";
      const std::string action_20_map = "action_20_map";
      const std::string action_20_reduce = "action_20_reduce";
      const std::string action_20_post = "action_20_post";
      const std::string action_21_ = "action_21_";
      ghash_map<std::string, PKGLib*> _pkglibs_map;
      __GQUERY__pkg1_f2_629813_fptr_t pkg1_f2_629813_fptr = nullptr;
      gvector<bool>* pkg1_f2_629813_is_granted_cache = nullptr;
      __GQUERY__pkg1_pkg2_f1_674634_fptr_t pkg1_pkg2_f1_674634_fptr = nullptr;
      gvector<bool>* pkg1_pkg2_f1_674634_is_granted_cache = nullptr;
      public:
        
        ///return vars
      };//end class GPR_testVsetDist
    bool call_testVsetDist(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::ldbc_snb::GPR_testVsetDist gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testVsetDist_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::ldbc_snb::GPR_testVsetDist gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testVsetDist(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::ldbc_snb::GPR_testVsetDist gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testVsetDist") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testVsetDist(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::ldbc_snb::GPR_testVsetDist gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testVsetDist(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::ldbc_snb::GPR_testVsetDist gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace ldbc_snb
}//end namespace UDIMPL
