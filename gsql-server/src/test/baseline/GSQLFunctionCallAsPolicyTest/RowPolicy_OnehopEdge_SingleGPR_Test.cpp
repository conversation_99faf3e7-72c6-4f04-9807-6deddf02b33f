/********** query start ***************
CREATE QUERY testOnehopEdge()
FOR GRAPH ldbc_snb SYNTAX v2 {
  // concrete vertex and edge type, src/tgt may or may not be enforced by policy 
  res_se_te = SELECT src
              FROM Person:src - (WORK_AT>) - Company:tgt;
  PRINT res_se_te;
  res_se_te_w = SELECT src
                FROM Person:src - (WORK_AT>) - Company:tgt
                WHERE src.gender == "female"
                ;
  PRINT res_se_te_w;
  res_se_tn = SELECT src
              FROM Person:src - (STUDY_AT>) - University:tgt;
  PRINT res_se_tn;
  res_se_tn_w = SELECT src
                FROM Person:src - (STUDY_AT>) - University:tgt
                WHERE src.gender == "female"
                ;
  PRINT res_se_tn_w;
  res_sn_te = SELECT src
              FROM Forum:src - (HAS_MEMBER>) - Person:tgt;
  PRINT res_sn_te;
  res_sn_te_w = SELECT src
                FROM Forum:src - (HAS_MEMBER>) - Person:tgt
                WHERE tgt.gender == "female"
                ;
  PRINT res_sn_te_w;
}
********** query end ***************/
#include "ldbc_snb-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "olgp/gpe/packagemanager.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace ldbc_snb {
    class GPR_testOnehopEdge {
      // These type aliases are for gsql package function
      using __GQUERY__pkg1_f2_629813_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*, const string&);
      using __GQUERY__pkg1_pkg2_f1_674634_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*, const string&);
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testOnehopEdge (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
        _schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
        _schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_f2_629813_fptr = _pkglibs_map[std::string("libpkgudf_pkg1")]->GetGSQLFunction<__GQUERY__pkg1_f2_629813_fptr_t>(std::string("callfunc_f2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_f2_629813_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.f2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_f1_674634_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_f1_674634_fptr_t>(std::string("callfunc_f1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_f1_674634_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.f1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      //query calling query constructor
      GPR_testOnehopEdge (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
        _schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
        _schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_f2_629813_fptr = _pkglibs_map[std::string("libpkgudf_pkg1")]->GetGSQLFunction<__GQUERY__pkg1_f2_629813_fptr_t>(std::string("callfunc_f2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_f2_629813_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.f2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_f1_674634_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_f1_674634_fptr_t>(std::string("callfunc_f1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_f1_674634_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.f1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      
      ///class destructor
      ~GPR_testOnehopEdge () {}
      
      ///vertex actions for write
      void Write_3(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_se_te_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_se_te_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_6(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_se_te_w_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_se_te_w_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_9(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_se_tn_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_se_tn_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_12(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_se_tn_w_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Person != -1 && _schema_VTY_Person == res_se_tn_w_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_15(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_sn_te_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Forum != -1 && _schema_VTY_Forum == res_sn_te_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Forum_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Forum_576037_id, 0));
          }
          if (_schema_VATT_Forum_576037_title != -1) {
            writer.WriteNameString("title");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Forum_576037_title));
          }
          if (_schema_VATT_Forum_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Forum_576037_creationDate, 0)));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      void Write_18(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(0);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int res_sn_te_w_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Forum != -1 && _schema_VTY_Forum == res_sn_te_w_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Forum_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Forum_576037_id, 0));
          }
          if (_schema_VATT_Forum_576037_title != -1) {
            writer.WriteNameString("title");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Forum_576037_title));
          }
          if (_schema_VATT_Forum_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Forum_576037_creationDate, 0)));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      void EdgeMap_2(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_Company >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_Company)
          return;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        string tgt_name_string = string();
        bool tgt_name_string_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_Company) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_Company_576037_name);
            tgt_name_string_flag = true;
          }
        }
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!(((src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)) && (tgt_name_string_flag && pkg1_f2_629813_fptr(_request.current_role_infos_.get(), pkg1_f2_629813_is_granted_cache, tgt_name_string))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      void EdgeMap_5(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_Company >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_Company)
          return;
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        string tgt_name_string = string();
        bool tgt_name_string_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_Company) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_Company_576037_name);
            tgt_name_string_flag = true;
          }
        }
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!(((src_gender_string_flag && (src_gender_string == string("female"))) && ((src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)) && (tgt_name_string_flag && pkg1_f2_629813_fptr(_request.current_role_infos_.get(), pkg1_f2_629813_is_granted_cache, tgt_name_string)))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_5(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      void EdgeMap_8(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_University >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_University)
          return;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!((src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_8(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      void EdgeMap_11(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_University >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_University)
          return;
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!(((src_gender_string_flag && (src_gender_string == string("female"))) && (src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string))))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_11(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      void EdgeMap_14(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_Person >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_Person)
          return;
        string tgt_firstName_string = string();
        bool tgt_firstName_string_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_Person) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_firstName_string = tgtVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            tgt_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!((tgt_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, tgt_firstName_string)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_14(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      void EdgeMap_17(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_Forum >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_Forum)
          return;
        string src_gender_string = string();
        bool src_gender_string_flag = false;
        string src_firstName_string = string();
        bool src_firstName_string_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_Person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
            src_gender_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
            src_firstName_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!(((src_gender_string_flag && (src_gender_string == string("female"))) && (src_firstName_string_flag && pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string))))) return;
        // prepare message
        DefaultDelta tgt_delta = DefaultDelta();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        context.Write(tgt, tgt_delta, 0);
        context.Activate(tgt, 0);
      }
      void Reduce_17(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          singleGprMainFlow();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void singleGprMainFlow () {
        try {
          gshared_ptr<gpr::GPR> gpr = _serviceapi->CreateGPR(&_request, true);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_te = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_te_w = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_tn = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_tn_w = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sn_te = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sn_te_w = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_;
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_se_te_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_se_te_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_se_te_w_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_se_te_w_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_se_tn_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_se_tn_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_se_tn_w_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_se_tn_w_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sn_te_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sn_te_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_sn_te_w_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_sn_te_w_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg5(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request));
          gpr::GPR_Container* __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg8(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request));
          gpr::GPR_Container* __GQUERY__delta_container11 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg11(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container11, &_request));
          gpr::GPR_Container* __GQUERY__delta_container14 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg14(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container14, &_request));
          gpr::GPR_Container* __GQUERY__delta_container17 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg17(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container17, &_request));
          
          {
            /*
            res_se_te = select src from Person : src - ( WORK_AT > : x ) - Company : tgt where ( ( pkg1.pkg2.f1 ( src.firstName ) ) ) and ( ( pkg1.f2 ( tgt.name ) ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_1_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_2_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_WORK_AT);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_2, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container2},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_te_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              V_VALUE src_val;
              
              Condition result = Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false).AND(Condition(Condition::UNDEFINE));
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              Condition result = Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false).AND(Condition(Condition::UNDEFINE));
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              string tgt_name_string = string();
              bool tgt_name_string_flag = false;
              
              //get tgt's attribute
              int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              if (tgt_typeIDVar == _schema_VTY_Company) {
                if (tgtVertex.GetAttr().IsValid()) {
                  tgt_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_Company_576037_name);
                  tgt_name_string_flag = true;
                }
              }
              Condition result = Condition(Condition::UNDEFINE).AND(Condition(tgt_name_string_flag? Condition(pkg1_f2_629813_fptr(_request.current_role_infos_.get(), pkg1_f2_629813_is_granted_cache, tgt_name_string)): false));
              return result.toBool();
            });
            
            filter.addTgtAttribute(
              _schema_VTY_Company, {(unsigned)_schema_VATT_Company_576037_name}
            );
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_te_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_2, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container2},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_se_te_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_te_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, true);
            std::swap(__GQUERY__vSet_res_se_te, __GQUERY__vSet_res_se_te_tmp);
            __GQUERY_GV_Global_Variable__vset_res_se_te_SIZE__.Value() = __GQUERY__vSet_res_se_te->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_2_map";
            __GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_se_te;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_3_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_3, this),//action function
                __GQUERY__vSet_res_se_te.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_se_te_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_se_te");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_se_te_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_se_te_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_se_te");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            res_se_te_w = select src from Person : src - ( WORK_AT > : x ) - Company : tgt where ( src.gender == "female" ) and ( ( ( pkg1.pkg2.f1 ( src.firstName ) ) ) and ( ( pkg1.f2 ( tgt.name ) ) ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_4_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_5_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg5 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_WORK_AT);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_5, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container5},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_te_w_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            {
              gvector<gvector<gindex::IndexPredicate>> hints_edgeAction;
              {
                gvector<gindex::IndexPredicate> hint_edgeAction;
                gvector<string> hint_VATT_Person_gender0 = {string("female")};
                if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                  hint_edgeAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                }
                hints_edgeAction.push_back(std::move(hint_edgeAction));
              }
              gindex::IndexHint indexHint_edgeAction(std::move(hints_edgeAction));
              edgeAction->SetSrcIndexHint(std::move(indexHint_edgeAction));
            }
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              V_VALUE src_val;
              
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false).AND(Condition(Condition::UNDEFINE)));
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false).AND(Condition(Condition::UNDEFINE)));
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              string tgt_name_string = string();
              bool tgt_name_string_flag = false;
              
              //get tgt's attribute
              int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              if (tgt_typeIDVar == _schema_VTY_Company) {
                if (tgtVertex.GetAttr().IsValid()) {
                  tgt_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_Company_576037_name);
                  tgt_name_string_flag = true;
                }
              }
              Condition result = Condition(Condition::UNDEFINE).AND(Condition(Condition::UNDEFINE).AND(Condition(tgt_name_string_flag? Condition(pkg1_f2_629813_fptr(_request.current_role_infos_.get(), pkg1_f2_629813_is_granted_cache, tgt_name_string)): false)));
              return result.toBool();
            });
            
            filter.addTgtAttribute(
              _schema_VTY_Company, {(unsigned)_schema_VATT_Company_576037_name}
            );
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_te_w_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_5, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container5},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_se_te_w_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_te_w_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, true);
            std::swap(__GQUERY__vSet_res_se_te_w, __GQUERY__vSet_res_se_te_w_tmp);
            __GQUERY_GV_Global_Variable__vset_res_se_te_w_SIZE__.Value() = __GQUERY__vSet_res_se_te_w->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_5_map";
            __GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_se_te_w;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_6_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_6, this),//action function
                __GQUERY__vSet_res_se_te_w.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_se_te_w_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_se_te_w");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_se_te_w_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_se_te_w_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_se_te_w");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            res_se_tn = select src from Person : src - ( STUDY_AT > : x ) - University : tgt where ( pkg1.pkg2.f1 ( src.firstName ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_7_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_8_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg8 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_STUDY_AT);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_8, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container8},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_tn_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              V_VALUE src_val;
              
              Condition result = Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false);
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              Condition result = Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false);
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              Condition result = Condition(Condition::UNDEFINE);
              return result.toBool();
            });
            
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_tn_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_8, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container8},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_se_tn_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_tn_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, false);
            std::swap(__GQUERY__vSet_res_se_tn, __GQUERY__vSet_res_se_tn_tmp);
            __GQUERY_GV_Global_Variable__vset_res_se_tn_SIZE__.Value() = __GQUERY__vSet_res_se_tn->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_8_map";
            __GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_se_tn;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_9_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_9, this),//action function
                __GQUERY__vSet_res_se_tn.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_se_tn_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_se_tn");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_se_tn_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_se_tn_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_se_tn");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            res_se_tn_w = select src from Person : src - ( STUDY_AT > : x ) - University : tgt where ( src.gender == "female" ) and ( ( pkg1.pkg2.f1 ( src.firstName ) ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_10_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_11_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container11 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg11 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container11, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_STUDY_AT);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_11, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container11},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_tn_w_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            {
              gvector<gvector<gindex::IndexPredicate>> hints_edgeAction;
              {
                gvector<gindex::IndexPredicate> hint_edgeAction;
                gvector<string> hint_VATT_Person_gender0 = {string("female")};
                if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                  hint_edgeAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                }
                hints_edgeAction.push_back(std::move(hint_edgeAction));
              }
              gindex::IndexHint indexHint_edgeAction(std::move(hints_edgeAction));
              edgeAction->SetSrcIndexHint(std::move(indexHint_edgeAction));
            }
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              V_VALUE src_val;
              
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false));
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false));
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              Condition result = Condition(Condition::UNDEFINE).AND(Condition(Condition::UNDEFINE));
              return result.toBool();
            });
            
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_se_tn_w_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_11, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container11},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_se_tn_w_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_se_tn_w_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, false);
            std::swap(__GQUERY__vSet_res_se_tn_w, __GQUERY__vSet_res_se_tn_w_tmp);
            __GQUERY_GV_Global_Variable__vset_res_se_tn_w_SIZE__.Value() = __GQUERY__vSet_res_se_tn_w->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_11_map";
            __GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_se_tn_w;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_12_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_12, this),//action function
                __GQUERY__vSet_res_se_tn_w.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_se_tn_w_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_se_tn_w");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_se_tn_w_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_se_tn_w_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_se_tn_w");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            res_sn_te = select src from Forum : src - ( HAS_MEMBER > : x ) - Person : tgt where ( pkg1.pkg2.f1 ( tgt.firstName ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_13_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Forum, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_14_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container14 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg14 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container14, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_HAS_MEMBER);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_14, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container14},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_sn_te_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              
              V_VALUE src_val;
              
              Condition result = Condition(Condition::UNDEFINE);
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              Condition result = Condition(Condition::UNDEFINE);
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              string tgt_firstName_string = string();
              bool tgt_firstName_string_flag = false;
              
              //get tgt's attribute
              int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              if (tgt_typeIDVar == _schema_VTY_Person) {
                if (tgtVertex.GetAttr().IsValid()) {
                  tgt_firstName_string = tgtVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  tgt_firstName_string_flag = true;
                }
              }
              Condition result = Condition(tgt_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, tgt_firstName_string)): false);
              return result.toBool();
            });
            
            filter.addTgtAttribute(
              _schema_VTY_Person, {(unsigned)_schema_VATT_Person_576037_firstName}
            );
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sn_te_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_14, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container14},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_sn_te_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_sn_te_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, false, true);
            std::swap(__GQUERY__vSet_res_sn_te, __GQUERY__vSet_res_sn_te_tmp);
            __GQUERY_GV_Global_Variable__vset_res_sn_te_SIZE__.Value() = __GQUERY__vSet_res_sn_te->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_14_map";
            __GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_sn_te;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_15_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_15, this),//action function
                __GQUERY__vSet_res_sn_te.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_sn_te_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_sn_te");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_sn_te_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_sn_te_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_sn_te");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            res_sn_te_w = select src from Person : tgt - ( HAS_MEMBER_REVERSE > : x ) - Forum : src where ( tgt.gender == "female" ) and ( ( pkg1.pkg2.f1 ( tgt.firstName ) ) );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_16_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_17_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container17 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg17 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container17, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_HAS_MEMBER_REVERSE);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::EdgeMap_17, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container17},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_sn_te_w_SIZE__}//output gv
            );
            edgeAction->AddInputObject(this);
            
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            {
              gvector<gvector<gindex::IndexPredicate>> hints_edgeAction;
              {
                gvector<gindex::IndexPredicate> hint_edgeAction;
                gvector<string> hint_VATT_Person_gender0 = {string("female")};
                if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_gender != -1) {
                  hint_edgeAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_string(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_gender, hint_VATT_Person_gender0));
                }
                hints_edgeAction.push_back(std::move(hint_edgeAction));
              }
              gindex::IndexHint indexHint_edgeAction(std::move(hints_edgeAction));
              edgeAction->SetSrcIndexHint(std::move(indexHint_edgeAction));
            }
            gpr::RemoteTopology::Filter filter;
            filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              auto graphAPI = context.GraphAPI();
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              V_VALUE src_val;
              
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false));
              return result.toBool();
            });
            
            filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
              VERTEX src = srcVertex.vid();
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE src_val;
              
              string src_gender_string = string();
              bool src_gender_string_flag = false;
              string src_firstName_string = string();
              bool src_firstName_string_flag = false;
              
              //get src's attribute
              int src_typeIDVar = graphAPI->GetVertexType(src);
              if (src_typeIDVar == _schema_VTY_Person) {
                if (srcVertex.GetAttr().IsValid()) {
                  src_gender_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender);
                  src_gender_string_flag = true;
                }
                if (srcVertex.GetAttr().IsValid()) {
                  src_firstName_string = srcVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName);
                  src_firstName_string_flag = true;
                }
              }
              Condition result = Condition(src_gender_string_flag? Condition((src_gender_string == string("female"))): false).AND(Condition(src_firstName_string_flag? Condition(pkg1_pkg2_f1_674634_fptr(_request.current_role_infos_.get(), pkg1_pkg2_f1_674634_is_granted_cache, src_firstName_string)): false));
              return result.toBool();
            });
            
            filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
              VERTEX tgt = tgtVertex.vid();
              auto graphAPI = context.GraphAPI();
              V_VALUE tgt_val;
              
              Condition result = Condition(Condition::UNDEFINE).AND(Condition(Condition::UNDEFINE));
              return result.toBool();
            });
            
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res_sn_te_w_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Reduce_17, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container17},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_res_sn_te_w_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_res_sn_te_w_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, false);
            std::swap(__GQUERY__vSet_res_sn_te_w, __GQUERY__vSet_res_sn_te_w_tmp);
            __GQUERY_GV_Global_Variable__vset_res_sn_te_w_SIZE__.Value() = __GQUERY__vSet_res_sn_te_w->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "executor finish action_17_map";
            __GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_.Value() = false;
            
          }
          {
            /*
            print res_sn_te_w;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|" << "starts action_18_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testOnehopEdge::Write_18, this),//action function
                __GQUERY__vSet_res_sn_te_w.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_res_sn_te_w_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("res_sn_te_w");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_res_sn_te_w_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_res_sn_te_w_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("res_sn_te_w");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end singleGprMainFlow
      
      ///helper function
      inline static SetAccum<string > HF_get_attr_setstring_V(V_ATTR* attr, int index) {
        SetAccum<string >  result;
        auto reader = attr->GetStringValueReader(index);
        while (reader.MoveNext()) {
          result += reader.value_;
        }
        return result;
      }
      
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_Company = 2;
      const int _schema_VTY_University = 3;
      const int _schema_VTY_Forum = 7;
      const int _schema_VTY_Person = 8;
      const int _schema_ETY_HAS_MEMBER = 6;
      const int _schema_ETY_HAS_MEMBER_REVERSE = 7;
      const int _schema_ETY_STUDY_AT = 25;
      const int _schema_ETY_WORK_AT = 27;
      int _schema_VATT_Company_576037_name = -1;
      int _schema_VATT_Forum_576037_id = -1;
      int _schema_VATT_Forum_576037_title = -1;
      int _schema_VATT_Forum_576037_creationDate = -1;
      int _schema_VATT_Person_576037_id = -1;
      int _schema_VATT_Person_576037_firstName = -1;
      int _schema_VATT_Person_576037_lastName = -1;
      int _schema_VATT_Person_576037_gender = -1;
      int _schema_VATT_Person_576037_birthday = -1;
      int _schema_VATT_Person_576037_creationDate = -1;
      int _schema_VATT_Person_576037_locationIP = -1;
      int _schema_VATT_Person_576037_browserUsed = -1;
      int _schema_VATT_Person_576037_speaks = -1;
      int _schema_VATT_Person_576037_email = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_ = "action_3_";
      const std::string action_4_ = "action_4_";
      const std::string action_5_map = "action_5_map";
      const std::string action_5_reduce = "action_5_reduce";
      const std::string action_5_post = "action_5_post";
      const std::string action_6_ = "action_6_";
      const std::string action_7_ = "action_7_";
      const std::string action_8_map = "action_8_map";
      const std::string action_8_reduce = "action_8_reduce";
      const std::string action_8_post = "action_8_post";
      const std::string action_9_ = "action_9_";
      const std::string action_10_ = "action_10_";
      const std::string action_11_map = "action_11_map";
      const std::string action_11_reduce = "action_11_reduce";
      const std::string action_11_post = "action_11_post";
      const std::string action_12_ = "action_12_";
      const std::string action_13_ = "action_13_";
      const std::string action_14_map = "action_14_map";
      const std::string action_14_reduce = "action_14_reduce";
      const std::string action_14_post = "action_14_post";
      const std::string action_15_ = "action_15_";
      const std::string action_16_ = "action_16_";
      const std::string action_17_map = "action_17_map";
      const std::string action_17_reduce = "action_17_reduce";
      const std::string action_17_post = "action_17_post";
      const std::string action_18_ = "action_18_";
      ghash_map<std::string, PKGLib*> _pkglibs_map;
      __GQUERY__pkg1_f2_629813_fptr_t pkg1_f2_629813_fptr = nullptr;
      gvector<bool>* pkg1_f2_629813_is_granted_cache = nullptr;
      __GQUERY__pkg1_pkg2_f1_674634_fptr_t pkg1_pkg2_f1_674634_fptr = nullptr;
      gvector<bool>* pkg1_pkg2_f1_674634_is_granted_cache = nullptr;
      public:
        
        ///return vars
      };//end class GPR_testOnehopEdge
    bool call_testOnehopEdge(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::ldbc_snb::GPR_testOnehopEdge gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_testOnehopEdge_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      //single gpr doing nothing
    }
    bool call_testOnehopEdge(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::ldbc_snb::GPR_testOnehopEdge gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testOnehopEdge") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testOnehopEdge(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::ldbc_snb::GPR_testOnehopEdge gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testOnehopEdge(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::ldbc_snb::GPR_testOnehopEdge gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace ldbc_snb
}//end namespace UDIMPL
