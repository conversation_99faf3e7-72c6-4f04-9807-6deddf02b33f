/********** query start ***************
CREATE QUERY sameParameterNamame(/* Parameters here */) FOR GRAPH empty_graph {
  GroupByAccum<int d, string s, GroupByAccum<int i, SumAccum<int> s> group> @@group1;
  GroupByAccum<int d, string s, GroupByAccum<int i, AvgAccum s> group> @@group2;
  GroupByAccum<int d, AvgAccum s> @@group3;
  GroupByAccum<int d, SumAccum<int> s> @@group4;

  @@group1 += (2, "hello" -> (2 -> 2));
  @@group2 += (2, "hello" -> (2 -> 2));
  @@group3 += (2 -> 1);
  @@group4 += (2 -> 1);

  print @@group1;
  print @@group2;
  print @@group3;
  print @@group4;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace empty_graph{ 
class UDF_sameParameterNamame :public gpelib4::BaseUDF {
template<typename i_t, class s_t>
struct GroupByAccum_1_int_i_1_SumAccum_int_s {
  MapAccum<int64_t, SumAccum<int64_t > >map;

  GroupByAccum_1_int_i_1_SumAccum_int_s() { }

  GroupByAccum_1_int_i_1_SumAccum_int_s(int64_t base_0, SumAccum<int64_t >  accum_0) {
    map = MapAccum<int64_t, SumAccum<int64_t > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_i_1_SumAccum_int_s& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_i_1_SumAccum_int_s operator+ (GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) {
    GroupByAccum_1_int_i_1_SumAccum_int_s newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("i");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const SumAccum<int64_t > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_i_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename d_t, typename s_t, class group_t>
struct GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group {
struct baseTuple {
  d_t d;
  s_t s;

  baseTuple() {
    d = d_t();
    s = s_t();
  }

  baseTuple(d_t d_, s_t s_){
    d = d_;
    s = s_;
  }
  operator std::tuple<d_t, s_t>() const {
    return std::make_tuple(d,s);
  }

  baseTuple(const std::tuple<d_t, s_t>& __GQUERY__other__576037) {
    d = std::get<0>(__GQUERY__other__576037);
    s = std::get<1>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const baseTuple& m) {
    os<<"[";
    os<<"d "<<m.d<<"|";
    os<<"s "<<m.s<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const baseTuple& m) {
    os<<"[";
    os<<"d "<<m.d<<"|";
    os<<"s "<<m.s<<"]";
      return os ;
  }


  bool operator==(baseTuple const &__GQUERY__other__576037) const {
    return
      d == __GQUERY__other__576037.d &&
      s == __GQUERY__other__576037.s;
  }


  baseTuple& operator+=( const baseTuple& __GQUERY__other__576037) {
      d += __GQUERY__other__576037.d;
      s += __GQUERY__other__576037.s;
    return *this;
  }

  friend std::size_t hash_value(const baseTuple& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.d);
    boost::hash_combine(seed, other.s);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("d");
      (d).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("s");
      (s).json_printer(writer, _request, graphAPI, true);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(baseTuple const &__GQUERY__other__576037) const {
      if (d < __GQUERY__other__576037.d) return true;
      if (d > __GQUERY__other__576037.d) return false;
      if (s < __GQUERY__other__576037.s) return true;
      if (s > __GQUERY__other__576037.s) return false;
      return false;
  }


  bool operator>(baseTuple const &__GQUERY__other__576037) const {
      if (d > __GQUERY__other__576037.d) return true;
      if (d < __GQUERY__other__576037.d) return false;
      if (s > __GQUERY__other__576037.s) return true;
      if (s < __GQUERY__other__576037.s) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (d, s);
   }

};

  MapAccum<baseTuple, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > >map;

  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group() { }

  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group(d_t base_0, s_t base_1, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  accum_0) {
    map = MapAccum<baseTuple, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > > (baseTuple(base_0, base_1), accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group operator+ (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) {
    GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("d");
      json_printer_util(it->first.d, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->first.s, writer, _request, graphAPI, verbose);
      writer.WriteName("group");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > & get (d_t base_0, s_t base_1) {
    return map.get(baseTuple(base_0, base_1));
  }
  int size () const {
    return map.size();
  }
  bool containskey (d_t base_0, s_t base_1) {
    return map.containskey(baseTuple(base_0, base_1));
  }
  void clear () {
    map.clear();
  }
  void remove (d_t base_0, s_t base_1) {
    map.remove(baseTuple(base_0, base_1));
  }
  void remove (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename i_t, class s_t>
struct GroupByAccum_1_int_i_1_AvgAccum_double_s {
  MapAccum<int64_t, AvgAccum>map;

  GroupByAccum_1_int_i_1_AvgAccum_double_s() { }

  GroupByAccum_1_int_i_1_AvgAccum_double_s(int64_t base_0, AvgAccum accum_0) {
    map = MapAccum<int64_t, AvgAccum> (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_i_1_AvgAccum_double_s& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_i_1_AvgAccum_double_s operator+ (GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    GroupByAccum_1_int_i_1_AvgAccum_double_s newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("i");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const AvgAccum& get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_i_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename d_t, typename s_t, class group_t>
struct GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group {
struct baseTuple {
  d_t d;
  s_t s;

  baseTuple() {
    d = d_t();
    s = s_t();
  }

  baseTuple(d_t d_, s_t s_){
    d = d_;
    s = s_;
  }
  operator std::tuple<d_t, s_t>() const {
    return std::make_tuple(d,s);
  }

  baseTuple(const std::tuple<d_t, s_t>& __GQUERY__other__576037) {
    d = std::get<0>(__GQUERY__other__576037);
    s = std::get<1>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const baseTuple& m) {
    os<<"[";
    os<<"d "<<m.d<<"|";
    os<<"s "<<m.s<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const baseTuple& m) {
    os<<"[";
    os<<"d "<<m.d<<"|";
    os<<"s "<<m.s<<"]";
      return os ;
  }


  bool operator==(baseTuple const &__GQUERY__other__576037) const {
    return
      d == __GQUERY__other__576037.d &&
      s == __GQUERY__other__576037.s;
  }


  baseTuple& operator+=( const baseTuple& __GQUERY__other__576037) {
      d += __GQUERY__other__576037.d;
      s += __GQUERY__other__576037.s;
    return *this;
  }

  friend std::size_t hash_value(const baseTuple& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.d);
    boost::hash_combine(seed, other.s);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("d");
      (d).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("s");
      (s).json_printer(writer, _request, graphAPI, true);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(baseTuple const &__GQUERY__other__576037) const {
      if (d < __GQUERY__other__576037.d) return true;
      if (d > __GQUERY__other__576037.d) return false;
      if (s < __GQUERY__other__576037.s) return true;
      if (s > __GQUERY__other__576037.s) return false;
      return false;
  }


  bool operator>(baseTuple const &__GQUERY__other__576037) const {
      if (d > __GQUERY__other__576037.d) return true;
      if (d < __GQUERY__other__576037.d) return false;
      if (s > __GQUERY__other__576037.s) return true;
      if (s < __GQUERY__other__576037.s) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (d, s);
   }

};

  MapAccum<baseTuple, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum > >map;

  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group() { }

  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group(d_t base_0, s_t base_1, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  accum_0) {
    map = MapAccum<baseTuple, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum > > (baseTuple(base_0, base_1), accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group operator+ (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) {
    GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("d");
      json_printer_util(it->first.d, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->first.s, writer, _request, graphAPI, verbose);
      writer.WriteName("group");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum > & get (d_t base_0, s_t base_1) {
    return map.get(baseTuple(base_0, base_1));
  }
  int size () const {
    return map.size();
  }
  bool containskey (d_t base_0, s_t base_1) {
    return map.containskey(baseTuple(base_0, base_1));
  }
  void clear () {
    map.clear();
  }
  void remove (d_t base_0, s_t base_1) {
    map.remove(baseTuple(base_0, base_1));
  }
  void remove (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename d_t, class s_t>
struct GroupByAccum_1_int_d_1_AvgAccum_double_s {
  MapAccum<int64_t, AvgAccum>map;

  GroupByAccum_1_int_d_1_AvgAccum_double_s() { }

  GroupByAccum_1_int_d_1_AvgAccum_double_s(int64_t base_0, AvgAccum accum_0) {
    map = MapAccum<int64_t, AvgAccum> (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_d_1_AvgAccum_double_s& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_d_1_AvgAccum_double_s operator+ (GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    GroupByAccum_1_int_d_1_AvgAccum_double_s newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("d");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const AvgAccum& get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_d_1_AvgAccum_double_s const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename d_t, class s_t>
struct GroupByAccum_1_int_d_1_SumAccum_int_s {
  MapAccum<int64_t, SumAccum<int64_t > >map;

  GroupByAccum_1_int_d_1_SumAccum_int_s() { }

  GroupByAccum_1_int_d_1_SumAccum_int_s(int64_t base_0, SumAccum<int64_t >  accum_0) {
    map = MapAccum<int64_t, SumAccum<int64_t > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_d_1_SumAccum_int_s& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_d_1_SumAccum_int_s operator+ (GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) {
    GroupByAccum_1_int_d_1_SumAccum_int_s newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("d");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("s");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const SumAccum<int64_t > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_d_1_SumAccum_int_s const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_sameParameterNamame (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_sameParameterNamame (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_sameParameterNamame () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_GACC_group1_1, GV_GACC_group2_1, GV_GACC_group3_1, GV_GACC_group4_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_group1_1, new  gpelib4::SumVariable<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  > > (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  >  ()));
   gvs->Register (GV_GACC_group2_1, new  gpelib4::SumVariable<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  > > (GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  >  ()));
   gvs->Register (GV_GACC_group3_1, new  gpelib4::SumVariable<GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum > > (GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum >  ()));
   gvs->Register (GV_GACC_group4_1, new  gpelib4::SumVariable<GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > > (GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_sameParameterNamame::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}


void (UDF_sameParameterNamame::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_sameParameterNamame::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_sameParameterNamame INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("sameParameterNamame");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_sameParameterNamame DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                     context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  > > (GV_GACC_group1_1).clear();
                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  > > (GV_GACC_group2_1).clear();
                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum > > (GV_GACC_group3_1).clear();
                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > > (GV_GACC_group4_1).clear();
                 PC = 4;
                 break;
           PC = 4; break;
         }

         case 4:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  > > (GV_GACC_group1_1) += ( GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  > (2l,string("hello"),( GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > (2l,( 2l)))));
                     PC = 5;
                     break;

           PC = 5; break;
         }

         case 5:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  > > (GV_GACC_group2_1) += ( GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  > (2l,string("hello"),( GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum > (2l,( 2l)))));
                     PC = 6;
                     break;

           PC = 6; break;
         }

         case 6:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum > > (GV_GACC_group3_1) += ( GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum > (2l,( 1l)));
                     PC = 7;
                     break;

           PC = 7; break;
         }

         case 7:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > > (GV_GACC_group4_1) += ( GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > (2l,( 1l)));
                     PC = 8;
                     break;

           PC = 8; break;
         }

         case 8:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@group1");
(( (context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_SumAccum_int_s_group<int64_t, string, GroupByAccum_1_int_i_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  >  > > (GV_GACC_group1_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 9;
                 break;

           PC = 9; break;
         }

         case 9:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@group2");
(( (context->GlobalVariable_GetValue<GroupByAccum_2_d_s_1_GroupByAccum_GroupByAccum_1_int_i_1_AvgAccum_double_s_group<int64_t, string, GroupByAccum_1_int_i_1_AvgAccum_double_s<int64_t, AvgAccum >  > > (GV_GACC_group2_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 10;
                 break;

           PC = 10; break;
         }

         case 10:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@group3");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_AvgAccum_double_s<int64_t, AvgAccum > > (GV_GACC_group3_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 11;
                 break;

           PC = 11; break;
         }

         case 11:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@group4");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_d_1_SumAccum_int_s<int64_t, SumAccum<int64_t >  > > (GV_GACC_group4_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 12;
                 break;

           PC = 12; break;
         }

         case 12:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
   V_VALUE* vvalptr = nullptr;
};

  bool call_sameParameterNamame(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query sameParameterNamame in all vetext active mode." << std::endl;
    }
    UDIMPL::empty_graph::UDF_sameParameterNamame udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterNamame") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterNamame") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterNamame") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterNamame") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_sameParameterNamame(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::empty_graph::UDF_sameParameterNamame udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_sameParameterNamame_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_sameParameterNamame(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_sameParameterNamame(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_sameParameterNamame(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query sameParameterNamame in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::empty_graph::UDF_sameParameterNamame udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
