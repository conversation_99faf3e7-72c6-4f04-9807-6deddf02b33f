/********** query start ***************
CREATE QUERY sameParameterName(/* Parameters here */) FOR GRAPH empty_graph { 
  GroupByAccum<INT a, SumAccum<INT> b> @@accum1;
  GroupByAccum<INT a, SumAccum<STRING>b> @@accum2;
  GroupByAccum<INT a, MapAccum<STRING, INT> b> @@accum3;
  GroupByAccum<INT a, MapAccum<INT, STRING> b> @@accum4;
  
  @@accum1 += (1 -> 2);
  @@accum2 += (1 -> "2");
  @@accum3 += (1 -> ("1" -> 2));
  @@accum4 += (1 -> (1 -> "2"));
  
  PRINT @@accum1;
  PRINT @@accum2;
  PRINT @@accum3;
  PRINT @@accum4;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace empty_graph{ 
class UDF_sameParameterName :public gpelib4::BaseUDF {
template<typename a_t, class b_t>
struct GroupByAccum_1_int_a_1_SumAccum_int_b {
  MapAccum<int64_t, SumAccum<int64_t > >map;

  GroupByAccum_1_int_a_1_SumAccum_int_b() { }

  GroupByAccum_1_int_a_1_SumAccum_int_b(int64_t base_0, SumAccum<int64_t >  accum_0) {
    map = MapAccum<int64_t, SumAccum<int64_t > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_a_1_SumAccum_int_b& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_a_1_SumAccum_int_b operator+ (GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) {
    GroupByAccum_1_int_a_1_SumAccum_int_b newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("a");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("b");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const SumAccum<int64_t > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_a_1_SumAccum_int_b const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename a_t, class b_t>
struct GroupByAccum_1_int_a_1_SumAccum_string_b {
  MapAccum<int64_t, SumAccum<string > >map;

  GroupByAccum_1_int_a_1_SumAccum_string_b() { }

  GroupByAccum_1_int_a_1_SumAccum_string_b(int64_t base_0, SumAccum<string >  accum_0) {
    map = MapAccum<int64_t, SumAccum<string > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_a_1_SumAccum_string_b& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_a_1_SumAccum_string_b operator+ (GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) {
    GroupByAccum_1_int_a_1_SumAccum_string_b newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("a");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("b");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const SumAccum<string > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_a_1_SumAccum_string_b const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename a_t, class b_t>
struct GroupByAccum_1_int_a_1_MapAccum_string_int_b {
  MapAccum<int64_t, MapAccum<string, int64_t > >map;

  GroupByAccum_1_int_a_1_MapAccum_string_int_b() { }

  GroupByAccum_1_int_a_1_MapAccum_string_int_b(int64_t base_0, MapAccum<string, int64_t >  accum_0) {
    map = MapAccum<int64_t, MapAccum<string, int64_t > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_a_1_MapAccum_string_int_b& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_a_1_MapAccum_string_int_b operator+ (GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) {
    GroupByAccum_1_int_a_1_MapAccum_string_int_b newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("a");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("b");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const MapAccum<string, int64_t > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_a_1_MapAccum_string_int_b const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

template<typename a_t, class b_t>
struct GroupByAccum_1_int_a_1_MapAccum_int_string_b {
  MapAccum<int64_t, MapAccum<int64_t, string > >map;

  GroupByAccum_1_int_a_1_MapAccum_int_string_b() { }

  GroupByAccum_1_int_a_1_MapAccum_int_string_b(int64_t base_0, MapAccum<int64_t, string >  accum_0) {
    map = MapAccum<int64_t, MapAccum<int64_t, string > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_a_1_MapAccum_int_string_b& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_int_a_1_MapAccum_int_string_b operator+ (GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) {
    GroupByAccum_1_int_a_1_MapAccum_int_string_b newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("a");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("b");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const MapAccum<int64_t, string > & get (int64_t base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (int64_t base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (int64_t base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_int_a_1_MapAccum_int_string_b const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_sameParameterName (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_sameParameterName (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_sameParameterName () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_GACC_accum1_1, GV_GACC_accum2_1, GV_GACC_accum3_1, GV_GACC_accum4_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_accum1_1, new  gpelib4::SumVariable<GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  > > (GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  >  ()));
   gvs->Register (GV_GACC_accum2_1, new  gpelib4::SumVariable<GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  > > (GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  >  ()));
   gvs->Register (GV_GACC_accum3_1, new  gpelib4::SumVariable<GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  > > (GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  >  ()));
   gvs->Register (GV_GACC_accum4_1, new  gpelib4::SumVariable<GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  > > (GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_sameParameterName::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}


void (UDF_sameParameterName::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_sameParameterName::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_sameParameterName INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("sameParameterName");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_sameParameterName DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  > > (GV_GACC_accum1_1).clear();
                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  > > (GV_GACC_accum2_1).clear();
                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  > > (GV_GACC_accum3_1).clear();
                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  > > (GV_GACC_accum4_1).clear();
                 PC = 4;
                 break;
           PC = 4; break;
         }

         case 4:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  > > (GV_GACC_accum1_1) += ( GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  > (1l,( 2l)));
                     PC = 5;
                     break;

           PC = 5; break;
         }

         case 5:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  > > (GV_GACC_accum2_1) += ( GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  > (1l,( string("2"))));
                     PC = 6;
                     break;

           PC = 6; break;
         }

         case 6:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  > > (GV_GACC_accum3_1) += ( GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  > (1l,( MapAccum<string, int64_t > (string("1"),( 2l)))));
                     PC = 7;
                     break;

           PC = 7; break;
         }

         case 7:
         {

                     context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  > > (GV_GACC_accum4_1) += ( GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  > (1l,( MapAccum<int64_t, string > (1l,( string("2"))))));
                     PC = 8;
                     break;

           PC = 8; break;
         }

         case 8:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@accum1");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_int_b<int64_t, SumAccum<int64_t >  > > (GV_GACC_accum1_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 9;
                 break;

           PC = 9; break;
         }

         case 9:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@accum2");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_SumAccum_string_b<int64_t, SumAccum<string >  > > (GV_GACC_accum2_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 10;
                 break;

           PC = 10; break;
         }

         case 10:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@accum3");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_string_int_b<int64_t, MapAccum<string, int64_t >  > > (GV_GACC_accum3_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 11;
                 break;

           PC = 11; break;
         }

         case 11:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@accum4");
(( (context->GlobalVariable_GetValue<GroupByAccum_1_int_a_1_MapAccum_int_string_b<int64_t, MapAccum<int64_t, string >  > > (GV_GACC_accum4_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 12;
                 break;

           PC = 12; break;
         }

         case 12:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
   V_VALUE* vvalptr = nullptr;
};

  bool call_sameParameterName(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query sameParameterName in all vetext active mode." << std::endl;
    }
    UDIMPL::empty_graph::UDF_sameParameterName udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterName") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterName") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterName") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "sameParameterName") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_sameParameterName(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::empty_graph::UDF_sameParameterName udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_sameParameterName_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_sameParameterName(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_sameParameterName(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_sameParameterName(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query sameParameterName in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::empty_graph::UDF_sameParameterName udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
