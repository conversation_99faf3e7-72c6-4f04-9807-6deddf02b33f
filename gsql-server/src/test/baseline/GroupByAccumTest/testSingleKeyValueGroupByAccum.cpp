/********** query start ***************
create query singleKeyValueGroupBy() for graph person_movie {
  GroupByAccum<vertex<person> id, ListAccum<vertex<person>> listMems> @compGroupIds;
  SumAccum<INT> @curId;
  
  Start = {person.*};
  vSet = SELECT src
        FROM Start:src-(roles:e)->:tgt
        ACCUM FOREACH (vtx, l) IN src.@compGroupIds
          DO
            vtx.@curId += 1,
            FOREACH v IN l
              DO
                v.@curId += 1
              END
          END
        ;
  
  PRINT vSet [vSet.@compGroupIds];
  PRINT vSet [vSet.@curId];
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace person_movie{ 
class UDF_singleKeyValueGroupBy :public gpelib4::BaseUDF {
template<typename id_t, class listMems_t>
struct GroupByAccum_1_person_id_1_ListAccum_person_listMems {
  MapAccum<VERTEX, ListAccum<VERTEX > >map;

  GroupByAccum_1_person_id_1_ListAccum_person_listMems() { }

  GroupByAccum_1_person_id_1_ListAccum_person_listMems(VERTEX base_0, ListAccum<VERTEX >  accum_0) {
    map = MapAccum<VERTEX, ListAccum<VERTEX > > (base_0, accum_0);
  }

  template <class ARCHIVE>
  void serialize(ARCHIVE& __GQUERY__ar__576037) {
    __GQUERY__ar__576037 (map);
  }
  friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_person_id_1_ListAccum_person_listMems& m) {
    os << m.map;
    return os ;
  }
  bool operator==(GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) const {
    return map == __GQUERY__other__576037.map;
  }
  bool operator!=(GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) const {
    return map != __GQUERY__other__576037.map;
  }
  GroupByAccum_1_person_id_1_ListAccum_person_listMems operator+ (GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) {
    GroupByAccum_1_person_id_1_ListAccum_person_listMems newG;
    newG += *this;
    newG += __GQUERY__other__576037;
    return newG;
  }
  void operator= (GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) {
    map = __GQUERY__other__576037.map;
  }
  void operator+=(GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) {
    map += __GQUERY__other__576037.map;
  }
  void json_printer (gutil::JSONWriter& writer,
    gpelib4::EngineServiceRequest& _request,
    gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
    writer.WriteStartArray();
    for (auto it = map.begin(); it != map.end(); it++) {
      writer.WriteStartObject();
      writer.WriteName("id");
      json_printer_util(it->first, writer, _request, graphAPI, verbose);
      writer.WriteName("listMems");
      json_printer_util(it->second, writer, _request, graphAPI, verbose);
      writer.WriteEndObject();
    }
    writer.WriteEndArray();
  }
  const ListAccum<VERTEX > & get (VERTEX base_0) {
    return map.get(base_0);
  }
  int size () const {
    return map.size();
  }
  bool containskey (VERTEX base_0) {
    return map.containskey(base_0);
  }
  void clear () {
    map.clear();
  }
  void remove (VERTEX base_0) {
    map.remove(base_0);
  }
  void remove (GroupByAccum_1_person_id_1_ListAccum_person_listMems const &__GQUERY__other__576037) {
    map.remove(__GQUERY__other__576037.map);
  }
  decltype(map.begin()) begin () {
    return map.begin();
  }
  decltype(map.end()) end () {
    return map.end();
  }
};

struct __GQUERY__Delta__576037 {
   // accumulators:
   SumAccum<int64_t >  curId_1;
   bool __GQUERY__hasChanged___576037curId_1;
   bool __GQUERY__set___576037curId_1;

   GroupByAccum_1_person_id_1_ListAccum_person_listMems<VERTEX, ListAccum<VERTEX >  >  compGroupIds_1;
   bool __GQUERY__hasChanged___576037compGroupIds_1;
   bool __GQUERY__set___576037compGroupIds_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037curId_1 = false;
      __GQUERY__set___576037curId_1 = false;
      __GQUERY__hasChanged___576037compGroupIds_1 = false;
      __GQUERY__set___576037compGroupIds_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037curId_1) {
        curId_1 += __GQUERY__other__576037.curId_1;
        __GQUERY__hasChanged___576037curId_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037curId_1) {
        curId_1 = __GQUERY__other__576037.curId_1;
        __GQUERY__set___576037curId_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037compGroupIds_1) {
        compGroupIds_1 += __GQUERY__other__576037.compGroupIds_1;
        __GQUERY__hasChanged___576037compGroupIds_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037compGroupIds_1) {
        compGroupIds_1 = __GQUERY__other__576037.compGroupIds_1;
        __GQUERY__set___576037compGroupIds_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   SumAccum<int64_t >  curId_1;
   GroupByAccum_1_person_id_1_ListAccum_person_listMems<VERTEX, ListAccum<VERTEX >  >  compGroupIds_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      curId_1 = __GQUERY__other__576037.curId_1;
      compGroupIds_1 = __GQUERY__other__576037.compGroupIds_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(curId_1, compGroupIds_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037curId_1) curId_1 += __GQUERY__d__576037.curId_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037curId_1) curId_1 = __GQUERY__d__576037.curId_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037compGroupIds_1) compGroupIds_1 += __GQUERY__d__576037.compGroupIds_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037compGroupIds_1) compGroupIds_1 = __GQUERY__d__576037.compGroupIds_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_singleKeyValueGroupBy (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_singleKeyValueGroupBy (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_singleKeyValueGroupBy () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_Start_SIZE, GV_SYS_Start_ORDERBY, GV_SYS_Start_LASTSET, GV_SYS_vSet_SIZE, GV_SYS_vSet_ORDERBY, GV_SYS_vSet_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_Start_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Start_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Start_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vSet_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vSet_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vSet_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_singleKeyValueGroupBy::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_5_vSet (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Enter function Write_5_vSet v: " << v << std::endl;
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("vSet.@curId");
writer.WriteInt(( v_val->curId_1.data_));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Exit function Write_5_vSet v: " << v << std::endl;
}


void Write_4_vSet (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Enter function Write_4_vSet v: " << v << std::endl;
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("vSet.@compGroupIds");
(( v_val->compGroupIds_1)).json_printer(writer, _request, context->GraphAPI(), true);


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Exit function Write_4_vSet v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_3 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Enter function EdgeMap_3 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->compGroupIds_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_vtx = it->first;
    auto& foreach_l = it->second;
   __GQUERY__Delta__576037 vtx_other_delta = __GQUERY__Delta__576037 ();
   vtx_other_delta.__GQUERY__isOther__576037 = true;

   vtx_other_delta.curId_1 += ( 1l);
   vtx_other_delta.__GQUERY__hasChanged___576037curId_1 = true;
   {
uint64_t __GQUERY__step_2 = 0;
const auto& __GQUERY__stepvar_2 = foreach_l;
  for (auto it = (__GQUERY__stepvar_2).begin(); it != (__GQUERY__stepvar_2).end(); it++) {
    auto& foreach_v = *it;
   __GQUERY__Delta__576037 v_other_delta = __GQUERY__Delta__576037 ();
   v_other_delta.__GQUERY__isOther__576037 = true;

   v_other_delta.curId_1 += ( 1l);
   v_other_delta.__GQUERY__hasChanged___576037curId_1 = true;   context->Write (foreach_v, v_other_delta);

if (UNLIKELY((++__GQUERY__step_2 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}
   context->Write (foreach_vtx, vtx_other_delta);

if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Exit function EdgeMap_3 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_3 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Enter function Reduce_3 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Exit function Reduce_3 v: " << v << std::endl;
}


void (UDF_singleKeyValueGroupBy::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_singleKeyValueGroupBy::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("singleKeyValueGroupBy");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_singleKeyValueGroupBy DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_person,true);

                 PC = 3;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Start";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "Start", 5);
                     timer_.saveVSetCode(30L, "Start = {person.*};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET) = 30L;
                   }
                 break;

           PC = 3; break;
         }

         case 3:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_roles);

                 edgemap   = &UDF_singleKeyValueGroupBy::EdgeMap_3;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_singleKeyValueGroupBy::Reduce_3;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vSet";
                 PC = 4;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_singleKeyValueGroupBy INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(38L, "vSet", 6);
                     timer_.saveVSetCode(38L, "vSet = SELECT src\n        FROM Start:src-(roles:e)-> :tgt\n        ACCUM FOREACH (vtx, l) IN src.@compGroupIds\n          DO\n            vtx.@curId += 1,\n            FOREACH v IN l\n              DO\n                v.@curId += 1\n              END\n          END\n        ;");
                     timer_.addDependency(38L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET));
                     timer_.start("vSet", 6, context->CalcActiveVertexCount(), 38L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_LASTSET) = 38L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vSet_ORDERBY) = -1;
                   break;
                 }

           PC = 4; break;
         }

         case 4:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_singleKeyValueGroupBy::Write_4_vSet;
                 context->set_udfprintsetting(0);

                 writer.WriteName("vSet");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 5;
                 break;

           PC = 5; break;
         }

         case 5:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_singleKeyValueGroupBy::Write_5_vSet;
                 context->set_udfprintsetting(0);

                 writer.WriteName("vSet");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 6;
                 break;

           PC = 6; break;
         }

         case 6:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 3:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_vSet_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_person = 0;
const int _schema_ETY_roles = 0;
   V_VALUE* vvalptr = nullptr;
};

  bool call_singleKeyValueGroupBy(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query singleKeyValueGroupBy in all vetext active mode." << std::endl;
    }
    UDIMPL::person_movie::UDF_singleKeyValueGroupBy udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "singleKeyValueGroupBy") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "singleKeyValueGroupBy") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "singleKeyValueGroupBy") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "singleKeyValueGroupBy") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_singleKeyValueGroupBy(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::person_movie::UDF_singleKeyValueGroupBy udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_singleKeyValueGroupBy_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_singleKeyValueGroupBy(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_singleKeyValueGroupBy(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_singleKeyValueGroupBy(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query singleKeyValueGroupBy in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::person_movie::UDF_singleKeyValueGroupBy udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
