/********** query start ***************
CREATE QUERY insertWithStringLiteralIncludingPercentSign() {
  start =
    SELECT t
    FROM Person:s - (KNOWS) - Person:t
    POST-<PERSON>UM
      INSERT INTO KNOWS VALUES(s, 10995116277782, to_datetime("2015-11-15 %Y-%M"))
    POST-ACCUM(s)
      INSERT INTO TagClass values(s.id+1000, "2015-11-15 %Y-%M", "%Y-%M%S");
 }
********** query end ***************/
#include "ldbc_snb-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace ldbc_snb {
    class GPR_insertWithStringLiteralIncludingPercentSign {
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_insertWithStringLiteralIncludingPercentSign (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
        _schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& ETY_KNOWS_attrMeta = meta->GetEdgeType(20).attributes_;
        _schema_EATT_KNOWS_576037_creationDate = ETY_KNOWS_attrMeta.GetAttributePosition("creationDate", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_insertWithStringLiteralIncludingPercentSign (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        __GQUERY__graphupdate = _graphupdates_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
        _schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& ETY_KNOWS_attrMeta = meta->GetEdgeType(20).attributes_;
        _schema_EATT_KNOWS_576037_creationDate = ETY_KNOWS_attrMeta.GetAttributePosition("creationDate", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_insertWithStringLiteralIncludingPercentSign () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void EdgeMap_2(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        // type filters
        if (_schema_VTY_Person >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_Person)
          return;
        
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        DefaultDelta tgt_delta = DefaultDelta();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        uint64_t v_id_uint64_t = 0;
        bool v_id_uint64_t_flag = false;
        
        //get v's attribute
        int v_typeIDVar = graphAPI->GetVertexType(v);
        if (v_typeIDVar == _schema_VTY_Person) {
          if (vVertex.GetAttr().IsValid()) {
            v_id_uint64_t = vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0);
            v_id_uint64_t_flag = true;
          }
        }
        V_VALUE v_val;
        
        // INSERT into KNOWS values ( s, 10995116277782, to_datetime ( "2015-11-15 %Y-%M" ) )
        if (delta.__GQUERY__isSrc__576037) {
          {
            size_t e_typeIDVar = _schema_ETY_KNOWS;
            uint32_t src_typeIDVar = graphAPI->GetVertexType(v);
            uint32_t tgt_typeIDVar = _schema_VTY_Person;
            topology4::DeltaVertexId srcdeltaid_ (src_typeIDVar, v);
            if (GSQL_UTIL::__to_string(10995116277782l) == string("")) {
              string msg("Runtime Error: empty vertex id in 'INSERT into KNOWS values ( s, 10995116277782, to_datetime ( \"2015-11-15 %Y-%M\" ) )'");
              throw gutil::GsqlException(msg, gutil::error_t::E_VERTEX_ID);
            }
            topology4::DeltaVertexId tgtdeltaid_ (tgt_typeIDVar, GSQL_UTIL::__to_string(10995116277782l));
            if (srcdeltaid_.isInvalid()) {
              string msg("Runtime Error: Inserting an edge of type '" + std::string("KNOWS") + "' with an invalid FROM vertex 'v'.");
              throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
            }
            if (tgtdeltaid_.isInvalid()) {
              string msg("Runtime Error: Inserting an edge of type '" + std::string("KNOWS") + "' with an invalid TO vertex '10995116277782'.");
              throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
            }
            string srcTy = _serviceapi->GetTopologyMeta()->GetVertexType(src_typeIDVar).typename_;
            string tgtTy = _serviceapi->GetTopologyMeta()->GetVertexType(tgt_typeIDVar).typename_;
            topology4::EdgeTypeMeta& eMeta = _serviceapi->GetTopologyMeta()->GetEdgeType(e_typeIDVar);
            string eName = eMeta.typename_;
            bool _GQUERY_TYPE_CHECK_VALID = true;
            if (UNLIKELY(!eMeta.edgepairs_.empty())) {
              std::pair<string, string> p = std::make_pair(srcTy, tgtTy);
              if (std::find(eMeta.edgepairs_.begin(), eMeta.edgepairs_.end(), p) == eMeta.edgepairs_.end()) {
                tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, tgt_typeIDVar);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              }
            } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.fromtypename_) != src_typeIDVar)) {
              tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, -1);
              __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
              _GQUERY_TYPE_CHECK_VALID = false;
            } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.totypename_) != tgt_typeIDVar)) {
              tuple_eid_svid_tvid key(e_typeIDVar, -1, tgt_typeIDVar);
              __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
              _GQUERY_TYPE_CHECK_VALID = false;
            }
            if (_GQUERY_TYPE_CHECK_VALID) {
              auto graphupdate_ = context.GetGraphUpdate();
              uint32_t esubtypeid = 0;
              std::stringstream multiedgeIdStream;
              bool isInsKey = false;
              if (eMeta.discriminator_count_ > 0) {
                gutil::EnableDiscriminator(esubtypeid);
                isInsKey = true;
              }
              if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_KNOWS_576037_creationDate].is_discriminator_) {
                MergeAttrToMultiedgeId(multiedgeIdStream, gutil::GtimeConverter::datetime_constructor(string("2015-11-15 %Y-%M"), _request, true));
              }
              std::string multiedgeId = multiedgeIdStream.str();
              {
                gutil::GLock lock(graphupdate_->GetMutex());
                {
                  const int64_t& attrValue = gutil::GtimeConverter::datetime_constructor(string("2015-11-15 %Y-%M"), _request, true);
                  graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_KNOWS_576037_creationDate, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                }
              }
              graphupdate_->CheckForFlush();
            }
          }
          
        }
        
        // INSERT into TagClass values ( s.id + 1000, "2015-11-15 %Y-%M", "%Y-%M%S" )
        if (delta.__GQUERY__isSrc__576037 && v_id_uint64_t_flag && v_id_uint64_t_flag) {
          {
            if (GSQL_UTIL::__to_string((v_id_uint64_t + 1000l)) == string("")) {
              string msg("Runtime Error: empty vertex id in 'INSERT into TagClass values ( s.id + 1000, \"2015-11-15 %Y-%M\", \"%Y-%M%S\" )'");
              throw gutil::GsqlException(msg, gutil::error_t::E_VERTEX_ID);
            }
            topology4::DeltaVertexId deltaid_ (_schema_VTY_TagClass, GSQL_UTIL::__to_string((v_id_uint64_t + 1000l)));
            auto graphupdate_ = context.GetGraphUpdate();
            {
              gutil::GLock lock(graphupdate_->GetMutex());
              if (graphupdate_->GetAU(deltaid_)->IsUpdatable(_schema_VATT_TagClass_576037_id)) { 
                graphupdate_->GetAU(deltaid_)->Set(_schema_VATT_TagClass_576037_id, uint64_t((v_id_uint64_t + 1000l)), topology4::DeltaAttributeOperator_Overwrite);
              } else {
                graphupdate_->GetAU(deltaid_)->ThrowDiscriminatorError(_schema_VATT_TagClass_576037_id);
              }
              if (graphupdate_->GetAU(deltaid_)->IsUpdatable(_schema_VATT_TagClass_576037_name)) { 
                graphupdate_->GetAU(deltaid_)->Set(_schema_VATT_TagClass_576037_name, string(string("2015-11-15 %Y-%M")), topology4::DeltaAttributeOperator_Overwrite);
              } else {
                graphupdate_->GetAU(deltaid_)->ThrowDiscriminatorError(_schema_VATT_TagClass_576037_name);
              }
              if (graphupdate_->GetAU(deltaid_)->IsUpdatable(_schema_VATT_TagClass_576037_url)) { 
                graphupdate_->GetAU(deltaid_)->Set(_schema_VATT_TagClass_576037_url, string(string("%Y-%M%S")), topology4::DeltaAttributeOperator_Overwrite);
              } else {
                graphupdate_->GetAU(deltaid_)->ThrowDiscriminatorError(_schema_VATT_TagClass_576037_url);
              }
            }
            graphupdate_->CheckForFlush();
          }
          
        }
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          singleGprMainFlow();
          {
            //commit action
            if (!isQueryCalled) _request.ResetQueryState();
            _serviceapi->QueryResponse_VIdtoUId(&_request);
            // set ignored insertion message before commit
            this->setIgnoredInsertionCountMsg(__GQUERY_UNIVERSAL_INSERTION_MAP.Value());
            if (!_request.error_) {
              if (!isQueryCalled) {
                _request.ResetQueryState();
                if (!__GQUERY__graphupdate->Commit()) {
                  throw gutil::GsqlException("Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
                }
              } else {
                __GQUERY__graphupdate->FlushAttribute();
              }
            }
          }
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void singleGprMainFlow () {
        try {
          gshared_ptr<gpr::GPR> gpr = _serviceapi->CreateGPR(&_request, true);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_start = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_;
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_start_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_start_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_start_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          if (!isQueryCalled) __GQUERY__graphupdate = _serviceapi->CreateGraphUpdates(&_request);
          
          {
            __GQUERY_GV_SYS_commit_update.Value() = true;
            /*
            start = select t from Person : s - ( KNOWS : x ) - Person : t post-accum INSERT into KNOWS values ( s, 10995116277782, to_datetime ( "2015-11-15 %Y-%M" ) ) post-accum INSERT into TagClass values ( s.id + 1000, "2015-11-15 %Y-%M", "%Y-%M%S" );
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|" << "starts action_1_";
            //declare output gvs
            //run action
            __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
            __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_Person, true);
            __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|" << "starts action_2_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
            //edge filters
            auto edgeFilterCtrl = gpr->CreateTypeFilterController();
            edgeFilterCtrl->DisableAllEdgeTypes();
            edgeFilterCtrl->EnableEdgeType(_schema_ETY_KNOWS);
            
            auto edgeAction = gpr->CreateEdgeAction(
              EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign::EdgeMap_2, this),//action function
              __GQUERY__vSet___GQUERY__source.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container2},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
            );
            edgeAction->AddInputObject(this);
            edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
            edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
            
            gpr::RemoteTopology::Filter filter;
            edgeAction->AddFilter(&filter);
            if (__disable_filter_) edgeAction->DisableFilter();
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_start_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign::Reduce_2, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container2},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_start_tmp.get()},//result bitset
              {},//input gv
              {&__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
            );
            reduceAction->AddInputObject(this);
            reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({edgeAction.get(), reduceAction.get()}, true, false);
            std::swap(__GQUERY__vSet_start, __GQUERY__vSet_start_tmp);
            __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|" << "executor finish action_2_map";
            if (__GQUERY__graphupdate->Error()){
              throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
            }
            __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
            
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end singleGprMainFlow
      
      ///helper function
      // tuple detail: edge id, source vertex id, target vertex id
      typedef std::tuple<size_t,uint32_t,uint32_t> tuple_eid_svid_tvid;
      ///method for count of ignored universal insert statement
      void setIgnoredInsertionCountMsg(const MapAccum<tuple_eid_svid_tvid, uint32_t> & data) {
        if (isQueryCalled) {
          if (__GQUERY_universal_insertion_data != nullptr){
            *__GQUERY_universal_insertion_data += data;
          }
          return;
        }
        if (data.size() == 0) return;
        std::stringstream msgss;
        auto topologyMeta = _serviceapi->GetTopologyMeta();
        char _buffer[1024];
        for (auto & it : data) {
          auto& _tuple = it.first;
          string eName = topologyMeta->GetEdgeType(std::get<0>(_tuple)).typename_;
          string srcTy, tgtTy;
          if (std::get<1>(_tuple) != -1) {
            srcTy = topologyMeta->GetVertexType(std::get<1>(_tuple)).typename_;
          }
          if (std::get<2>(_tuple) != -1) {
            tgtTy = topologyMeta->GetVertexType(std::get<2>(_tuple)).typename_;
          }
          if (std::get<1>(_tuple) == -1) {
            sprintf(_buffer, "WARNING: '%s' is not a valid TO vertex type for edge type '%s'.", tgtTy.c_str(), eName.c_str());
          } else if (std::get<2>(_tuple) == -1) {
            sprintf(_buffer, "WARNING: '%s' is not a valid FROM vertex type for edge type '%s'.", srcTy.c_str(), eName.c_str());
          } else {
            sprintf(_buffer, "WARNING: (%s, %s) is not a valid vertex pair for edge type '%s'.",srcTy.c_str(), tgtTy.c_str(), eName.c_str());
          }
          msgss << _buffer << " Ignored edge count: " << it.second << std::endl;
        }
        _request.SetErrorMessage(msgss.str());
      }
      void setUniversalInsertionData(MapAccum<tuple_eid_svid_tvid, uint32_t>* _data){
        this->__GQUERY_universal_insertion_data = _data;
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_Person = 8;
      const int _schema_VTY_TagClass = 10;
      const int _schema_ETY_KNOWS = 20;
      int _schema_VATT_Person_576037_id = -1;
      int _schema_VATT_TagClass_576037_id = -1;
      int _schema_VATT_TagClass_576037_name = -1;
      int _schema_VATT_TagClass_576037_url = -1;
      int _schema_EATT_KNOWS_576037_creationDate = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_implicit_commit = "action_implicit_commit";
      const std::string action_explicit_commit = "action_explicit_commit";
      const std::string action_abort = "action_abort";
      gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
      topology4::GraphUpdatesPointer __GQUERY__graphupdate;
      MapAccum<tuple_eid_svid_tvid, uint32_t>* __GQUERY_universal_insertion_data = nullptr;
      public:
        
        ///return vars
      };//end class GPR_insertWithStringLiteralIncludingPercentSign
    bool call_insertWithStringLiteralIncludingPercentSign(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_insertWithStringLiteralIncludingPercentSign_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      //single gpr doing nothing
    }
    bool call_insertWithStringLiteralIncludingPercentSign(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "insertWithStringLiteralIncludingPercentSign") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_insertWithStringLiteralIncludingPercentSign(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_insertWithStringLiteralIncludingPercentSign(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::ldbc_snb::GPR_insertWithStringLiteralIncludingPercentSign gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.setUniversalInsertionData(_mapaccum);
      gpr.run();
      
    }
  }//end namespace ldbc_snb
}//end namespace UDIMPL
