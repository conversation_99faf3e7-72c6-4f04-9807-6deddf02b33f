/********** query start ***************
create or replace query test(vertex inputv) for graph ldbc_snb{
  ListAccum<STRING> @tag_name;
  SumAccum<int> @cnt;
  seed = {inputv};
  tagss = SELECT t
          FROM seed:s-(HAS_TAG:e)-Tag:t
          ACCUM s.@tag_name+=t.name;
  persons = SELECT t
            FROM seed:s-(HAS_MEMBER:e)-Person:t
            ACCUM t.@cnt += 1;
  print seed[seed.id AS id, seed.@tag_name.get(0) AS tag]
        ,persons[persons.@cnt as cnt_persons] as forum<PERSON>ersons;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_test :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:
   SumAccum<int64_t >  cnt_1;
   bool __GQUERY__hasChanged___576037cnt_1;
   bool __GQUERY__set___576037cnt_1;

   ListAccum<string >  tag_name_1;
   bool __GQUERY__hasChanged___576037tag_name_1;
   bool __GQUERY__set___576037tag_name_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037cnt_1 = false;
      __GQUERY__set___576037cnt_1 = false;
      __GQUERY__hasChanged___576037tag_name_1 = false;
      __GQUERY__set___576037tag_name_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037cnt_1) {
        cnt_1 += __GQUERY__other__576037.cnt_1;
        __GQUERY__hasChanged___576037cnt_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037cnt_1) {
        cnt_1 = __GQUERY__other__576037.cnt_1;
        __GQUERY__set___576037cnt_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037tag_name_1) {
        tag_name_1 += __GQUERY__other__576037.tag_name_1;
        __GQUERY__hasChanged___576037tag_name_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037tag_name_1) {
        tag_name_1 = __GQUERY__other__576037.tag_name_1;
        __GQUERY__set___576037tag_name_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   SumAccum<int64_t >  cnt_1;
   ListAccum<string >  tag_name_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      cnt_1 = __GQUERY__other__576037.cnt_1;
      tag_name_1 = __GQUERY__other__576037.tag_name_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(cnt_1, tag_name_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037cnt_1) cnt_1 += __GQUERY__d__576037.cnt_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037cnt_1) cnt_1 = __GQUERY__d__576037.cnt_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037tag_name_1) tag_name_1 += __GQUERY__d__576037.tag_name_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037tag_name_1) tag_name_1 = __GQUERY__d__576037.tag_name_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_test (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
_schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
_schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
_schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
_schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
_schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
_schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
    if (request.jsoptions_.isMember("inputv")) {
      VertexLocalId_t localId;
      if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
        _inputv = VERTEX(std::atoll(request.jsoptions_["inputv"][0]["id"].asString().c_str()));
      } else {
        std::stringstream ss;
        ss << serviceapi.GetTopologyMeta()->GetVertexTypeId(request.jsoptions_["inputv"][0]["type"].asString(), _request.graph_id_);
        ss << "_" << request.jsoptions_["inputv"][0]["id"].asString();
        if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
          _inputv = VERTEX(localId);
        } else {
          std::string msg("Failed to convert user vertex id for parameter inputv");
          HF_set_error(request, msg, true);
          return;
        }
      }
      inputv_flag = true;
    } else {
      _inputv = VERTEX(-1);
      inputv_flag = false;
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_test (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , VERTEX inputv, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
_schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
_schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
_schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
_schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
_schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
_schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_inputv = inputv;
inputv_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_test () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_inputv, GV_SYS_inputv_flag, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_persons_SIZE, GV_SYS_persons_ORDERBY, GV_SYS_persons_LASTSET, GV_SYS_seed_SIZE, GV_SYS_seed_ORDERBY, GV_SYS_seed_LASTSET, GV_SYS_tagss_SIZE, GV_SYS_tagss_ORDERBY, GV_SYS_tagss_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_persons_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_persons_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_persons_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_seed_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_seed_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_seed_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_tagss_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_tagss_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_tagss_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_inputv, new  gpelib4::BroadcastVariable<VERTEX> (_inputv));
   gvs->Register (GV_SYS_inputv_flag, new  gpelib4::BroadcastVariable<bool> (inputv_flag));


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_test::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_5_forumPersons (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function Write_5_forumPersons v: " << v << std::endl;
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("cnt_persons");
writer.WriteInt(( v_val->cnt_1.data_));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function Write_5_forumPersons v: " << v << std::endl;
}


void Write_5_seed (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function Write_5_seed v: " << v << std::endl;

   //attributes' local var declaration
   uint64_t seed_id_uint64_t = 0;
   bool seed_id_uint64_t_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_Comment) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Comment_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Company) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Company_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Continent) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Continent_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_University) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_University_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Post) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Post_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Country) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Country_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_TagClass) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_TagClass_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_City) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_City_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Tag) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Tag_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Person) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Person_576037_id, 0);
     seed_id_uint64_t_flag = true;
     } else if (v_typeIDVar == _schema_VTY_Forum) {
       seed_id_uint64_t = v_attr->GetUInt(_schema_VATT_Forum_576037_id, 0);
     seed_id_uint64_t_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("id");
writer.WriteUnsignedInt(( seed_id_uint64_t));

writer.WriteNameString("tag");
writer.WriteString(( v_val->tag_name_1.get(( 0l))));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function Write_5_seed v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_3 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function EdgeMap_3 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_Tag)) return;

   //attributes' local var declaration
   string t_name_string = string();
   bool t_name_string_flag = false;

   //get tgt's attribute
   int tgt_typeIDVar = context->GraphAPI()->GetVertexType(tgt);
     if (tgt_typeIDVar == _schema_VTY_Tag) {
       t_name_string = tgt_attr->GetString(_schema_VATT_Tag_576037_name);
     t_name_string_flag = true;
     }


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   if (t_name_string_flag) {
  src_delta.tag_name_1 += ( t_name_string);
     src_delta.__GQUERY__hasChanged___576037tag_name_1 = true;
   }



   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function EdgeMap_3 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_3 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function Reduce_3 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function Reduce_3 v: " << v << std::endl;
}
void EdgeMap_4 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function EdgeMap_4 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_Person)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   tgt_delta.cnt_1 += ( 1l);
   tgt_delta.__GQUERY__hasChanged___576037cnt_1 = true;


   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function EdgeMap_4 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_4 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function Reduce_4 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function Reduce_4 v: " << v << std::endl;
}


void (UDF_test::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_test::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("test");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_test DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

{
  const VERTEX& _activate_v = HF_retrieve_param_856409387<VERTEX>(context, GV_PARAM_inputv, GV_SYS_inputv_flag, "inputv");
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}

                 PC = 3;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "seed";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_seed_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_seed_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(23L, "seed", 4);
                     timer_.saveVSetCode(23L, "seed = {inputv};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_seed_LASTSET) = 23L;
                   }
                 break;

           PC = 3; break;
         }

         case 3:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "seed") {
                     context->StoreBitSets("seed", *context->GetBitSets());
                 }

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_HAS_TAG);

                 edgemap   = &UDF_test::EdgeMap_3;
                 context->set_udfedgemapsetting(2);

                 reduce    = &UDF_test::Reduce_3;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "tagss";
                 PC = 4;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "tagss", 5);
                     timer_.saveVSetCode(30L, "tagss = SELECT t\n          FROM seed:s-(HAS_TAG:e)-> Tag:t\n          ACCUM s.@tag_name+=t.name;");
                     timer_.addDependency(30L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_seed_LASTSET));
                     timer_.start("tagss", 5, context->CalcActiveVertexCount(), 30L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_tagss_LASTSET) = 30L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_tagss_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_tagss_ORDERBY) = -1;
                   break;
                 }

           PC = 4; break;
         }

         case 4:
         {
                 gutil::BitSets& __GQUERY__bs_seed = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "seed") { 
                     __GQUERY__bs_seed = *context->GetBitSets();
                 } else if (context->HasBitSets("seed")) {
                     context->LoadBitSets("seed", __GQUERY__bs_seed);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_seed);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_seed;

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_HAS_MEMBER);

                 edgemap   = &UDF_test::EdgeMap_4;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_test::Reduce_4;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "persons";
                 PC = 5;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_test INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(57L, "persons", 8);
                     timer_.saveVSetCode(57L, "persons = SELECT t\n            FROM seed:s-(HAS_MEMBER:e)-> Person:t\n            ACCUM t.@cnt += 1;");
                     timer_.addDependency(57L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_seed_LASTSET));
                     timer_.start("persons", 8, context->CalcActiveVertexCount(), 57L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_persons_LASTSET) = 57L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_persons_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_persons_ORDERBY) = -1;
                   break;
                 }

           PC = 5; break;
         }

         case 5:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "persons") {
                     context->StoreBitSets("persons", *context->GetBitSets());
                 }

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 context->StoreBitSets("tmp", *context->GetBitSets());
                 std::string tmpLastActive = context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE);
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
                 gutil::BitSets& __GQUERY__bs_seed = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "seed") { 
                     __GQUERY__bs_seed = *context->GetBitSets();
                 } else if (context->HasBitSets("seed")) {
                     context->LoadBitSets("seed", __GQUERY__bs_seed);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_seed);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_seed;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "seed";

                 write = &UDF_test::Write_5_seed;
                 context->set_udfprintsetting(1);

                 writer.WriteName("seed");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 { //START
                 gutil::BitSets& __GQUERY__bs_persons = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "persons") { 
                     __GQUERY__bs_persons = *context->GetBitSets();
                 } else if (context->HasBitSets("persons")) {
                     context->LoadBitSets("persons", __GQUERY__bs_persons);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_persons);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_persons;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "persons";

                 write = &UDF_test::Write_5_forumPersons;
                 context->set_udfprintsetting(0);

                 writer.WriteName("forumPersons");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 context->LoadBitSets("tmp",*context->GetBitSets() );
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = tmpLastActive;
                 PC = 6;
                 break;

           PC = 6; break;
         }

         case 6:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 3:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_tagss_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_tagss_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 4:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_persons_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_persons_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

private:
   VERTEX _inputv;
   bool inputv_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Comment = 0;
const int _schema_VTY_Post = 1;
const int _schema_VTY_Company = 2;
const int _schema_VTY_University = 3;
const int _schema_VTY_City = 4;
const int _schema_VTY_Country = 5;
const int _schema_VTY_Continent = 6;
const int _schema_VTY_Forum = 7;
const int _schema_VTY_Person = 8;
const int _schema_VTY_Tag = 9;
const int _schema_VTY_TagClass = 10;
const int _schema_ETY_HAS_MEMBER = 6;
const int _schema_ETY_HAS_TAG = 10;
int _schema_VATT_Comment_576037_id = -1;
int _schema_VATT_Post_576037_id = -1;
int _schema_VATT_Company_576037_id = -1;
int _schema_VATT_University_576037_id = -1;
int _schema_VATT_City_576037_id = -1;
int _schema_VATT_Country_576037_id = -1;
int _schema_VATT_Continent_576037_id = -1;
int _schema_VATT_Forum_576037_id = -1;
int _schema_VATT_Person_576037_id = -1;
int _schema_VATT_Tag_576037_id = -1;
int _schema_VATT_Tag_576037_name = -1;
int _schema_VATT_TagClass_576037_id = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_test(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query test in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_test udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "test") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "test") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "test") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_test(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,VERTEX inputv) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_test udf(request, serviceapi, _graphupdates_ , inputv, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_test_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_test(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,VERTEX inputv){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_test(graphAPI, request, serviceapi,_graphupdates_ , inputv);
}
  bool call_test(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query test in all vetext active mode." << std::endl;
  }

if (values.size() != 1) {
    HF_set_error(request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
VERTEX inputv = values[0]->GetVidInternal();


  UDIMPL::ldbc_snb::UDF_test udf(request, serviceapi, graphupdates, inputv, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
