/********** query start ***************
create query p1() for graph poc_graph {
  F0 = {members.*};
  PRINT F0; // no load
  PRINT F0[F0.id]; // no load
  F1 = {members.*};
  PRINT F0; // load
  PRINT F0[F0.id]; // load
  PRINT F1; // no load
  PRINT F1[F1.id]; // no load
  PRINT F1, F0; // load
  PRINT F0[F0.id], F1[F1.id]; // load
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace poc_graph{ 
class UDF_p1 :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_p1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
_schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
_schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_p1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
_schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
_schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_p1 () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_F0_SIZE, GV_SYS_F0_ORDERBY, GV_SYS_F0_LASTSET, GV_SYS_F1_SIZE, GV_SYS_F1_ORDERBY, GV_SYS_F1_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_F0_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_F0_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_F0_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_F1_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_F1_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_F1_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_p1::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_8_F1 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_8_F1 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int F1_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_members != -1 && F1_typeIDVar == _schema_VTY_members)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_members_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_id));
         }
         if (_schema_VATT_members_576037_profileIndustryId != -1) {
         writer.WriteNameString("profileIndustryId");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_profileIndustryId));
         }
         if (_schema_VATT_members_576037_registrationDate != -1) {
         writer.WriteNameString("registrationDate");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_members_576037_registrationDate, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_8_F1 v: " << v << std::endl;
}


void Write_7_F1 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_7_F1 v: " << v << std::endl;

   //attributes' local var declaration
   string F1_id_string = string();
   bool F1_id_string_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_members) {
       F1_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F1_id_string_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("F1.id");
writer.WriteString(( F1_id_string));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_7_F1 v: " << v << std::endl;
}


void Write_9_F1 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_9_F1 v: " << v << std::endl;

   //attributes' local var declaration
   string F0_id_string = string();
   bool F0_id_string_flag = false;
   string F1_id_string = string();
   bool F1_id_string_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_members) {
       F0_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F0_id_string_flag = true;
     F1_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F1_id_string_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("F1.id");
writer.WriteString(( F1_id_string));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_9_F1 v: " << v << std::endl;
}


void Write_8_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_8_F0 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int F0_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_members != -1 && F0_typeIDVar == _schema_VTY_members)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_members_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_id));
         }
         if (_schema_VATT_members_576037_profileIndustryId != -1) {
         writer.WriteNameString("profileIndustryId");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_profileIndustryId));
         }
         if (_schema_VATT_members_576037_registrationDate != -1) {
         writer.WriteNameString("registrationDate");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_members_576037_registrationDate, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_8_F0 v: " << v << std::endl;
}


void Write_9_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_9_F0 v: " << v << std::endl;

   //attributes' local var declaration
   string F0_id_string = string();
   bool F0_id_string_flag = false;
   string F1_id_string = string();
   bool F1_id_string_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_members) {
       F0_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F0_id_string_flag = true;
     F1_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F1_id_string_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("F0.id");
writer.WriteString(( F0_id_string));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_9_F0 v: " << v << std::endl;
}


void Write_4_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_4_F0 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int F0_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_members != -1 && F0_typeIDVar == _schema_VTY_members)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_members_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_id));
         }
         if (_schema_VATT_members_576037_profileIndustryId != -1) {
         writer.WriteNameString("profileIndustryId");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_profileIndustryId));
         }
         if (_schema_VATT_members_576037_registrationDate != -1) {
         writer.WriteNameString("registrationDate");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_members_576037_registrationDate, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_4_F0 v: " << v << std::endl;
}


void Write_5_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_5_F0 v: " << v << std::endl;

   //attributes' local var declaration
   string F0_id_string = string();
   bool F0_id_string_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_members) {
       F0_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F0_id_string_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("F0.id");
writer.WriteString(( F0_id_string));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_5_F0 v: " << v << std::endl;
}


void Write_6_F1 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_6_F1 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int F1_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_members != -1 && F1_typeIDVar == _schema_VTY_members)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_members_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_id));
         }
         if (_schema_VATT_members_576037_profileIndustryId != -1) {
         writer.WriteNameString("profileIndustryId");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_profileIndustryId));
         }
         if (_schema_VATT_members_576037_registrationDate != -1) {
         writer.WriteNameString("registrationDate");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_members_576037_registrationDate, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_6_F1 v: " << v << std::endl;
}


void Write_1_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_1_F0 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int F0_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_members != -1 && F0_typeIDVar == _schema_VTY_members)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_members_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_id));
         }
         if (_schema_VATT_members_576037_profileIndustryId != -1) {
         writer.WriteNameString("profileIndustryId");
         writer.WriteString(v_attr->GetString(_schema_VATT_members_576037_profileIndustryId));
         }
         if (_schema_VATT_members_576037_registrationDate != -1) {
         writer.WriteNameString("registrationDate");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_members_576037_registrationDate, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_1_F0 v: " << v << std::endl;
}


void Write_2_F0 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function Write_2_F0 v: " << v << std::endl;

   //attributes' local var declaration
   string F0_id_string = string();
   bool F0_id_string_flag = false;

   //get v's attribute
   int v_typeIDVar = context->GraphAPI()->GetVertexType(v);
     if (v_typeIDVar == _schema_VTY_members) {
       F0_id_string = v_attr->GetString(_schema_VATT_members_576037_id);
     F0_id_string_flag = true;
     }
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });

   gutil::JSONWriter& writer = *__GQUERY__local_writer;
   writer.WriteStartObject ();
   writer.WriteName ("v_id").WriteMarkVId (v);
   _request.output_idservice_vids.push_back (v);
writer.WriteNameString("v_type");
writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));


   writer.WriteName ("attributes");
   writer.WriteStartObject ();
   writer.WriteNameString("F0.id");
writer.WriteString(( F0_id_string));


   writer.WriteEndObject ();
   writer.WriteEndObject ();
  GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Exit function Write_2_F0 v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}


void (UDF_p1::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_p1::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_p1 INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("p1");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_p1 DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_members,true);

                 PC = 1;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F0";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_F0_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_F0_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(12L, "F0", 2);
                     timer_.saveVSetCode(12L, "F0 = {members.*};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_F0_LASTSET) = 12L;
                   }
                 break;

           PC = 1; break;
         }

         case 1:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_p1::Write_1_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 2;
                 break;

           PC = 2; break;
         }

         case 2:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_p1::Write_2_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 3;
                 break;

           PC = 3; break;
         }

         case 3:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F0") {
                     context->StoreBitSets("F0", *context->GetBitSets());
                 }
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_members,true);

                 PC = 4;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F1";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_F1_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_F1_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "F1", 5);
                     timer_.saveVSetCode(30L, "F1 = {members.*};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_F1_LASTSET) = 30L;
                   }
                 break;

           PC = 4; break;
         }

         case 4:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 context->StoreBitSets("tmp", *context->GetBitSets());
                 std::string tmpLastActive = context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE);
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
                 gutil::BitSets& __GQUERY__bs_F0 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F0") { 
                     __GQUERY__bs_F0 = *context->GetBitSets();
                 } else if (context->HasBitSets("F0")) {
                     context->LoadBitSets("F0", __GQUERY__bs_F0);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F0);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F0;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F0";

                 write = &UDF_p1::Write_4_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 context->LoadBitSets("tmp",*context->GetBitSets() );
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = tmpLastActive;
                 PC = 5;
                 break;

           PC = 5; break;
         }

         case 5:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 context->StoreBitSets("tmp", *context->GetBitSets());
                 std::string tmpLastActive = context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE);
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
                 gutil::BitSets& __GQUERY__bs_F0 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F0") { 
                     __GQUERY__bs_F0 = *context->GetBitSets();
                 } else if (context->HasBitSets("F0")) {
                     context->LoadBitSets("F0", __GQUERY__bs_F0);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F0);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F0;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F0";

                 write = &UDF_p1::Write_5_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 context->LoadBitSets("tmp",*context->GetBitSets() );
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = tmpLastActive;
                 PC = 6;
                 break;

           PC = 6; break;
         }

         case 6:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_p1::Write_6_F1;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F1");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 7;
                 break;

           PC = 7; break;
         }

         case 7:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_p1::Write_7_F1;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F1");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 8;
                 break;

           PC = 8; break;
         }

         case 8:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 context->StoreBitSets("tmp", *context->GetBitSets());
                 std::string tmpLastActive = context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE);
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
                 gutil::BitSets& __GQUERY__bs_F1 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F1") { 
                     __GQUERY__bs_F1 = *context->GetBitSets();
                 } else if (context->HasBitSets("F1")) {
                     context->LoadBitSets("F1", __GQUERY__bs_F1);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F1);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F1;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F1";

                 write = &UDF_p1::Write_8_F1;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F1");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 { //START
                 gutil::BitSets& __GQUERY__bs_F0 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F0") { 
                     __GQUERY__bs_F0 = *context->GetBitSets();
                 } else if (context->HasBitSets("F0")) {
                     context->LoadBitSets("F0", __GQUERY__bs_F0);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F0);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F0;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F0";

                 write = &UDF_p1::Write_8_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 context->LoadBitSets("tmp",*context->GetBitSets() );
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = tmpLastActive;
                 PC = 9;
                 break;

           PC = 9; break;
         }

         case 9:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F1") {
                     context->StoreBitSets("F1", *context->GetBitSets());
                 }

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 context->StoreBitSets("tmp", *context->GetBitSets());
                 std::string tmpLastActive = context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE);
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
                 gutil::BitSets& __GQUERY__bs_F0 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F0") { 
                     __GQUERY__bs_F0 = *context->GetBitSets();
                 } else if (context->HasBitSets("F0")) {
                     context->LoadBitSets("F0", __GQUERY__bs_F0);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F0);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F0;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F0";

                 write = &UDF_p1::Write_9_F0;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F0");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 { //START
                 gutil::BitSets& __GQUERY__bs_F1 = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "F1") { 
                     __GQUERY__bs_F1 = *context->GetBitSets();
                 } else if (context->HasBitSets("F1")) {
                     context->LoadBitSets("F1", __GQUERY__bs_F1);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_F1);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_F1;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "F1";

                 write = &UDF_p1::Write_9_F1;
                 context->set_udfprintsetting(1);

                 writer.WriteName("F1");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 context->LoadBitSets("tmp",*context->GetBitSets() );
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = tmpLastActive;
                 PC = 10;
                 break;

           PC = 10; break;
         }

         case 10:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_members = 1;
int _schema_VATT_members_576037_id = -1;
int _schema_VATT_members_576037_profileIndustryId = -1;
int _schema_VATT_members_576037_registrationDate = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_p1(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query p1 in all vetext active mode." << std::endl;
    }
    UDIMPL::poc_graph::UDF_p1 udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "p1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "p1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "p1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "p1") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_p1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::poc_graph::UDF_p1 udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_p1_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_p1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_p1(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_p1(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query p1 in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::poc_graph::UDF_p1 udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
