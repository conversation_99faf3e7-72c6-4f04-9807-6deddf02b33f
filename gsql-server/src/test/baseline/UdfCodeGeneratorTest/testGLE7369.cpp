/********** query start ***************
CREATE QUERY testGLE7369(SET<VERTEX<Post>> post_ids, SET<VERTEX<Person>> person_ids) {
    IF person_ids.size() != 0 AND post_ids.size() == 0 THEN
        personSet = {person_ids};
    ELSE IF person_ids.size() == 0 AND post_ids.size() != 0 THEN
        postSet = {post_ids};
        personSet = SELECT t FROM postSet -(HAS_CREATOR>)- Person:t;
    END;
    result = SELECT t 
        FROM personSet -(IS_LOCATED_IN>:e1) - City:t -(IS_PART_OF>:e2)- Country:ev;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_testGLE7369 :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:
   OrAccum<bool >  matches_at_t_1;
   bool __GQUERY__hasChanged___576037matches_at_t_1;
   bool __GQUERY__set___576037matches_at_t_1;

   SetAccum<VERTEX >  propagAcc_t_to_ev_1;
   bool __GQUERY__hasChanged___576037propagAcc_t_to_ev_1;
   bool __GQUERY__set___576037propagAcc_t_to_ev_1;

   SetAccum<VERTEX >  propagAcc_x1_to_t_1;
   bool __GQUERY__hasChanged___576037propagAcc_x1_to_t_1;
   bool __GQUERY__set___576037propagAcc_x1_to_t_1;

   SetAccum<VERTEX >  matches_at_x1_1;
   bool __GQUERY__hasChanged___576037matches_at_x1_1;
   bool __GQUERY__set___576037matches_at_x1_1;


   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

      __GQUERY__hasChanged___576037matches_at_t_1 = false;
      __GQUERY__set___576037matches_at_t_1 = false;
      __GQUERY__hasChanged___576037propagAcc_t_to_ev_1 = false;
      __GQUERY__set___576037propagAcc_t_to_ev_1 = false;
      __GQUERY__hasChanged___576037propagAcc_x1_to_t_1 = false;
      __GQUERY__set___576037propagAcc_x1_to_t_1 = false;
      __GQUERY__hasChanged___576037matches_at_x1_1 = false;
      __GQUERY__set___576037matches_at_x1_1 = false;
   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037matches_at_t_1) {
        matches_at_t_1 += __GQUERY__other__576037.matches_at_t_1;
        __GQUERY__hasChanged___576037matches_at_t_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037matches_at_t_1) {
        matches_at_t_1 = __GQUERY__other__576037.matches_at_t_1;
        __GQUERY__set___576037matches_at_t_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037propagAcc_t_to_ev_1) {
        propagAcc_t_to_ev_1 += __GQUERY__other__576037.propagAcc_t_to_ev_1;
        __GQUERY__hasChanged___576037propagAcc_t_to_ev_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037propagAcc_t_to_ev_1) {
        propagAcc_t_to_ev_1 = __GQUERY__other__576037.propagAcc_t_to_ev_1;
        __GQUERY__set___576037propagAcc_t_to_ev_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037propagAcc_x1_to_t_1) {
        propagAcc_x1_to_t_1 += __GQUERY__other__576037.propagAcc_x1_to_t_1;
        __GQUERY__hasChanged___576037propagAcc_x1_to_t_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037propagAcc_x1_to_t_1) {
        propagAcc_x1_to_t_1 = __GQUERY__other__576037.propagAcc_x1_to_t_1;
        __GQUERY__set___576037propagAcc_x1_to_t_1 = true;
      }

      if (__GQUERY__other__576037.__GQUERY__hasChanged___576037matches_at_x1_1) {
        matches_at_x1_1 += __GQUERY__other__576037.matches_at_x1_1;
        __GQUERY__hasChanged___576037matches_at_x1_1 = true;
      } else if (__GQUERY__other__576037.__GQUERY__set___576037matches_at_x1_1) {
        matches_at_x1_1 = __GQUERY__other__576037.matches_at_x1_1;
        __GQUERY__set___576037matches_at_x1_1 = true;
      }

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:
   OrAccum<bool >  matches_at_t_1;
   SetAccum<VERTEX >  propagAcc_t_to_ev_1;
   SetAccum<VERTEX >  propagAcc_x1_to_t_1;
   SetAccum<VERTEX >  matches_at_x1_1;

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
      matches_at_t_1 = __GQUERY__other__576037.matches_at_t_1;
      propagAcc_t_to_ev_1 = __GQUERY__other__576037.propagAcc_t_to_ev_1;
      propagAcc_x1_to_t_1 = __GQUERY__other__576037.propagAcc_x1_to_t_1;
      matches_at_x1_1 = __GQUERY__other__576037.matches_at_x1_1;
   }

   // serialise interface
   template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037(matches_at_t_1, propagAcc_t_to_ev_1, propagAcc_x1_to_t_1, matches_at_x1_1);
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037matches_at_t_1) matches_at_t_1 += __GQUERY__d__576037.matches_at_t_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037matches_at_t_1) matches_at_t_1 = __GQUERY__d__576037.matches_at_t_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037propagAcc_t_to_ev_1) propagAcc_t_to_ev_1 += __GQUERY__d__576037.propagAcc_t_to_ev_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037propagAcc_t_to_ev_1) propagAcc_t_to_ev_1 = __GQUERY__d__576037.propagAcc_t_to_ev_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037propagAcc_x1_to_t_1) propagAcc_x1_to_t_1 += __GQUERY__d__576037.propagAcc_x1_to_t_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037propagAcc_x1_to_t_1) propagAcc_x1_to_t_1 = __GQUERY__d__576037.propagAcc_x1_to_t_1;

      if (__GQUERY__d__576037.__GQUERY__hasChanged___576037matches_at_x1_1) matches_at_x1_1 += __GQUERY__d__576037.matches_at_x1_1;
      else if (__GQUERY__d__576037.__GQUERY__set___576037matches_at_x1_1) matches_at_x1_1 = __GQUERY__d__576037.matches_at_x1_1;

      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef std::shared_ptr <__GQUERY__VertexVal__576037> V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_testGLE7369 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
    if (request.jsoptions_.isMember("post_ids")) {
      VertexLocalId_t localId;
      for (auto i = 0; i < request.jsoptions_["post_ids"].size(); i++) {
        if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
          _post_ids += (std::atoll(request.jsoptions_["post_ids"][i]["id"].asString().c_str()));
        } else {
          std::stringstream ss;
          ss << _schema_VTY_Post;
          ss << "_" << request.jsoptions_["post_ids"][i]["id"].asString();
          if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
            _post_ids += VERTEX(localId);
          } else {
            std::string msg("Warning: vertex set post_ids contains invalid vertex ids. They will be removed from the set.");
            HF_set_error(request, msg, false);
          }
        }
      }
      post_ids_flag = true;
    } else {
      post_ids_flag = false;
    }
    if (request.jsoptions_.isMember("person_ids")) {
      VertexLocalId_t localId;
      for (auto i = 0; i < request.jsoptions_["person_ids"].size(); i++) {
        if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
          _person_ids += (std::atoll(request.jsoptions_["person_ids"][i]["id"].asString().c_str()));
        } else {
          std::stringstream ss;
          ss << _schema_VTY_Person;
          ss << "_" << request.jsoptions_["person_ids"][i]["id"].asString();
          if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
            _person_ids += VERTEX(localId);
          } else {
            std::string msg("Warning: vertex set person_ids contains invalid vertex ids. They will be removed from the set.");
            HF_set_error(request, msg, false);
          }
        }
      }
      person_ids_flag = true;
    } else {
      person_ids_flag = false;
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_testGLE7369 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , SetAccum<VERTEX >  post_ids, SetAccum<VERTEX >  person_ids, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_post_ids = post_ids;
post_ids_flag = true;
_person_ids = person_ids;
person_ids_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_testGLE7369 () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_post_ids, GV_SYS_post_ids_flag, GV_PARAM_person_ids, GV_SYS_person_ids_flag, GV_GV_i_1, GV_GACC_VSAcc_ev_1, GV_GACC_VSAcc_t_1, GV_GACC_VSAcc_x1_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_VS_ev_SIZE, GV_SYS_VS_ev_ORDERBY, GV_SYS_VS_ev_LASTSET, GV_SYS_result_SIZE, GV_SYS_result_ORDERBY, GV_SYS_result_LASTSET, GV_SYS_personSet_SIZE, GV_SYS_personSet_ORDERBY, GV_SYS_personSet_LASTSET, GV_SYS_VS_x1_SIZE, GV_SYS_VS_x1_ORDERBY, GV_SYS_VS_x1_LASTSET, GV_SYS_VS_t_SIZE, GV_SYS_VS_t_ORDERBY, GV_SYS_VS_t_LASTSET, GV_SYS_postSet_SIZE, GV_SYS_postSet_ORDERBY, GV_SYS_postSet_LASTSET, GV_SYS_VS_t_filtered_SIZE, GV_SYS_VS_t_filtered_ORDERBY, GV_SYS_VS_t_filtered_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_VS_ev_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_ev_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS_ev_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_result_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_result_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_result_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_personSet_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_personSet_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_personSet_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_x1_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_x1_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS_x1_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_t_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_t_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS_t_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_postSet_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_postSet_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_postSet_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_t_filtered_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VS_t_filtered_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_VS_t_filtered_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_post_ids, new  gpelib4::SumVariable<SetAccum<VERTEX > > (_post_ids));
   gvs->Register (GV_SYS_post_ids_flag, new  gpelib4::BroadcastVariable<bool> (post_ids_flag));
   gvs->Register (GV_PARAM_person_ids, new  gpelib4::SumVariable<SetAccum<VERTEX > > (_person_ids));
   gvs->Register (GV_SYS_person_ids_flag, new  gpelib4::BroadcastVariable<bool> (person_ids_flag));

   // global variables
   gvs->Register (GV_GV_i_1, new gpelib4::StateVariable<int64_t> (int64_t()));

   // loop indices

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_VSAcc_ev_1, new  gpelib4::SumVariable<SetAccum<VERTEX > > (SetAccum<VERTEX >  ()));
   gvs->Register (GV_GACC_VSAcc_t_1, new  gpelib4::SumVariable<SetAccum<VERTEX > > (SetAccum<VERTEX >  ()));
   gvs->Register (GV_GACC_VSAcc_x1_1, new  gpelib4::SumVariable<SetAccum<VERTEX > > (SetAccum<VERTEX >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_testGLE7369::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void VertexMap_16 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function VertexMap_16 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->propagAcc_t_to_ev_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_t = *it;
   __GQUERY__Delta__576037 t_other_delta = __GQUERY__Delta__576037 ();
   t_other_delta.__GQUERY__isOther__576037 = true;

   t_other_delta.matches_at_t_1 += ( true);
   t_other_delta.__GQUERY__hasChanged___576037matches_at_t_1 = true;
   context->GlobalVariable_Reduce<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1, SetAccum<VERTEX >  (( foreach_t)));   context->Write (foreach_t, t_other_delta);

if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function VertexMap_16 src: " << src << std::endl;
}

void Reduce_16 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_16 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->propagAcc_t_to_ev_1.clear();
  
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_16 v: " << v << std::endl;
}
void VertexMap_19 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function VertexMap_19 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function VertexMap_19 src: " << src << std::endl;
}

void Reduce_19 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_19 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->propagAcc_x1_to_t_1.clear();
  
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_19 v: " << v << std::endl;
}
void VertexMap_22 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function VertexMap_22 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->propagAcc_x1_to_t_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_x1 = *it;

   if (( src_val->matches_at_t_1.data_)) {
   __GQUERY__Delta__576037 x1_other_delta = __GQUERY__Delta__576037 ();
   x1_other_delta.__GQUERY__isOther__576037 = true;

   x1_other_delta.matches_at_x1_1 += ( (src));
   x1_other_delta.__GQUERY__hasChanged___576037matches_at_x1_1 = true;   context->Write (foreach_x1, x1_other_delta);

  }
   context->GlobalVariable_Reduce<SetAccum<VERTEX > > (GV_GACC_VSAcc_x1_1, SetAccum<VERTEX >  (( foreach_x1)));
if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function VertexMap_22 src: " << src << std::endl;
}

void Reduce_22 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_22 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->propagAcc_x1_to_t_1.clear();
  
  
   }

   if (delta.__GQUERY__isSrc__576037) {
  l_val->matches_at_t_1 = ( false);
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_22 v: " << v << std::endl;
}
void VertexMap_25 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function VertexMap_25 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   {
uint64_t __GQUERY__step_1 = 0;
const auto& __GQUERY__stepvar_1 = src_val->matches_at_x1_1;
  for (auto it = (__GQUERY__stepvar_1).begin(); it != (__GQUERY__stepvar_1).end(); it++) {
    auto& foreach_t__ = *it;

   context->GlobalVariable_Reduce<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1, SetAccum<VERTEX >  (( foreach_t__)));
if (UNLIKELY((++__GQUERY__step_1 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  return;
}
  }
}



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function VertexMap_25 src: " << src << std::endl;
}

void Reduce_25 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_25 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;

   //attributes' local var declaration




if (delta.__GQUERY__isSrc__576037|| delta.__GQUERY__isTgt__576037) {
   // post-accum

   if (delta.__GQUERY__isSrc__576037) {
  
  l_val->matches_at_x1_1.clear();
  
  
   }
}

   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_25 v: " << v << std::endl;
}
void EdgeMap_12 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function EdgeMap_12 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_Person)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function EdgeMap_12 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_12 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_12 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_12 v: " << v << std::endl;
}
void EdgeMap_14 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function EdgeMap_14 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_City)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   tgt_delta.propagAcc_x1_to_t_1 += ( src);
   tgt_delta.__GQUERY__hasChanged___576037propagAcc_x1_to_t_1 = true;


   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function EdgeMap_14 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_14 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_14 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_14 v: " << v << std::endl;
}
void EdgeMap_15 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function EdgeMap_15 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(tgt) == _schema_VTY_Country)) return;

   //attributes' local var declaration




   // prepare messages
   __GQUERY__Delta__576037 tgt_delta = __GQUERY__Delta__576037 ();
   tgt_delta.__GQUERY__isTgt__576037 = true;

   // ACCUM
   

   tgt_delta.propagAcc_t_to_ev_1 += ( src);
   tgt_delta.__GQUERY__hasChanged___576037propagAcc_t_to_ev_1 = true;


   // SELECT
   tgt_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (tgt, tgt_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function EdgeMap_15 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_15 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function Reduce_15 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):
   V_VALUE l_val = V_VALUE(new __GQUERY__VertexVal__576037(*v_val));
   (*l_val) += delta;


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   // Overwrite vertex value and activate if appropriate
   context->Write (v, l_val, __GQUERY__activate__576037);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function Reduce_15 v: " << v << std::endl;
}


void (UDF_testGLE7369::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_testGLE7369::*vertexmap) (const VERTEX& src,
                   V_ATTR*                src_attr,
                   const V_VALUE&         src_val,
                   gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void VertexMap (const VertexLocalId_t& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(vertexmap)) (VERTEX(src), src_attr, src_val, ctx);
}



void (UDF_testGLE7369::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("testGLE7369");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_testGLE7369 DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                 PC = 4;
                 break;
           PC = 4; break;
         }

         case 4:
         {

                 PC = 5;
                 break;
           PC = 5; break;
         }

         case 5:
         {

                     context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_ev_1).clear();
                 PC = 6;
                 break;
           PC = 6; break;
         }

         case 6:
         {

                     context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1).clear();
                 PC = 7;
                 break;
           PC = 7; break;
         }

         case 7:
         {

                     context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_x1_1).clear();
                 PC = 8;
                 break;
           PC = 8; break;
         }

         case 8:
         {

                 if (!(((context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_PARAM_person_ids).size()) != (0l) 
&& (context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_PARAM_post_ids).size()) == (0l)))) {
                   PC = 10;
                   break;
                 }
           PC = 9; break;
         }

         case 9:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter = HF_retrieve_param_856409387<SetAccum<VERTEX > >(context, GV_PARAM_person_ids, GV_SYS_person_ids_flag, "person_ids").begin(); iter!=HF_retrieve_param_856409387<SetAccum<VERTEX > >(context, GV_PARAM_person_ids, GV_SYS_person_ids_flag, "person_ids").end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 13;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "personSet";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_personSet_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(359L, "personSet", 18);
                     timer_.saveVSetCode(359L, "personSet = {person_ids};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_LASTSET) = 359L;
                   }
                 break;

           PC = 10; break;
         }

         case 10:
         {

                 if (!(((context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_PARAM_person_ids).size()) == (0l) 
&& (context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_PARAM_post_ids).size()) != (0l)))) {
                   PC = 13;
                   break;
                 }
           PC = 11; break;
         }

         case 11:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter = HF_retrieve_param_856409387<SetAccum<VERTEX > >(context, GV_PARAM_post_ids, GV_SYS_post_ids_flag, "post_ids").begin(); iter!=HF_retrieve_param_856409387<SetAccum<VERTEX > >(context, GV_PARAM_post_ids, GV_SYS_post_ids_flag, "post_ids").end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 12;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "postSet";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_postSet_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_postSet_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(414L, "postSet", 20);
                     timer_.saveVSetCode(414L, "postSet = {post_ids};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_postSet_LASTSET) = 414L;
                   }
                 break;

           PC = 12; break;
         }

         case 12:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_HAS_CREATOR);

                 edgemap   = &UDF_testGLE7369::EdgeMap_12;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_testGLE7369::Reduce_12;
                 context->set_udfreducesetting(0);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "personSet";
                 PC = 13;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(253L, "personSet", 22);
                     timer_.saveVSetCode(253L, "personSet =\n\tselect t\n\tfrom postSet:x -(HAS_CREATOR>:x_1)- Person:t;");
                     timer_.addDependency(253L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_postSet_LASTSET));
                     timer_.start("personSet", 22, context->CalcActiveVertexCount(), 253L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_LASTSET) = 253L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_personSet_ORDERBY) = -1;
                   break;
                 }

           PC = 13; break;
         }

         case 13:
         {
                 gutil::BitSets __GQUERY__bs_personSet;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) != "personSet") {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_personSet);
                 } else {
                     __GQUERY__bs_personSet = *context->GetBitSets();
                 }
                 *context->GetBitSets() = __GQUERY__bs_personSet;


                 PC = 14;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_x1";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_x1_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(465L, "VS_x1", 30);
                     timer_.saveVSetCode(465L, "VS_x1 = personSet;");
                     timer_.addDependency(465L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_LASTSET));
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_LASTSET) = 465L;
                   }
                 break;

           PC = 14; break;
         }

         case 14:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_IS_LOCATED_IN);

                 edgemap   = &UDF_testGLE7369::EdgeMap_14;
                 context->set_udfedgemapsetting(1);

                 reduce    = &UDF_testGLE7369::Reduce_14;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t";
                 PC = 15;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(595L, "VS_t", 34);
                     timer_.saveVSetCode(595L, "VS_t =\n\tselect t\n\tfrom VS_x1:x1 -(IS_LOCATED_IN>:e1)- City:t\n\taccum        t.@propagAcc_x1_to_t += x1\n\t\t;");
                     timer_.addDependency(595L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_LASTSET));
                     timer_.start("VS_t", 34, context->CalcActiveVertexCount(), 595L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET) = 595L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
                   break;
                 }

           PC = 15; break;
         }

         case 15:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "VS_t") {
                     context->StoreBitSets("VS_t", *context->GetBitSets());
                 }

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_IS_PART_OF);

                 edgemap   = &UDF_testGLE7369::EdgeMap_15;
                 context->set_udfedgemapsetting(1);

                 reduce    = &UDF_testGLE7369::Reduce_15;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_ev";
                 PC = 16;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(903L, "VS_ev", 42);
                     timer_.saveVSetCode(903L, "VS_ev =\n\tselect ev\n\tfrom VS_t:t -(IS_PART_OF>:e2)- Country:ev\n\taccum        ev.@propagAcc_t_to_ev += t\n\t\t;");
                     timer_.addDependency(903L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET));
                     timer_.start("VS_ev", 42, context->CalcActiveVertexCount(), 903L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_LASTSET) = 903L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_ev_ORDERBY) = -1;
                   break;
                 }

           PC = 16; break;
         }

         case 16:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_testGLE7369::VertexMap_16;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_testGLE7369::Reduce_16;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_ev";
                 PC = 17;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(1275L, "VS_ev", 50);
                     timer_.saveVSetCode(1275L, "VS_ev = \n\tSELECT ev\n\tFROM   VS_ev:ev\n\tACCUM  foreach t in ev.@propagAcc_t_to_ev do\n\t         t.@matches_at_t += true,\n\t         @@VSAcc_t += t\n\t       end\n\tPOST-ACCUM ev.@propagAcc_t_to_ev.clear();");
                     timer_.addDependency(1275L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_LASTSET));
                     timer_.start("VS_ev", 50, context->CalcActiveVertexCount(), 1275L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_LASTSET) = 1275L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_ev_ORDERBY) = -1;
                   break;
                 }

           PC = 17; break;
         }

         case 17:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).begin(); iter!=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    // verify type of vertex to activate
    int _activate_vTypeID = context->GraphAPI()->GetVertexType(_activate_v);
    if (_activate_vTypeID != _schema_VTY_City) {
      std::string msg("Runtime Error: trying to activate vid of invalid vertex type for vertex set VS_t_filtered. The required types are: [City]. The provided type is " + context->GraphAPI()->GetVertexTypeName(_activate_v) + ".");
      HF_set_error(_request, msg, true);
      context->Abort();
      return;
    }
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 18;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t_filtered";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_filtered_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(1830L, "VS_t_filtered", 60);
                     timer_.saveVSetCode(1830L, "VS_t_filtered(City) = {@@VSAcc_t};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_LASTSET) = 1830L;
                   }
                 break;

           PC = 18; break;
         }

         case 18:
         {
                 gutil::BitSets& __GQUERY__bs_VS_t_filtered = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) != "VS_t_filtered") {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_VS_t_filtered);
                 } else {
                     __GQUERY__bs_VS_t_filtered = *context->GetBitSets();
                 }
                 gutil::BitSets __GQUERY__bs_VS_t;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "VS_t") { 
                     __GQUERY__bs_VS_t = *context->GetBitSets();
                 } else if (context->HasBitSets("VS_t")) {
                     context->LoadBitSets("VS_t", __GQUERY__bs_VS_t);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_VS_t);
                 } 

                 *context->GetBitSets() = (__GQUERY__bs_VS_t_filtered - __GQUERY__bs_VS_t);


                 PC = 19;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t_filtered";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_filtered_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(1891L, "VS_t_filtered", 61);
                     timer_.saveVSetCode(1891L, "VS_t_filtered = VS_t_filtered minus VS_t;");
                     timer_.addDependency(1891L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET));
                     timer_.addDependency(1891L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_LASTSET));
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_LASTSET) = 1891L;
                   }
                 break;

           PC = 19; break;
         }

         case 19:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_testGLE7369::VertexMap_19;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_testGLE7369::Reduce_19;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t_filtered";
                 PC = 20;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(1953L, "VS_t_filtered", 62);
                     timer_.saveVSetCode(1953L, "VS_t_filtered = SELECT t FROM VS_t_filtered:t POST-ACCUM t.@propagAcc_x1_to_t.clear();");
                     timer_.addDependency(1953L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_LASTSET));
                     timer_.start("VS_t_filtered", 62, context->CalcActiveVertexCount(), 1953L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_LASTSET) = 1953L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_filtered_ORDERBY) = -1;
                   break;
                 }

           PC = 20; break;
         }

         case 20:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).begin(); iter!=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    // verify type of vertex to activate
    int _activate_vTypeID = context->GraphAPI()->GetVertexType(_activate_v);
    if (_activate_vTypeID != _schema_VTY_City) {
      std::string msg("Runtime Error: trying to activate vid of invalid vertex type for vertex set VS_t. The required types are: [City]. The provided type is " + context->GraphAPI()->GetVertexTypeName(_activate_v) + ".");
      HF_set_error(_request, msg, true);
      context->Abort();
      return;
    }
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 21;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(2080L, "VS_t", 64);
                     timer_.saveVSetCode(2080L, "VS_t(City) = {@@VSAcc_t};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET) = 2080L;
                   }
                 break;

           PC = 21; break;
         }

         case 21:
         {

                     ( (context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).clear());
;
;
                     PC = 22;
                     break;

           PC = 22; break;
         }

         case 22:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_testGLE7369::VertexMap_22;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_testGLE7369::Reduce_22;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t";
                 PC = 23;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(2346L, "VS_t", 68);
                     timer_.saveVSetCode(2346L, "VS_t = \n\tSELECT t\n\tFROM   VS_t:t\n\tACCUM  foreach x1 in t.@propagAcc_x1_to_t do\n\t           if t.@matches_at_t then\n\t              x1.@matches_at_x1 += (t)\n\t           end,\n\t         @@VSAcc_x1 += x1\n\t       end\n\tPOST-ACCUM t.@propagAcc_x1_to_t.clear(),\n\t           t.@matches_at_t = false;");
                     timer_.addDependency(2346L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET));
                     timer_.start("VS_t", 68, context->CalcActiveVertexCount(), 2346L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET) = 2346L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
                   break;
                 }

           PC = 23; break;
         }

         case 23:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_x1_1)).begin(); iter!=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_x1_1)).end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    // verify type of vertex to activate
    int _activate_vTypeID = context->GraphAPI()->GetVertexType(_activate_v);
    if (_activate_vTypeID != _schema_VTY_Person) {
      std::string msg("Runtime Error: trying to activate vid of invalid vertex type for vertex set VS_x1. The required types are: [Person]. The provided type is " + context->GraphAPI()->GetVertexTypeName(_activate_v) + ".");
      HF_set_error(_request, msg, true);
      context->Abort();
      return;
    }
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 24;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_x1";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_x1_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(3240L, "VS_x1", 80);
                     timer_.saveVSetCode(3240L, "VS_x1(Person) = {@@VSAcc_x1};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_LASTSET) = 3240L;
                   }
                 break;

           PC = 24; break;
         }

         case 24:
         {

                     ( (context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_x1_1)).clear());
;
;
                     PC = 25;
                     break;

           PC = 25; break;
         }

         case 25:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_VertexMap);
                 vertexmap = &UDF_testGLE7369::VertexMap_25;
                 context->set_udfvertexmapsetting(0);

                 reduce    = &UDF_testGLE7369::Reduce_25;
                 context->set_udfreducesetting(2);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_x1";
                 PC = 26;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGLE7369 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(3655L, "VS_x1", 85);
                     timer_.saveVSetCode(3655L, "VS_x1 = \n\tSELECT x1\n\tFROM   VS_x1:x1\n\tACCUM  foreach t__ in x1.@matches_at_x1 do\n\t       \n\t\t      @@VSAcc_t += t__\n\t         \n\t       end\n\tPOST-ACCUM x1.@matches_at_x1.clear();");
                     timer_.addDependency(3655L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_LASTSET));
                     timer_.start("VS_x1", 85, context->CalcActiveVertexCount(), 3655L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_LASTSET) = 3655L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_x1_ORDERBY) = -1;
                   break;
                 }

           PC = 26; break;
         }

         case 26:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 for (auto iter=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).begin(); iter!=(context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).end();iter++) {
{
  const VERTEX& _activate_v = *iter;
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    // verify type of vertex to activate
    int _activate_vTypeID = context->GraphAPI()->GetVertexType(_activate_v);
    if (_activate_vTypeID != _schema_VTY_City) {
      std::string msg("Runtime Error: trying to activate vid of invalid vertex type for vertex set VS_t. The required types are: [City]. The provided type is " + context->GraphAPI()->GetVertexTypeName(_activate_v) + ".");
      HF_set_error(_request, msg, true);
      context->Abort();
      return;
    }
    context->SetActiveFlag (_activate_v);
  }
}
                 }


                 PC = 27;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "VS_t";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(4465L, "VS_t", 94);
                     timer_.saveVSetCode(4465L, "VS_t(City) = {@@VSAcc_t};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET) = 4465L;
                   }
                 break;

           PC = 27; break;
         }

         case 27:
         {

                     ( (context->GlobalVariable_GetValue<SetAccum<VERTEX > > (GV_GACC_VSAcc_t_1)).clear());
;
;
                     PC = 28;
                     break;

           PC = 28; break;
         }

         case 28:
         {


                 PC = 29;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "result";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_result_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(4656L, "result", 96);
                     timer_.saveVSetCode(4656L, "result  = VS_t;");
                     timer_.addDependency(4656L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_LASTSET));
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_LASTSET) = 4656L;
                   }
                 break;

           PC = 29; break;
         }

         case 29:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 12:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_personSet_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_personSet_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 13:
     {
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 14:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 15:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_ev_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 16:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_ev_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_ev_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 18:
     {
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 19:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_filtered_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_filtered_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 22:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_t_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_t_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 25:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_VS_x1_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_VS_x1_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 28:
     {
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval (new __GQUERY__VertexVal__576037 ());
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

private:
   SetAccum<VERTEX> _post_ids;
   bool post_ids_flag;
   SetAccum<VERTEX> _person_ids;
   bool person_ids_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Post = 1;
const int _schema_VTY_City = 4;
const int _schema_VTY_Country = 5;
const int _schema_VTY_Person = 8;
const int _schema_ETY_HAS_CREATOR = 2;
const int _schema_ETY_IS_LOCATED_IN = 14;
const int _schema_ETY_IS_PART_OF = 16;
   V_VALUE* vvalptr = nullptr;
};

  bool call_testGLE7369(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query testGLE7369 in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_testGLE7369 udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "testGLE7369") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testGLE7369") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testGLE7369") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "testGLE7369") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_testGLE7369(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,SetAccum<VERTEX >  post_ids,SetAccum<VERTEX >  person_ids) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_testGLE7369 udf(request, serviceapi, _graphupdates_ , post_ids, person_ids, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_testGLE7369_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_testGLE7369(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,SetAccum<VERTEX >  post_ids,SetAccum<VERTEX >  person_ids){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_testGLE7369(graphAPI, request, serviceapi,_graphupdates_ , post_ids, person_ids);
}
  bool call_testGLE7369(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query testGLE7369 in all vetext active mode." << std::endl;
  }

if (values.size() != 2) {
    HF_set_error(request, "Invalid parameter size: 2|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
SetAccum<VERTEX >  post_ids;
{
  interpret::ValuePtr it_0 = values[0]->Begin();
  while (it_0->NEInternal(values[0]->End())) {
VERTEX var_0 = it_0->GetValue()->GetVidInternal();
post_ids += var_0;
  it_0->Next();
  }
}

SetAccum<VERTEX >  person_ids;
{
  interpret::ValuePtr it_0 = values[1]->Begin();
  while (it_0->NEInternal(values[1]->End())) {
VERTEX var_0 = it_0->GetValue()->GetVidInternal();
person_ids += var_0;
  it_0->Next();
  }
}


  UDIMPL::ldbc_snb::UDF_testGLE7369 udf(request, serviceapi, graphupdates, post_ids, person_ids, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
