/********** query start ***************
CREATE distributed QUERY dell_12(STRING vType, String attr, String attrType)
 FOR GRAPH ldbc_snb {
  ListAccum<INT> @@resint;
  ListAccum<FLOAT> @@resfloat;
  ListAccum<DOUBLE> @@resdouble;
  Start={vType};
  result= select s from Start:s-(_:e)->_:t
  WHERE e.classYear == 1999
  ACCUM
    CASE attrType WHEN "INT" THEN
        @@resint += e.getAttr(attr, "INT")
    WHEN "FLOAT" THEN
        @@resfloat += e.getAttr(attr, "FLOAT")
    WHEN "DOUBLE" THEN
        @@resdouble += e.getAttr(attr, "DOUBLE")
    END;
  print @@resint;
  print @@resfloat;
  print @@resdouble;
}
********** query end ***************/
#include "ldbc_snb-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace ldbc_snb {
    class GPR_dell_12 {
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_dell_12 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& ETY_STUDY_AT_attrMeta = meta->GetEdgeType(25).attributes_;
        _schema_EATT_STUDY_AT_576037_classYear = ETY_STUDY_AT_attrMeta.GetAttributePosition("classYear", true);
        topology4::AttributesMeta& ETY_STUDY_AT_REVERSE_attrMeta = meta->GetEdgeType(26).attributes_;
        _schema_EATT_STUDY_AT_REVERSE_576037_classYear = ETY_STUDY_AT_REVERSE_attrMeta.GetAttributePosition("classYear", true);
        if (request.jsoptions_.isMember("vType")) {
          _vType = request.jsoptions_["vType"][0].asString();
          vType_flag = true;
        } else {
          // parameter is not given (null case)
          _vType = string();
          vType_flag = false;
        }
        if (request.jsoptions_.isMember("attr")) {
          _attr = request.jsoptions_["attr"][0].asString();
          attr_flag = true;
        } else {
          // parameter is not given (null case)
          _attr = string();
          attr_flag = false;
        }
        if (request.jsoptions_.isMember("attrType")) {
          _attrType = request.jsoptions_["attrType"][0].asString();
          attrType_flag = true;
        } else {
          // parameter is not given (null case)
          _attrType = string();
          attrType_flag = false;
        }
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        for (unsigned i = 0; i < 29 ; i++) attrPosMapE_param_attr[i] = -1;
        attrPosMapE_param_attr[_schema_ETY_STUDY_AT_REVERSE] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT_REVERSE, _attr, true);
        attrPosMapE_param_attr[_schema_ETY_STUDY_AT] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT, _attr, true);
      }
      //query calling query constructor
      GPR_dell_12 (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, string vType, string attr, string attrType, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& ETY_STUDY_AT_attrMeta = meta->GetEdgeType(25).attributes_;
        _schema_EATT_STUDY_AT_576037_classYear = ETY_STUDY_AT_attrMeta.GetAttributePosition("classYear", true);
        topology4::AttributesMeta& ETY_STUDY_AT_REVERSE_attrMeta = meta->GetEdgeType(26).attributes_;
        _schema_EATT_STUDY_AT_REVERSE_576037_classYear = ETY_STUDY_AT_REVERSE_attrMeta.GetAttributePosition("classYear", true);
        _vType = vType;
        vType_flag = true;
        _attr = attr;
        attr_flag = true;
        _attrType = attrType;
        attrType_flag = true;
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        for (unsigned i = 0; i < 29 ; i++) attrPosMapE_param_attr[i] = -1;
        attrPosMapE_param_attr[_schema_ETY_STUDY_AT_REVERSE] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT_REVERSE, _attr, true);
        attrPosMapE_param_attr[_schema_ETY_STUDY_AT] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT, _attr, true);
      }
      
      ///class destructor
      ~GPR_dell_12 () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void EdgeMap_2(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        int64_t e_param_attr_int64_t = 0;
        float e_param_attr_float = 0;
        double e_param_attr_double = 0;
        int64_t e_classYear_int64_t = 0;
        bool e_classYear_int64_t_flag = false;
        
        //get e's attribute
        int e_typeIDVar = edge.GetAttr().type();
        if (e_typeIDVar == _schema_ETY_STUDY_AT_REVERSE) {
          if (edge.GetAttr().IsValid()) {
            e_classYear_int64_t = edge.GetAttr().GetInt(_schema_EATT_STUDY_AT_REVERSE_576037_classYear, 0);
            e_classYear_int64_t_flag = true;
          }
        } else if (e_typeIDVar == _schema_ETY_STUDY_AT) {
          if (edge.GetAttr().IsValid()) {
            e_classYear_int64_t = edge.GetAttr().GetInt(_schema_EATT_STUDY_AT_576037_classYear, 0);
            e_classYear_int64_t_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        if (!((e_classYear_int64_t_flag && (e_classYear_int64_t == 1999l)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // case attrType when "INT" then @@resint += e.getAttr ( attr, "INT" ) when "FLOAT" then @@resfloat += e.getAttr ( attr, "FLOAT" ) when "DOUBLE" then @@resdouble += e.getAttr ( attr, "DOUBLE" ) end
        if (((__HF_GPR_retrieveGV<string >(context, 2, __HF_GPR_retrieveGV<bool >(context, 3, true, "attrType_flag$"), "attrType") == string("INT")))) {
          
          // @@resint += e.getAttr ( attr, "INT" )
          if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && edge.GetAttr().IsValid()) {
            context.GlobalVariableAdd(0, ListAccum<int64_t > (edge.GetAttr().GetAttrValue<int64_t>(attrPosMapE_param_attr[e_typeIDVar])));
          }
        }
        else if (((__HF_GPR_retrieveGV<string >(context, 2, __HF_GPR_retrieveGV<bool >(context, 3, true, "attrType_flag$"), "attrType") == string("FLOAT")))) {
          
          // @@resfloat += e.getAttr ( attr, "FLOAT" )
          if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && edge.GetAttr().IsValid()) {
            context.GlobalVariableAdd(1, ListAccum<float > (edge.GetAttr().GetAttrValue<float>(attrPosMapE_param_attr[e_typeIDVar])));
          }
        }
        else if (((__HF_GPR_retrieveGV<string >(context, 2, __HF_GPR_retrieveGV<bool >(context, 3, true, "attrType_flag$"), "attrType") == string("DOUBLE")))) {
          
          // @@resdouble += e.getAttr ( attr, "DOUBLE" )
          if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && edge.GetAttr().IsValid()) {
            context.GlobalVariableAdd(2, ListAccum<double > (edge.GetAttr().GetAttrValue<double>(attrPosMapE_param_attr[e_typeIDVar])));
          }
        }
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("dell_12")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_vType_(_vType);
        gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_vType_flag(vType_flag);
        gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_attr_(_attr);
        gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_attr_flag(attr_flag);
        gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_attrType_(_attrType);
        gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_attrType_flag(attrType_flag);
        
        gpelib4::SumVariable<ListAccum<int64_t > > __GQUERY_GV_resint_1_;
        gpelib4::SumVariable<ListAccum<float > > __GQUERY_GV_resfloat_1_;
        gpelib4::SumVariable<ListAccum<double > > __GQUERY_GV_resdouble_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_result_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_result_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_result_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_Start_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_Start_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_Start_vector_;
        {
          /*
          @@resint_1 reinitiate
          */
          
          // @@resint_1 reinitiate
          __GQUERY_GV_resint_1_.Value().clear();
          
        }
        {
          /*
          @@resfloat_1 reinitiate
          */
          
          // @@resfloat_1 reinitiate
          __GQUERY_GV_resfloat_1_.Value().clear();
          
        }
        {
          /*
          @@resdouble_1 reinitiate
          */
          
          // @@resdouble_1 reinitiate
          __GQUERY_GV_resdouble_1_.Value().clear();
          
        }
        {
          /*
          Start = { vType };
          */
          int64_t __vset_Start_size = __GQUERY_GV_Global_Variable__vset_Start_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {&__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag, &__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_Start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_Start_SIZE__.Value() -= __vset_Start_size;
          
        }
        {
          /*
          result = select s from Start : s -(_ : e)-> _ : t where e.classYear == 1999 accum case attrType when "INT" then @@resint += e.getAttr ( attr, "INT" ) when "FLOAT" then @@resfloat += e.getAttr ( attr, "FLOAT" ) when "DOUBLE" then @@resdouble += e.getAttr ( attr, "DOUBLE" ) end;
          */
          int64_t __vset_result_size = __GQUERY_GV_Global_Variable__vset_result_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {&__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag, &__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag}, //input gvs
            {&__GQUERY_GV_resint_1_, &__GQUERY_GV_resfloat_1_, &__GQUERY_GV_resdouble_1_, &__GQUERY_GV_Global_Variable__vset_result_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_result_SIZE__.Value() -= __vset_result_size;
          __GQUERY_GV_Global_Variable__vset_result_hasOrder_.Value() = false;
          
        }
        {
          /*
          print @@resint;
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("@@resint");
            (__HF_GPR_retrieveGV<ListAccum<int64_t >  >(__GQUERY_GV_resint_1_, true, "resint_1")).json_printer(writer, _request, graphAPI, true);
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          print @@resfloat;
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("@@resfloat");
            (__HF_GPR_retrieveGV<ListAccum<float >  >(__GQUERY_GV_resfloat_1_, true, "resfloat_1")).json_printer(writer, _request, graphAPI, true);
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          print @@resdouble;
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("@@resdouble");
            (__HF_GPR_retrieveGV<ListAccum<double >  >(__GQUERY_GV_resdouble_1_, true, "resdouble_1")).json_printer(writer, _request, graphAPI, true);
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_result = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_Start = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_vType_(_vType);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_vType_flag(vType_flag);
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_attr_(_attr);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_attr_flag(attr_flag);
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_attrType_(_attrType);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_attrType_flag(attrType_flag);
          
          gpelib4::SumVariable<ListAccum<int64_t > > __GQUERY_GV_resint_1_;
          gpelib4::SumVariable<ListAccum<float > > __GQUERY_GV_resfloat_1_;
          gpelib4::SumVariable<ListAccum<double > > __GQUERY_GV_resdouble_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_result_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_result_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_result_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_Start_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_Start_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_Start_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag, &__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_Start_SIZE__) __GQUERY_GV_Global_Variable__vset_Start_SIZE___output;
                //run action
                __GQUERY__vSet_Start->SetAllActiveFlag(false);
                VERTEX vTypeId = GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_vType_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_vType_flag, true, "vType_flag$"), "vType"));
                __GQUERY__vSet_Start->SetActiveFlagByType(vTypeId, true);
                __GQUERY_GV_Global_Variable__vset_Start_SIZE__.Value() = __GQUERY__vSet_Start->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_Start_SIZE___output.Value() = __GQUERY__vSet_Start->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_Start_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag, &__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag});
                //declare output gvs
                decltype(__GQUERY_GV_resint_1_) __GQUERY_GV_resint_1__output;
                decltype(__GQUERY_GV_resfloat_1_) __GQUERY_GV_resfloat_1__output;
                decltype(__GQUERY_GV_resdouble_1_) __GQUERY_GV_resdouble_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_result_SIZE__) __GQUERY_GV_Global_Variable__vset_result_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                enableAllEdgeTypesInGraph(edgeFilterCtrl.get());
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::ldbc_snb::GPR_dell_12::EdgeMap_2, this),//action function
                  __GQUERY__vSet_Start.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container2},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag, &__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag},//input gv
                  {&__GQUERY_GV_resint_1__output, &__GQUERY_GV_resfloat_1__output, &__GQUERY_GV_resdouble_1__output, &__GQUERY_GV_Global_Variable__vset_result_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                filter.setSrcFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::Context& context) -> bool {
                  VERTEX src = srcVertex.vid();
                  auto graphAPI = context.GraphAPI();
                  
                  V_VALUE src_val;
                  
                  Condition result = Condition(Condition::UNDEFINE);
                  return result.toBool();
                });
                
                filter.setEdgeFilterFunction([&](gpr::VertexEntity& srcVertex, gpr::VertexEntity& tgtVertex, gpr::EdgeEntity& edge, gpr::Context& context) -> bool {
                  VERTEX src = srcVertex.vid();
                  VERTEX tgt = tgtVertex.vid();
                  auto graphAPI = context.GraphAPI();
                  V_VALUE src_val;
                  
                  int64_t e_classYear_int64_t = 0;
                  bool e_classYear_int64_t_flag = false;
                  
                  //get e's attribute
                  int e_typeIDVar = edge.GetAttr().type();
                  if (e_typeIDVar == _schema_ETY_STUDY_AT_REVERSE) {
                    if (edge.GetAttr().IsValid()) {
                      e_classYear_int64_t = edge.GetAttr().GetInt(_schema_EATT_STUDY_AT_REVERSE_576037_classYear, 0);
                      e_classYear_int64_t_flag = true;
                    }
                  } else if (e_typeIDVar == _schema_ETY_STUDY_AT) {
                    if (edge.GetAttr().IsValid()) {
                      e_classYear_int64_t = edge.GetAttr().GetInt(_schema_EATT_STUDY_AT_576037_classYear, 0);
                      e_classYear_int64_t_flag = true;
                    }
                  }
                  Condition result = Condition(e_classYear_int64_t_flag? Condition((e_classYear_int64_t == 1999l)): false);
                  return result.toBool();
                });
                
                filter.setTgtFilterFunction([&](gpr::VertexEntity& tgtVertex, gpr::Context& context) -> bool {
                  VERTEX tgt = tgtVertex.vid();
                  auto graphAPI = context.GraphAPI();
                  V_VALUE tgt_val;
                  
                  Condition result = Condition(Condition::UNDEFINE);
                  return result.toBool();
                });
                
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container2}, {__GQUERY__vSet_target.get()});
                //run vertex action
                
                __GQUERY__vSet_result->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::ldbc_snb::GPR_dell_12::Reduce_2, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container2},//input container(old v_value and delta)
                  {},//output container(new v_value)
                  {__GQUERY__vSet_result.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_attr_, &__GQUERY_GV_Global_Variable_attr_flag, &__GQUERY_GV_Global_Variable_attrType_, &__GQUERY_GV_Global_Variable_attrType_flag, &__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag},//input gv
                  {&__GQUERY_GV_resint_1__output, &__GQUERY_GV_resfloat_1__output, &__GQUERY_GV_resdouble_1__output, &__GQUERY_GV_Global_Variable__vset_result_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_result_SIZE__.Value() = __GQUERY__vSet_result->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_result_SIZE___output.Value() = __GQUERY__vSet_result->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_resint_1__output, &__GQUERY_GV_resfloat_1__output, &__GQUERY_GV_resdouble_1__output, &__GQUERY_GV_Global_Variable__vset_result_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_result_hasOrder_.Value() = false;
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      string _vType;
      bool vType_flag = true;
      string _attr;
      bool attr_flag = true;
      string _attrType;
      bool attrType_flag = true;
      const int _schema_ETY_STUDY_AT = 25;
      const int _schema_ETY_STUDY_AT_REVERSE = 26;
      int _schema_EATT_STUDY_AT_576037_classYear = -1;
      int _schema_EATT_STUDY_AT_REVERSE_576037_classYear = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_ = "action_3_";
      const std::string action_4_ = "action_4_";
      const std::string action_5_ = "action_5_";
      int attrPosMapE_param_attr[29];
      public:
        
        ///return vars
      };//end class GPR_dell_12
    bool call_dell_12(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::ldbc_snb::GPR_dell_12 gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_dell_12_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::ldbc_snb::GPR_dell_12 gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_dell_12(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 3) {
          HF_set_error(_request, "Invalid parameter size: 3|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        string vType = values[0]->GetStringInternal();
        string attr = values[1]->GetStringInternal();
        string attrType = values[2]->GetStringInternal();
        
        UDIMPL::ldbc_snb::GPR_dell_12 gpr(graphAPI.get(), _request, serviceapi, graphupdates, vType, attr, attrType, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "dell_12") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_dell_12(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string vType, string attr, string attrType) {
      UDIMPL::ldbc_snb::GPR_dell_12 gpr(graphAPI, request, serviceapi, _graphupdates_, vType, attr, attrType, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_dell_12(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum, string vType, string attr, string attrType) {
      UDIMPL::ldbc_snb::GPR_dell_12 gpr(graphAPI, request, serviceapi, _graphupdates_, vType, attr, attrType, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace ldbc_snb
}//end namespace UDIMPL
