/********** query start ***************
CREATE QUERY dell_12(STRING vType, String attr, String attrType) FOR GRAPH ldbc_snb {
  ListAccum<INT> @@resint;
  ListAccum<FLOAT> @@resfloat;
  ListAccum<DOUBLE> @@resdouble;
  Start={vType};
  result= select s from Start:s-(_:e)->_:t
  WHERE e.classYear == 1999
  ACCUM
    CASE attrType WHEN "INT" THEN
        @@resint += e.getAttr(attr, "INT")
    WHEN "FLOAT" THEN
        @@resfloat += e.getAttr(attr, "FLOAT")
    WHEN "DOUBLE" THEN
        @@resdouble += e.getAttr(attr, "DOUBLE")
    END;
  print @@resint;
  print @@resfloat;
  print @@resdouble;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_dell_12 :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_dell_12 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& ETY_STUDY_AT_attrMeta = meta->GetEdgeType(25).attributes_;
_schema_EATT_STUDY_AT_576037_classYear = ETY_STUDY_AT_attrMeta.GetAttributePosition("classYear", true);
topology4::AttributesMeta& ETY_STUDY_AT_REVERSE_attrMeta = meta->GetEdgeType(26).attributes_;
_schema_EATT_STUDY_AT_REVERSE_576037_classYear = ETY_STUDY_AT_REVERSE_attrMeta.GetAttributePosition("classYear", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
      if (request.jsoptions_.isMember("vType")) {
        _vType = request.jsoptions_["vType"][0].asString();
        vType_flag = true;
      } else {
        // parameter is not given (null case)
        _vType = string();
        vType_flag = false;
      }
      if (request.jsoptions_.isMember("attr")) {
        _attr = request.jsoptions_["attr"][0].asString();
        attr_flag = true;
      } else {
        // parameter is not given (null case)
        _attr = string();
        attr_flag = false;
      }
      if (request.jsoptions_.isMember("attrType")) {
        _attrType = request.jsoptions_["attrType"][0].asString();
        attrType_flag = true;
      } else {
        // parameter is not given (null case)
        _attrType = string();
        attrType_flag = false;
      }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }
for (unsigned i = 0; i < 29 ; i++) attrPosMapE_param_attr[i] = -1;
attrPosMapE_param_attr[_schema_ETY_STUDY_AT_REVERSE] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT_REVERSE, _attr, true);
attrPosMapE_param_attr[_schema_ETY_STUDY_AT] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT, _attr, true);

}




   UDF_dell_12 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string vType, string attr, string attrType, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& ETY_STUDY_AT_attrMeta = meta->GetEdgeType(25).attributes_;
_schema_EATT_STUDY_AT_576037_classYear = ETY_STUDY_AT_attrMeta.GetAttributePosition("classYear", true);
topology4::AttributesMeta& ETY_STUDY_AT_REVERSE_attrMeta = meta->GetEdgeType(26).attributes_;
_schema_EATT_STUDY_AT_REVERSE_576037_classYear = ETY_STUDY_AT_REVERSE_attrMeta.GetAttributePosition("classYear", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_vType = vType;
vType_flag = true;
_attr = attr;
attr_flag = true;
_attrType = attrType;
attrType_flag = true;
__GQUERY__all_vetex_mode = false;
for (unsigned i = 0; i < 29 ; i++) attrPosMapE_param_attr[i] = -1;
attrPosMapE_param_attr[_schema_ETY_STUDY_AT_REVERSE] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT_REVERSE, _attr, true);
attrPosMapE_param_attr[_schema_ETY_STUDY_AT] = GSQL_UTIL::HF_GetAttrPosInSchema(&serviceapi, _schema_ETY_STUDY_AT, _attr, true);

}

   ~UDF_dell_12 () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_vType, GV_SYS_vType_flag, GV_PARAM_attr, GV_SYS_attr_flag, GV_PARAM_attrType, GV_SYS_attrType_flag, GV_GACC_resint_1, GV_GACC_resfloat_1, GV_GACC_resdouble_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_result_SIZE, GV_SYS_result_ORDERBY, GV_SYS_result_LASTSET, GV_SYS_Start_SIZE, GV_SYS_Start_ORDERBY, GV_SYS_Start_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_result_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_result_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_result_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Start_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Start_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Start_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_vType, new  gpelib4::BroadcastVariable<string> (_vType));
   gvs->Register (GV_SYS_vType_flag, new  gpelib4::BroadcastVariable<bool> (vType_flag));
   gvs->Register (GV_PARAM_attr, new  gpelib4::BroadcastVariable<string> (_attr));
   gvs->Register (GV_SYS_attr_flag, new  gpelib4::BroadcastVariable<bool> (attr_flag));
   gvs->Register (GV_PARAM_attrType, new  gpelib4::BroadcastVariable<string> (_attrType));
   gvs->Register (GV_SYS_attrType_flag, new  gpelib4::BroadcastVariable<bool> (attrType_flag));


   // loop indices

   //limit k gv heap

   // global accs
   gvs->Register (GV_GACC_resint_1, new  gpelib4::SumVariable<ListAccum<int64_t > > (ListAccum<int64_t >  ()));
   gvs->Register (GV_GACC_resfloat_1, new  gpelib4::SumVariable<ListAccum<float > > (ListAccum<float >  ()));
   gvs->Register (GV_GACC_resdouble_1, new  gpelib4::SumVariable<ListAccum<double > > (ListAccum<double >  ()));

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_dell_12::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_4 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Enter function EdgeMap_4 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration
   int64_t e_classYear_int64_t = 0;
   bool e_classYear_int64_t_flag = false;

   //get e's attribute
   int e_typeIDVar = e_attr->type();
     if (e_typeIDVar == _schema_ETY_STUDY_AT_REVERSE) {
       e_classYear_int64_t = e_attr->GetInt(_schema_EATT_STUDY_AT_REVERSE_576037_classYear, 0);
     e_classYear_int64_t_flag = true;
     } else if (e_typeIDVar == _schema_ETY_STUDY_AT) {
       e_classYear_int64_t = e_attr->GetInt(_schema_EATT_STUDY_AT_576037_classYear, 0);
     e_classYear_int64_t_flag = true;
     }

   // WHERE
   if (!(e_classYear_int64_t_flag && (e_classYear_int64_t) == (1999l))) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   if (HF_retrieve_param_856409387<string>(context, GV_PARAM_attrType, GV_SYS_attrType_flag, "attrType") == string("INT")) { 
   if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && (*e_attr).IsValid()) {
  context->GlobalVariable_Reduce<ListAccum<int64_t > > (GV_GACC_resint_1, ListAccum<int64_t >  (( (*e_attr).GetAttrValue<int64_t>(attrPosMapE_param_attr[e_typeIDVar]))));
   }
 }
   else if (HF_retrieve_param_856409387<string>(context, GV_PARAM_attrType, GV_SYS_attrType_flag, "attrType") == string("FLOAT")) { 
   if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && (*e_attr).IsValid()) {
  context->GlobalVariable_Reduce<ListAccum<float > > (GV_GACC_resfloat_1, ListAccum<float >  (( (*e_attr).GetAttrValue<float>(attrPosMapE_param_attr[e_typeIDVar]))));
   }
 }
   else if (HF_retrieve_param_856409387<string>(context, GV_PARAM_attrType, GV_SYS_attrType_flag, "attrType") == string("DOUBLE")) { 
   if (attrPosMapE_param_attr[e_typeIDVar] >= 0 && (*e_attr).IsValid()) {
  context->GlobalVariable_Reduce<ListAccum<double > > (GV_GACC_resdouble_1, ListAccum<double >  (( (*e_attr).GetAttrValue<double>(attrPosMapE_param_attr[e_typeIDVar]))));
   }
 }


   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Exit function EdgeMap_4 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_4 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Enter function Reduce_4 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Exit function Reduce_4 v: " << v << std::endl;
}


void (UDF_dell_12::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_dell_12::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("dell_12");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_dell_12 DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                     context->GlobalVariable_GetValue<ListAccum<int64_t > > (GV_GACC_resint_1).clear();
                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                     context->GlobalVariable_GetValue<ListAccum<float > > (GV_GACC_resfloat_1).clear();
                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                     context->GlobalVariable_GetValue<ListAccum<double > > (GV_GACC_resdouble_1).clear();
                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if (!__GQUERY__all_vetex_mode) {
                   uint64_t vTypeId = _serviceapi->GetTopologyMeta()->GetVertexTypeId(HF_retrieve_param_856409387<string>(context, GV_PARAM_vType, GV_SYS_vType_flag, "vType"), _request.graph_id_, true);
                   if (vTypeId == (uint64_t)-1) {
                     string msg("Runtime Error: '" + HF_retrieve_param_856409387<string>(context, GV_PARAM_vType, GV_SYS_vType_flag, "vType") + "' is not valid vertex type.");
                     _request.AbortQuery(msg);
                     _request.SetErrorCode(3001);
                     return;
                   }
                   context->SetActiveFlagByType(vTypeId, true);
                 }

                 PC = 4;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Start";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Start_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(30L, "Start", 5);
                     timer_.saveVSetCode(30L, "Start={vType};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET) = 30L;
                   }
                 break;

           PC = 4; break;
         }

         case 4:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 enableAllEdgeTypesInGraph(context->GetTypeFilterController());

                 edgemap   = &UDF_dell_12::EdgeMap_4;
                 context->set_udfedgemapsetting(0);

                 reduce    = &UDF_dell_12::Reduce_4;
                 context->set_udfreducesetting(0);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "result";
                 PC = 5;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_dell_12 INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(38L, "result", 6);
                     timer_.saveVSetCode(38L, "result= select s from Start:s-(_:e)-> _:t\n  WHERE e.classYear == 1999\n  ACCUM\n    CASE attrType WHEN \"INT\" THEN\n        @@resint += e.getAttr(attr, \"INT\")\n    WHEN \"FLOAT\" THEN\n        @@resfloat += e.getAttr(attr, \"FLOAT\")\n    WHEN \"DOUBLE\" THEN\n        @@resdouble += e.getAttr(attr, \"DOUBLE\")\n    END;");
                     timer_.addDependency(38L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Start_LASTSET));
                     timer_.start("result", 6, context->CalcActiveVertexCount(), 38L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_LASTSET) = 38L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_result_ORDERBY) = -1;
                   break;
                 }

           PC = 5; break;
         }

         case 5:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@resint");
(( (context->GlobalVariable_GetValue<ListAccum<int64_t > > (GV_GACC_resint_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 6;
                 break;

           PC = 6; break;
         }

         case 6:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@resfloat");
(( (context->GlobalVariable_GetValue<ListAccum<float > > (GV_GACC_resfloat_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 7;
                 break;

           PC = 7; break;
         }

         case 7:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("@@resdouble");
(( (context->GlobalVariable_GetValue<ListAccum<double > > (GV_GACC_resdouble_1)))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 8;
                 break;

           PC = 8; break;
         }

         case 8:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 4:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_result_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_result_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

private:
   string _vType;
   bool vType_flag;
   string _attr;
   bool attr_flag;
   string _attrType;
   bool attrType_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_ETY_STUDY_AT = 25;
const int _schema_ETY_STUDY_AT_REVERSE = 26;
int _schema_EATT_STUDY_AT_576037_classYear = -1;
int _schema_EATT_STUDY_AT_REVERSE_576037_classYear = -1;
   V_VALUE* vvalptr = nullptr;
   int attrPosMapE_param_attr[29];
};

  bool call_dell_12(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query dell_12 in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_dell_12 udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "dell_12") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "dell_12") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "dell_12") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "dell_12") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_dell_12(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,string vType,string attr,string attrType) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_dell_12 udf(request, serviceapi, _graphupdates_ , vType, attr, attrType, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_dell_12_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_dell_12(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,string vType,string attr,string attrType){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_dell_12(graphAPI, request, serviceapi,_graphupdates_ , vType, attr, attrType);
}
  bool call_dell_12(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query dell_12 in all vetext active mode." << std::endl;
  }

if (values.size() != 3) {
    HF_set_error(request, "Invalid parameter size: 3|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
string vType = values[0]->GetStringInternal();
string attr = values[1]->GetStringInternal();
string attrType = values[2]->GetStringInternal();

  UDIMPL::ldbc_snb::UDF_dell_12 udf(request, serviceapi, graphupdates, vType, attr, attrType, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
