/********** query start ***************
create query testSelectVertex() for graph poc_graph {
  S1 = SelectVertex("/path/to/file", $0, company, ",", false);
  print S1;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "utility/gutil/fileinputoutputpolicy/FileInputOutputPolicy.hpp"
#include "utility/gutil/filelinereader.hpp"


using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace poc_graph{ 
class UDF_testSelectVertex :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_testSelectVertex (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_company_576037_label = VTY_company_attrMeta.GetAttributePosition("label", true);
_schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
_schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
// construct file policy: input
if (request.jsoptions_.isMember ("__GQUERY__FILE_INPUT_POLICY__")) {
  gvector<std::string> file_policy_list;
  const auto& file_policy_json = request.jsoptions_["__GQUERY__FILE_INPUT_POLICY__"];
  for (Json::Value::const_iterator it=file_policy_json.begin(); it!=file_policy_json.end(); ++it) {
    file_policy_list.push_back(it->asString());
  }
  __GQUERY__input_policy__576037 = std::make_unique<gutil::FileInputOutputPolicy>(file_policy_list, true);
} else {
  std::string msg("Runtime Error: File input policy is not built!");
  throw gutil::GsqlException(msg, gutil::error_t::E_FILE_INPUT_POLICY);
}

    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_testSelectVertex (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_company_576037_label = VTY_company_attrMeta.GetAttributePosition("label", true);
_schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
_schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
// construct file policy: input
if (request.jsoptions_.isMember ("__GQUERY__FILE_INPUT_POLICY__")) {
  gvector<std::string> file_policy_list;
  const auto& file_policy_json = request.jsoptions_["__GQUERY__FILE_INPUT_POLICY__"];
  for (Json::Value::const_iterator it=file_policy_json.begin(); it!=file_policy_json.end(); ++it) {
    file_policy_list.push_back(it->asString());
  }
  __GQUERY__input_policy__576037 = std::make_unique<gutil::FileInputOutputPolicy>(file_policy_list, true);
} else {
  std::string msg("Runtime Error: File input policy is not built!");
  throw gutil::GsqlException(msg, gutil::error_t::E_FILE_INPUT_POLICY);
}

    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;

}

   ~UDF_testSelectVertex () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_S1_SIZE, GV_SYS_S1_ORDERBY, GV_SYS_S1_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_S1_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_S1_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_S1_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params


   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_testSelectVertex::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_1_S1 (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_testSelectVertex INFO] " << "Enter function Write_1_S1 v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int S1_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_company != -1 && S1_typeIDVar == _schema_VTY_company)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_company_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteString(v_attr->GetString(_schema_VATT_company_576037_id));
         }
         if (_schema_VATT_company_576037_label != -1) {
         writer.WriteNameString("label");
         writer.WriteBool(v_attr->GetBool(_schema_VATT_company_576037_label, 0));
         }
         if (_schema_VATT_company_576037_company_name != -1) {
         writer.WriteNameString("company_name");
         writer.WriteString(v_attr->GetString(_schema_VATT_company_576037_company_name));
         }
         if (_schema_VATT_company_576037_country != -1) {
         writer.WriteNameString("country");
         (string_compress(v_attr, _schema_VATT_company_576037_country)).json_printer(writer, _request, context->GraphAPI(), true);
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_testSelectVertex INFO] " << "Exit function Write_1_S1 v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}


void (UDF_testSelectVertex::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_testSelectVertex::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testSelectVertex INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("testSelectVertex");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_testSelectVertex DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
  gvector<std::pair<std::string, std::string> > type_uid_list;
// Select vertex from file.
{
  std::string fileName = string("/path/to/file");
// check file policy: input
std::string _file_input_policy_err_msg;
if (!__GQUERY__input_policy__576037->allows(fileName, _file_input_policy_err_msg)) {
  _request.SetErrorCode(gutil::error_t::E_FILE_INPUT_POLICY);
  HF_set_error(_request, _file_input_policy_err_msg, true);
  return;
}
  char separator = ',';
  if (!__gquery__file_exists(fileName)) {
    std::string msg("Runtime Error: File '" + fileName + "' does not exist.");
    HF_set_error(_request, msg, true);
    context->Abort();
    return;
  }
  boost::filesystem::path gsql_f_path_(fileName);
  boost::filesystem::path gsql_c_path_ = boost::filesystem::canonical(gsql_f_path_);
  if (!boost::filesystem::is_regular_file(gsql_c_path_)) {
    std::string msg("Runtime Error: File '" + fileName + "' is not a regular file, please make sure it is a regular file.");
    HF_set_error(_request, msg, true);
    context->Abort();
    return;
  }
  gutil::FileLineReader fileReader (fileName);
  ghash_map<string, uint32_t> name2position;
  char* chrPtr = NULL;
  size_t chrLen;
  gvector<std::pair<std::string, std::string> > line_type_uid_list;
  line_type_uid_list.push_back(std::pair<std::string, std::string>("company", ""));
  uint32_t maxPosition = 0;
  uint32_t tmpPosition;
  ghash_map<int, gvector<int> > position2line_type_list;
  ghash_map<int, gvector<int> > position2line_uid_list;
  tmpPosition = 0;
  if (tmpPosition > maxPosition) maxPosition = tmpPosition;
  position2line_uid_list[tmpPosition].push_back(0);
  while (fileReader.MoveNextLine()) {
    for (uint32_t i = 0; i <= maxPosition; ++i) {
      if (!fileReader.NextString(chrPtr, chrLen, separator)) {
        std::string msg("Runtime Error: The row does not contain enough words.");
        HF_set_error(_request, msg, true);
        context->Abort();
        return;
      }
      for (uint32_t j = 0; j < position2line_type_list[i].size(); ++j) {
        line_type_uid_list[position2line_type_list[i][j]].first = string(chrPtr, chrLen);
      }
      for (uint32_t j = 0; j < position2line_uid_list[i].size(); ++j) {
        line_type_uid_list[position2line_uid_list[i][j]].second = string(chrPtr, chrLen);
      }
    }
    type_uid_list.insert(type_uid_list.end(), line_type_uid_list.begin(), line_type_uid_list.end());
  }
}

  for (uint32_t i = 0; i < type_uid_list.size(); ++i) {
    if (!HF_VertexTypeCheck(type_uid_list[i].first)) {
      std::string msg("Runtime Error: \"" + type_uid_list[i].first + "\" is not valid vertex type.");
      HF_set_error(_request, msg, true);
      context->Abort();
      return;
    }
  }
  std::stringstream error_ss;
  gvector<VertexLocalId_t> vidList = _serviceapi->Convert_UIdtoVId (&_request, type_uid_list, error_ss);
  if (vidList.empty()) {
    HF_set_error(_request, error_ss.str(), false);
  }
  uint32_t failedToConvert = 0;
  for (uint32_t i = 0; i < vidList.size(); ++i) {
    if (vidList[i] == (VertexLocalId_t)-1) {
      failedToConvert++;
      std::string msg("Runtime Warning: " + boost::lexical_cast<string>(failedToConvert) + " vertices failed to convert. Check input file to verify!");
      HF_set_error(_request, msg, false);
    } else {
      context->SetActiveFlag (vidList[i]);
    }
  }

                 PC = 1;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "S1";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_S1_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_S1_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(12L, "S1", 2);
                     timer_.saveVSetCode(12L, "S1 = SelectVertex(\"/path/to/file\", $0, company, \",\", false);");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_S1_LASTSET) = 12L;
                   }
                 break;

           PC = 1; break;
         }

         case 1:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_testSelectVertex::Write_1_S1;
                 context->set_udfprintsetting(1);

                 writer.WriteName("S1");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 2;
                 break;

           PC = 2; break;
         }

         case 2:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

ghash_set<string> vTypeList;
bool HF_VertexTypeCheck(string& vType) {
  if (vTypeList.size() == 0) {
    vTypeList.insert("company");
    vTypeList.insert("members");
    vTypeList.insert("skill");
  }
  return (vTypeList.find(vType) != vTypeList.end());
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_company = 0;
int _schema_VATT_company_576037_id = -1;
int _schema_VATT_company_576037_label = -1;
int _schema_VATT_company_576037_company_name = -1;
int _schema_VATT_company_576037_country = -1;
   V_VALUE* vvalptr = nullptr;
   gunique_ptr<gutil::FileInputOutputPolicy> __GQUERY__input_policy__576037 = nullptr;
};

  bool call_testSelectVertex(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query testSelectVertex in all vetext active mode." << std::endl;
    }
    UDIMPL::poc_graph::UDF_testSelectVertex udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "testSelectVertex") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testSelectVertex") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testSelectVertex") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "testSelectVertex") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_testSelectVertex(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::poc_graph::UDF_testSelectVertex udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_testSelectVertex_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_testSelectVertex(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_testSelectVertex(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_testSelectVertex(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query testSelectVertex in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::poc_graph::UDF_testSelectVertex udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
