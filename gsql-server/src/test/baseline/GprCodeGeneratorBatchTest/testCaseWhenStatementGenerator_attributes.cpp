/********** query start ***************
CREATE DISTRIBUTED QUERY att() FOR GRAPH poc_graph {
  SumAccum<string> @@cnt;
  start = { members.* };
  start = SELECT s FROM start:s -(:e)-> :t
    ACCUM
      CASE
        WHEN t.type == "company" AND t.company_name != "abc" THEN
          @@cnt += t.country
        WHEN t.registrationDate > 55 THEN
          @@cnt += s.profileIndustryId
        ELSE
          @@cnt += t.id
      END;
    PRINT @@cnt;
}
********** query end ***************/
#include "poc_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace poc_graph {
    class GPR_att {
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_att (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        _schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
        topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
        _schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
        topology4::AttributesMeta& VTY_skill_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_skill_576037_id = VTY_skill_attrMeta.GetAttributePosition("id", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_att (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        _schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
        topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
        _schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
        topology4::AttributesMeta& VTY_skill_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_skill_576037_id = VTY_skill_attrMeta.GetAttributePosition("id", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_att () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void EdgeMap_2(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        string_compress tgt_country_string_compress = string();
        bool tgt_country_string_compress_flag = false;
        string src_profileIndustryId_string = string();
        bool src_profileIndustryId_string_flag = false;
        string tgt_id_string = string();
        bool tgt_id_string_flag = false;
        string tgt_company_name_string = string();
        bool tgt_company_name_string_flag = false;
        uint64_t tgt_registrationDate_uint64_t = 0;
        bool tgt_registrationDate_uint64_t_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_skill) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_id_string = tgtVertex.GetAttr().GetString(_schema_VATT_skill_576037_id);
            tgt_id_string_flag = true;
          }
        } else if (tgt_typeIDVar == _schema_VTY_members) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_id_string = tgtVertex.GetAttr().GetString(_schema_VATT_members_576037_id);
            tgt_id_string_flag = true;
          }
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_registrationDate_uint64_t = tgtVertex.GetAttr().GetUInt(_schema_VATT_members_576037_registrationDate, 0);
            tgt_registrationDate_uint64_t_flag = true;
          }
        } else if (tgt_typeIDVar == _schema_VTY_company) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_country_string_compress = string_compress(&tgtVertex.GetAttr(), _schema_VATT_company_576037_country);
            tgt_country_string_compress_flag = true;
          }
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_company_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_company_576037_company_name);
            tgt_company_name_string_flag = true;
          }
        }
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_members) {
          if (srcVertex.GetAttr().IsValid()) {
            src_profileIndustryId_string = srcVertex.GetAttr().GetString(_schema_VATT_members_576037_profileIndustryId);
            src_profileIndustryId_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // case when t.type == "company" and t.company_name != "abc" then @@cnt += t.country when t.registrationDate > 55 then @@cnt += s.profileIndustryId else @@cnt += t.id end
        if (((_schema_VTY_company >= 0 && graphAPI->GetVertexType(tgt) == (unsigned) _schema_VTY_company) && (tgt_company_name_string_flag && (tgt_company_name_string != string("abc"))))) {
          
          // @@cnt += t.country
          if (tgt_country_string_compress_flag) {
            context.GlobalVariableAdd(0, SumAccum<string > (tgt_country_string_compress));
          }
        }
        else if ((tgt_registrationDate_uint64_t_flag && (tgt_registrationDate_uint64_t > 55l))) {
          
          // @@cnt += s.profileIndustryId
          if (src_profileIndustryId_string_flag) {
            context.GlobalVariableAdd(0, SumAccum<string > (src_profileIndustryId_string));
          }
        }
        else {
          
          // @@cnt += t.id
          if (tgt_id_string_flag) {
            context.GlobalVariableAdd(0, SumAccum<string > (tgt_id_string));
          }
        }
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("att")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<SumAccum<string > > __GQUERY_GV_cnt_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_start_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_start_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_start_vector_;
        {
          /*
          @@cnt_1 reinitiate
          */
          
          // @@cnt_1 reinitiate
          __GQUERY_GV_cnt_1_.Value().clear();
          
        }
        {
          /*
          start = { members .* };
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          
        }
        {
          /*
          start = select s from start : s -(: e)-> : t accum case when t.type == "company" and t.company_name != "abc" then @@cnt += t.country when t.registrationDate > 55 then @@cnt += s.profileIndustryId else @@cnt += t.id end;
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
          
        }
        {
          /*
          print @@cnt;
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("@@cnt");
            writer.WriteString(__HF_GPR_retrieveGV<SumAccum<string >  >(__GQUERY_GV_cnt_1_, true, "cnt_1").data_);
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_start = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::SumVariable<SumAccum<string > > __GQUERY_GV_cnt_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_start_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_start_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_start_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                //run action
                __GQUERY__vSet_start->SetAllActiveFlag(false);
                __GQUERY__vSet_start->SetActiveFlagByType(_schema_VTY_members, true);
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_start_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                enableAllEdgeTypesInGraph(edgeFilterCtrl.get());
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::poc_graph::GPR_att::EdgeMap_2, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container2},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                filter.addTgtAttribute(
                  _schema_VTY_skill, {(unsigned)_schema_VATT_skill_576037_id}
                );
                filter.addTgtAttribute(
                  _schema_VTY_members, {(unsigned)_schema_VATT_members_576037_id, (unsigned)_schema_VATT_members_576037_registrationDate}
                );
                filter.addTgtAttribute(
                  _schema_VTY_company, {(unsigned)_schema_VATT_company_576037_country, (unsigned)_schema_VATT_company_576037_company_name}
                );
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container2}, {__GQUERY__vSet_target.get()});
                //run vertex action
                
                __GQUERY__vSet_start->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_att::Reduce_2, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container2},//input container(old v_value and delta)
                  {},//output container(new v_value)
                  {__GQUERY__vSet_start.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "att") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_company = 0;
      const int _schema_VTY_members = 1;
      const int _schema_VTY_skill = 2;
      int _schema_VATT_company_576037_company_name = -1;
      int _schema_VATT_company_576037_country = -1;
      int _schema_VATT_members_576037_id = -1;
      int _schema_VATT_members_576037_profileIndustryId = -1;
      int _schema_VATT_members_576037_registrationDate = -1;
      int _schema_VATT_skill_576037_id = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_ = "action_3_";
      public:
        
        ///return vars
      };//end class GPR_att
    bool call_att(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::poc_graph::GPR_att gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_att_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::poc_graph::GPR_att gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_att(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::poc_graph::GPR_att gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "att") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_att(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::poc_graph::GPR_att gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_att(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::poc_graph::GPR_att gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace poc_graph
}//end namespace UDIMPL
