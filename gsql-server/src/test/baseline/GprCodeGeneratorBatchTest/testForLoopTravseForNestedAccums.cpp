/********** query start ***************
CREATE DISTRIBUTED QUERY testForLoopTravseForNestedAccums() {
  GroupByAccum<
    String myKey,
    SetAccum<double> sumField,
    SetAccum<double> min<PERSON><PERSON>,
    SetAccum<double> max<PERSON>ield,
    SetAccum<double> avgField
  > @summaryAccum;
  GroupByAccum<
    String myKey,
    SumAccum<double> sumField,
    MinAccum<double> min<PERSON>ield,
    MaxAccum<double> max<PERSON>ield,
    AvgAccum avgField
  > @singleAccum;
  res = Select s From members:s
    POST-ACCUM FOREACH itr in s.@singleAccum DO
      s.@summaryAccum += ("singleKey" -> itr.sumField, itr.minField, itr.maxField,
        itr.avgField)
    END;
}
********** query end ***************/
#include "poc_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace poc_graph {
    class GPR_testForLoopTravseForNestedAccums {
      template<typename myKey_t, class sumField_t, class minField_t, class maxField_t, class avgField_t>
      struct GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField {
        struct accumTuple {
          sumField_t sumField;
          minField_t minField;
          maxField_t maxField;
          avgField_t avgField;
          
          accumTuple() {
            sumField = sumField_t();
            minField = minField_t();
            maxField = maxField_t();
            avgField = avgField_t();
          }
          
          accumTuple(sumField_t sumField_, minField_t minField_, maxField_t maxField_, avgField_t avgField_) {
            sumField = sumField_;
            minField = minField_;
            maxField = maxField_;
            avgField = avgField_;
          }
          
          accumTuple(const std::tuple<sumField_t, minField_t, maxField_t, avgField_t>& __GQUERY__other__576037) {
            sumField = std::get<0>(__GQUERY__other__576037);
            minField = std::get<1>(__GQUERY__other__576037);
            maxField = std::get<2>(__GQUERY__other__576037);
            avgField = std::get<3>(__GQUERY__other__576037);
          }
          
          friend std::ostream& operator<<(std::ostream& os, const accumTuple& __GQUERY__other__576037) {
            os << "[";
            os << "sumField " << __GQUERY__other__576037.sumField << "|";
            os << "minField " << __GQUERY__other__576037.minField << "|";
            os << "maxField " << __GQUERY__other__576037.maxField << "|";
            os << "avgField " << __GQUERY__other__576037.avgField << "]";
            return os;
          }
          
          bool operator==(const accumTuple& __GQUERY__other__576037) const {
            return
              sumField == __GQUERY__other__576037.sumField &&
              minField == __GQUERY__other__576037.minField &&
              maxField == __GQUERY__other__576037.maxField &&
              avgField == __GQUERY__other__576037.avgField;
          }
          
          accumTuple& operator+=(const accumTuple& __GQUERY__other__576037) {
            sumField += __GQUERY__other__576037.sumField;
            minField += __GQUERY__other__576037.minField;
            maxField += __GQUERY__other__576037.maxField;
            avgField += __GQUERY__other__576037.avgField;
            return *this;
          }
          
          bool operator<(const accumTuple& __GQUERY__other__576037) const {
            if (sumField > __GQUERY__other__576037.sumField) return true;
            if (sumField < __GQUERY__other__576037.sumField) return false;
            if (minField > __GQUERY__other__576037.minField) return true;
            if (minField < __GQUERY__other__576037.minField) return false;
            if (maxField > __GQUERY__other__576037.maxField) return true;
            if (maxField < __GQUERY__other__576037.maxField) return false;
            if (avgField > __GQUERY__other__576037.avgField) return true;
            if (avgField < __GQUERY__other__576037.avgField) return false;
            return false;
          }
          
          bool operator>(const accumTuple& __GQUERY__other__576037) const {
            if (sumField < __GQUERY__other__576037.sumField) return true;
            if (sumField > __GQUERY__other__576037.sumField) return false;
            if (minField < __GQUERY__other__576037.minField) return true;
            if (minField > __GQUERY__other__576037.minField) return false;
            if (maxField < __GQUERY__other__576037.maxField) return true;
            if (maxField > __GQUERY__other__576037.maxField) return false;
            if (avgField < __GQUERY__other__576037.avgField) return true;
            if (avgField > __GQUERY__other__576037.avgField) return false;
            return false;
          }
          
          operator std::tuple<sumField_t, minField_t, maxField_t, avgField_t>() const {
            return std::make_tuple(sumField,minField,maxField,avgField);
          }
          
          void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
            gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
              writer.WriteStartObject();
            writer.WriteNameString("sumField");
            (sumField).json_printer(writer, _request, graphAPI, true);
            writer.WriteNameString("minField");
            (minField).json_printer(writer, _request, graphAPI, true);
            writer.WriteNameString("maxField");
            (maxField).json_printer(writer, _request, graphAPI, true);
            writer.WriteNameString("avgField");
            (avgField).json_printer(writer, _request, graphAPI, true);
            writer.WriteEndObject();
          }
          gutil::JSONWriter& json_write_name (
            gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
            gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
              std::string ss = boost::lexical_cast<std::string>(*this);
            return writer.WriteNameString(ss.c_str());
          }
          template <class ARCHIVE>
          void serialize(ARCHIVE& __GQUERY__ar_) {
            __GQUERY__ar_(sumField, minField, maxField, avgField);
          }
        };
        MapAccum<string, accumTuple>  map;
        string baseItem;
        accumTuple accumItem;
        
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField() { }
        
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField(string base_0, sumField_t accum_0, minField_t accum_1, maxField_t accum_2, avgField_t accum_3) {
          map = MapAccum<string, accumTuple>(base_0, accumTuple(accum_0, accum_1, accum_2, accum_3));
        }
        
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(map, baseItem, accumItem);
        }
        friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField& m) {
          os << m.map;
          return os ;
        }
        bool operator==(GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) const {
          return map == __GQUERY__other__576037.map;
        }
        bool operator!=(GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) const {
          return map != __GQUERY__other__576037.map;
        }
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField operator+ (GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) {
          GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField newG;
          newG += *this;
          newG += __GQUERY__other__576037;
          return newG;
        }
        void operator= (GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) {
          map = __GQUERY__other__576037.map;
        }
        void operator+=(GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) {
          map += __GQUERY__other__576037.map;
        }
        void json_printer (gutil::JSONWriter& writer,
          gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
            writer.WriteStartArray();
          for (auto it = map.begin(); it != map.end(); it++) {
            writer.WriteStartObject();
            writer.WriteName("myKey");
            json_printer_util(it->first, writer, _request, graphAPI, verbose);
            writer.WriteName("sumField");
            json_printer_util(it->second.sumField, writer, _request, graphAPI, verbose);
            writer.WriteName("minField");
            json_printer_util(it->second.minField, writer, _request, graphAPI, verbose);
            writer.WriteName("maxField");
            json_printer_util(it->second.maxField, writer, _request, graphAPI, verbose);
            writer.WriteName("avgField");
            json_printer_util(it->second.avgField, writer, _request, graphAPI, verbose);
            writer.WriteEndObject();
          }
          writer.WriteEndArray();
        }
        accumTuple get (string base_0) const {
          return map.get(base_0);
        }
        int size () const {
          return map.size();
        }
        bool containskey (string base_0) const {
          return map.containskey(base_0);
        }
        void clear () {
          map.clear();
        }
        void remove (string base_0) {
          map.remove(base_0);
        }
        void remove (GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField const &__GQUERY__other__576037) {
          map.remove(__GQUERY__other__576037.map);
        }
        decltype(map.begin()) begin () {
          return map.begin();
        }
        decltype(map.end()) end () {
          return map.end();
        }
      };
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_1__576037 {
        // accumulators:
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  >  summaryAccum_1;
        // default constructor
        __GQUERY__VertexVal_1__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_1__576037 (const __GQUERY__VertexVal_1__576037& __GQUERY__other_) {
          summaryAccum_1 = __GQUERY__other_.summaryAccum_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(summaryAccum_1);
        }
      };
      struct __GQUERY__VertexVal_2__576037 {
        // accumulators:
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SumAccum<double > , MinAccum<double > , MaxAccum<double > , AvgAccum >  singleAccum_1;
        // default constructor
        __GQUERY__VertexVal_2__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_2__576037 (const __GQUERY__VertexVal_2__576037& __GQUERY__other_) {
          singleAccum_1 = __GQUERY__other_.singleAccum_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(singleAccum_1);
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  > * summaryAccum_1;
        void add (const __GQUERY__VertexVal_1__576037& __GQUERY__other_) {
          summaryAccum_1 = const_cast<GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  > * > (&__GQUERY__other_.summaryAccum_1);
        }
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SumAccum<double > , MinAccum<double > , MaxAccum<double > , AvgAccum > * singleAccum_1;
        void add (const __GQUERY__VertexVal_2__576037& __GQUERY__other_) {
          singleAccum_1 = const_cast<GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SumAccum<double > , MinAccum<double > , MaxAccum<double > , AvgAccum > * > (&__GQUERY__other_.singleAccum_1);
        }
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  > * summaryAccum_1Ptr;
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SumAccum<double > , MinAccum<double > , MaxAccum<double > , AvgAccum > * singleAccum_1Ptr;
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  > & summaryAccum_1 = *summaryAccum_1Ptr;
        GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SumAccum<double > , MinAccum<double > , MaxAccum<double > , AvgAccum > & singleAccum_1 = *singleAccum_1Ptr;
        // default constructor
        __GQUERY__VertexVal__576037 () {};
        // constructor
        __GQUERY__VertexVal__576037 (__GQUERY__VertexVal_ptr__576037& __GQUERY__other_) : summaryAccum_1(*__GQUERY__other_.summaryAccum_1), singleAccum_1(*__GQUERY__other_.singleAccum_1)
          {}
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testForLoopTravseForNestedAccums (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_testForLoopTravseForNestedAccums (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_testForLoopTravseForNestedAccums () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void VertexMap_2(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_1__576037 l_val_1(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_1__576037>());
        lptr.add(l_val_1);
        __GQUERY__VertexVal_2__576037 l_val_2(*vVertex.GetValuePtr(2).GetRawPtr<__GQUERY__VertexVal_2__576037>());
        lptr.add(l_val_2);
        V_VALUE l_val(lptr);
        
        // foreach itr in s.@singleAccum do s.@summaryAccum += ( "singleKey" -> itr.sumField, itr.minField, itr.maxField, itr.avgField ) end
        if (delta.__GQUERY__isSrc__576037) {
          uint64_t __GQUERY__iter_2 = 0;
          const auto&  __GQUERY__accum_2 = l_val.singleAccum_1;
          for (auto it = ( __GQUERY__accum_2).begin(); it != ( __GQUERY__accum_2).end(); it++) {
            auto& lvar_itr_first = it->first;
            auto& lvar_itr_second = it->second;
            
            // s.@summaryAccum += ( "singleKey" -> itr.sumField, itr.minField, itr.maxField, itr.avgField )
            if (delta.__GQUERY__isSrc__576037) {
              l_val.summaryAccum_1 += GroupByAccum_1_string_myKey_4_sumField_minField_maxField_avgField<string, SetAccum<double > , SetAccum<double > , SetAccum<double > , SetAccum<double >  > (string("singleKey"),lvar_itr_second.sumField.data_,lvar_itr_second.minField.data_,lvar_itr_second.maxField.data_,lvar_itr_second.avgField.data_);
              
            }
            if (UNLIKELY((++__GQUERY__iter_2 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
              std::string msg("Aborted due to timeout or system memory in critical state.");
              HF_set_error(_request, msg, true);
              return;
            }
          }
          
        }
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_1, 0);
        context.Write(v, l_val_2, 1);
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("testForLoopTravseForNestedAccums")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
        
        {
          /*
          res = select s from members : s post-accum foreach itr in s.@singleAccum do s.@summaryAccum += ( "singleKey" -> itr.sumField, itr.minField, itr.maxField, itr.avgField ) end;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_res_size = __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "master starts action_2_reduce";
          if (!manager.RunCMD(
            action_2_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_res_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "action_2_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value() -= __vset_res_size;
          __GQUERY_GV_Global_Variable__vset_res_hasOrder_.Value() = false;
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          gpr::GPR_Container* __GQUERY__value_container1 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_1__576037>();
          gutil::ScopedCleanUp sc_val1(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container1, &_request));
          gpr::GPR_Container* __GQUERY__value_container2 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_2__576037>();
          gutil::ScopedCleanUp sc_val2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container2, &_request));
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_members, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_SIZE__) __GQUERY_GV_Global_Variable__vset_res_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums::VertexMap_2, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container2},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container2}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_2_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_res_SIZE__) __GQUERY_GV_Global_Variable__vset_res_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_res->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums::Reduce_2, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container2,__GQUERY__value_container1,__GQUERY__value_container2},//input container(old v_value and delta)
                  {__GQUERY__value_container1,__GQUERY__value_container2},//output container(new v_value)
                  {__GQUERY__vSet_res.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_res_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value() = __GQUERY__vSet_res->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_SIZE___output.Value() = __GQUERY__vSet_res->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_res_SIZE___output});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_members = 1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      public:
        
        ///return vars
      };//end class GPR_testForLoopTravseForNestedAccums
    bool call_testForLoopTravseForNestedAccums(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testForLoopTravseForNestedAccums_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testForLoopTravseForNestedAccums(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testForLoopTravseForNestedAccums") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testForLoopTravseForNestedAccums(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testForLoopTravseForNestedAccums(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::poc_graph::GPR_testForLoopTravseForNestedAccums gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace poc_graph
}//end namespace UDIMPL
