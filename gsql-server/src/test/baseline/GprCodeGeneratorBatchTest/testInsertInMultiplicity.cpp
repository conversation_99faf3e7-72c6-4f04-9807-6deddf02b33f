/********** query start ***************
create distributed query q1() {
W = select t
    from v1:s-(e3>)-v2-(e4>.e5>)-v_list:t
    ACCUM insert into v_list values("2", 1),
          insert into e2 values(s, t, 1, 1),
          insert into e6 values(s, t, 1, 1, 1, 1);
W = select t
    from v1:s-(e3>)-v2-(e4>.e5>)-v_list:t
    ACCUM insert into v_list1 (primary_id, lucky_nums) values("2", 1),
          insert into e2 (from, to, l1) values(s, t, 1),
          insert into e6 (from, to, DISCRIMINATOR(ii, iv), l1) values(s, t, 1, 1, 1);
}
********** query end ***************/
#include "complex_attribute-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace complex_attribute {
    class GPR_q1 {
      
      struct Delta_2 {
        // accumulators
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_1_1;
        bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1;
        bool __GQUERY__set___576037srcIdOnlyPropagAcc_1_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_2 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = false;
          __GQUERY__set___576037srcIdOnlyPropagAcc_1_1 = false;
        }
        // message combinator
        Delta_2& operator+= (const Delta_2& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1) {
            srcIdOnlyPropagAcc_1_1 += __GQUERY__other_.srcIdOnlyPropagAcc_1_1;
            __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037srcIdOnlyPropagAcc_1_1) {
            srcIdOnlyPropagAcc_1_1 = __GQUERY__other_.srcIdOnlyPropagAcc_1_1;
            __GQUERY__set___576037srcIdOnlyPropagAcc_1_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, srcIdOnlyPropagAcc_1_1, __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1, __GQUERY__set___576037srcIdOnlyPropagAcc_1_1);
        }
      };
      struct Delta_3 {
        // accumulators
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_2_1;
        bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1;
        bool __GQUERY__set___576037srcIdOnlyPropagAcc_2_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_3 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = false;
          __GQUERY__set___576037srcIdOnlyPropagAcc_2_1 = false;
        }
        // message combinator
        Delta_3& operator+= (const Delta_3& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1) {
            srcIdOnlyPropagAcc_2_1 += __GQUERY__other_.srcIdOnlyPropagAcc_2_1;
            __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037srcIdOnlyPropagAcc_2_1) {
            srcIdOnlyPropagAcc_2_1 = __GQUERY__other_.srcIdOnlyPropagAcc_2_1;
            __GQUERY__set___576037srcIdOnlyPropagAcc_2_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, srcIdOnlyPropagAcc_2_1, __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1, __GQUERY__set___576037srcIdOnlyPropagAcc_2_1);
        }
      };
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct Delta_6 {
        // accumulators
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_4_1;
        bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1;
        bool __GQUERY__set___576037srcIdOnlyPropagAcc_4_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_6 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1 = false;
          __GQUERY__set___576037srcIdOnlyPropagAcc_4_1 = false;
        }
        // message combinator
        Delta_6& operator+= (const Delta_6& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1) {
            srcIdOnlyPropagAcc_4_1 += __GQUERY__other_.srcIdOnlyPropagAcc_4_1;
            __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037srcIdOnlyPropagAcc_4_1) {
            srcIdOnlyPropagAcc_4_1 = __GQUERY__other_.srcIdOnlyPropagAcc_4_1;
            __GQUERY__set___576037srcIdOnlyPropagAcc_4_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, srcIdOnlyPropagAcc_4_1, __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1, __GQUERY__set___576037srcIdOnlyPropagAcc_4_1);
        }
      };
      struct Delta_7 {
        // accumulators
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_5_1;
        bool __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1;
        bool __GQUERY__set___576037srcIdOnlyPropagAcc_5_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_7 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1 = false;
          __GQUERY__set___576037srcIdOnlyPropagAcc_5_1 = false;
        }
        // message combinator
        Delta_7& operator+= (const Delta_7& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1) {
            srcIdOnlyPropagAcc_5_1 += __GQUERY__other_.srcIdOnlyPropagAcc_5_1;
            __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037srcIdOnlyPropagAcc_5_1) {
            srcIdOnlyPropagAcc_5_1 = __GQUERY__other_.srcIdOnlyPropagAcc_5_1;
            __GQUERY__set___576037srcIdOnlyPropagAcc_5_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, srcIdOnlyPropagAcc_5_1, __GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1, __GQUERY__set___576037srcIdOnlyPropagAcc_5_1);
        }
      };
      struct __GQUERY__VertexVal_1__576037 {
        // accumulators:
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_4_1;
        // default constructor
        __GQUERY__VertexVal_1__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_1__576037 (const __GQUERY__VertexVal_1__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_4_1 = __GQUERY__other_.srcIdOnlyPropagAcc_4_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(srcIdOnlyPropagAcc_4_1);
        }
      };
      struct __GQUERY__VertexVal_2__576037 {
        // accumulators:
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_5_1;
        // default constructor
        __GQUERY__VertexVal_2__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_2__576037 (const __GQUERY__VertexVal_2__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_5_1 = __GQUERY__other_.srcIdOnlyPropagAcc_5_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(srcIdOnlyPropagAcc_5_1);
        }
      };
      struct __GQUERY__VertexVal_3__576037 {
        // accumulators:
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_2_1;
        // default constructor
        __GQUERY__VertexVal_3__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_3__576037 (const __GQUERY__VertexVal_3__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_2_1 = __GQUERY__other_.srcIdOnlyPropagAcc_2_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(srcIdOnlyPropagAcc_2_1);
        }
      };
      struct __GQUERY__VertexVal_4__576037 {
        // accumulators:
        SetAccum<VERTEX >  srcIdOnlyPropagAcc_1_1;
        // default constructor
        __GQUERY__VertexVal_4__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_4__576037 (const __GQUERY__VertexVal_4__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_1_1 = __GQUERY__other_.srcIdOnlyPropagAcc_1_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(srcIdOnlyPropagAcc_1_1);
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_4_1;
        void add (const __GQUERY__VertexVal_1__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_4_1 = const_cast<SetAccum<VERTEX > * > (&__GQUERY__other_.srcIdOnlyPropagAcc_4_1);
        }
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_5_1;
        void add (const __GQUERY__VertexVal_2__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_5_1 = const_cast<SetAccum<VERTEX > * > (&__GQUERY__other_.srcIdOnlyPropagAcc_5_1);
        }
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_2_1;
        void add (const __GQUERY__VertexVal_3__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_2_1 = const_cast<SetAccum<VERTEX > * > (&__GQUERY__other_.srcIdOnlyPropagAcc_2_1);
        }
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_1_1;
        void add (const __GQUERY__VertexVal_4__576037& __GQUERY__other_) {
          srcIdOnlyPropagAcc_1_1 = const_cast<SetAccum<VERTEX > * > (&__GQUERY__other_.srcIdOnlyPropagAcc_1_1);
        }
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_4_1Ptr;
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_5_1Ptr;
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_2_1Ptr;
        SetAccum<VERTEX > * srcIdOnlyPropagAcc_1_1Ptr;
        SetAccum<VERTEX > & srcIdOnlyPropagAcc_4_1 = *srcIdOnlyPropagAcc_4_1Ptr;
        SetAccum<VERTEX > & srcIdOnlyPropagAcc_5_1 = *srcIdOnlyPropagAcc_5_1Ptr;
        SetAccum<VERTEX > & srcIdOnlyPropagAcc_2_1 = *srcIdOnlyPropagAcc_2_1Ptr;
        SetAccum<VERTEX > & srcIdOnlyPropagAcc_1_1 = *srcIdOnlyPropagAcc_1_1Ptr;
        // default constructor
        __GQUERY__VertexVal__576037 () {};
        // constructor
        __GQUERY__VertexVal__576037 (__GQUERY__VertexVal_ptr__576037& __GQUERY__other_) : srcIdOnlyPropagAcc_4_1(*__GQUERY__other_.srcIdOnlyPropagAcc_4_1), srcIdOnlyPropagAcc_5_1(*__GQUERY__other_.srcIdOnlyPropagAcc_5_1), srcIdOnlyPropagAcc_2_1(*__GQUERY__other_.srcIdOnlyPropagAcc_2_1), srcIdOnlyPropagAcc_1_1(*__GQUERY__other_.srcIdOnlyPropagAcc_1_1)
          {}
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_q1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi), manager(_serviceapi, &_request) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_v_list_attrMeta = meta->GetVertexType(4).attributes_;
        _schema_VATT_v_list_576037_lucky_nums = VTY_v_list_attrMeta.GetAttributePosition("lucky_nums", true);
        topology4::AttributesMeta& VTY_v_list1_attrMeta = meta->GetVertexType(5).attributes_;
        _schema_VATT_v_list1_576037_lucky_nums = VTY_v_list1_attrMeta.GetAttributePosition("lucky_nums", true);
        topology4::AttributesMeta& ETY_e2_attrMeta = meta->GetEdgeType(1).attributes_;
        _schema_EATT_e2_576037_val = ETY_e2_attrMeta.GetAttributePosition("val", true);
        _schema_EATT_e2_576037_l1 = ETY_e2_attrMeta.GetAttributePosition("l1", true);
        topology4::AttributesMeta& ETY_e6_attrMeta = meta->GetEdgeType(5).attributes_;
        _schema_EATT_e6_576037_ii = ETY_e6_attrMeta.GetAttributePosition("ii", true);
        _schema_EATT_e6_576037_iv = ETY_e6_attrMeta.GetAttributePosition("iv", true);
        _schema_EATT_e6_576037_val = ETY_e6_attrMeta.GetAttributePosition("val", true);
        _schema_EATT_e6_576037_l1 = ETY_e6_attrMeta.GetAttributePosition("l1", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_q1 (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi), manager(_serviceapi, &_request) {
        isQueryCalled = _isQueryCalled_;
        __GQUERY__graphupdate = _graphupdates_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_v_list_attrMeta = meta->GetVertexType(4).attributes_;
        _schema_VATT_v_list_576037_lucky_nums = VTY_v_list_attrMeta.GetAttributePosition("lucky_nums", true);
        topology4::AttributesMeta& VTY_v_list1_attrMeta = meta->GetVertexType(5).attributes_;
        _schema_VATT_v_list1_576037_lucky_nums = VTY_v_list1_attrMeta.GetAttributePosition("lucky_nums", true);
        topology4::AttributesMeta& ETY_e2_attrMeta = meta->GetEdgeType(1).attributes_;
        _schema_EATT_e2_576037_val = ETY_e2_attrMeta.GetAttributePosition("val", true);
        _schema_EATT_e2_576037_l1 = ETY_e2_attrMeta.GetAttributePosition("l1", true);
        topology4::AttributesMeta& ETY_e6_attrMeta = meta->GetEdgeType(5).attributes_;
        _schema_EATT_e6_576037_ii = ETY_e6_attrMeta.GetAttributePosition("ii", true);
        _schema_EATT_e6_576037_iv = ETY_e6_attrMeta.GetAttributePosition("iv", true);
        _schema_EATT_e6_576037_val = ETY_e6_attrMeta.GetAttributePosition("val", true);
        _schema_EATT_e6_576037_l1 = ETY_e6_attrMeta.GetAttributePosition("l1", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_q1 () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void EdgeMap_2(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_2 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_v2 >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_v2)
          return;
        
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        Delta_2 tgt_delta = Delta_2();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        
        // x_1.@srcIdOnlyPropagAcc_1 += s
        tgt_delta.srcIdOnlyPropagAcc_1_1 += src;
        tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1 = true;
        context.Write(tgt, tgt_delta, 0);
        context.Activate(tgt, 0);
      }
      void Reduce_2(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_2 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_2>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_4__576037 l_val_4(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_4__576037>());
        lptr.add(l_val_4);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_1_1) {
          l_val.srcIdOnlyPropagAcc_1_1 += delta.srcIdOnlyPropagAcc_1_1;
        } else if (delta.__GQUERY__set___576037srcIdOnlyPropagAcc_1_1) {
          l_val.srcIdOnlyPropagAcc_1_1 = delta.srcIdOnlyPropagAcc_1_1;
        }
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_4, 0);
      }
      void EdgeMap_3(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_3 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        
        __GQUERY__VertexVal_ptr__576037 vptr_src_val;
        vptr_src_val.add(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_4__576037>());
        V_VALUE src_val(vptr_src_val);
        __GQUERY__VertexVal_ptr__576037 vptr_tgt_val;
        V_VALUE tgt_val(vptr_tgt_val);
        // prepare message
        Delta_3 tgt_delta = Delta_3();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        Delta_3 src_delta = Delta_3();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // x_4.@srcIdOnlyPropagAcc_2 += x_1.@srcIdOnlyPropagAcc_1
        tgt_delta.srcIdOnlyPropagAcc_2_1 += src_val.srcIdOnlyPropagAcc_1_1;
        tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1 = true;
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_3(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_3 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_3>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_4__576037 l_val_4(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_4__576037>());
        lptr.add(l_val_4);
        __GQUERY__VertexVal_3__576037 l_val_3(*vVertex.GetValuePtr(2).GetRawPtr<__GQUERY__VertexVal_3__576037>());
        lptr.add(l_val_3);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_2_1) {
          l_val.srcIdOnlyPropagAcc_2_1 += delta.srcIdOnlyPropagAcc_2_1;
        } else if (delta.__GQUERY__set___576037srcIdOnlyPropagAcc_2_1) {
          l_val.srcIdOnlyPropagAcc_2_1 = delta.srcIdOnlyPropagAcc_2_1;
        }
        
        // x_1.@srcIdOnlyPropagAcc_1.clear ( )
        if (delta.__GQUERY__isSrc__576037) {
          l_val.srcIdOnlyPropagAcc_1_1.clear();
        }
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_4, 0);
        context.Write(v, l_val_3, 1);
      }
      void EdgeMap_4(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        // type filters
        if (_schema_VTY_v_list >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_v_list)
          return;
        
        __GQUERY__VertexVal_ptr__576037 vptr_src_val;
        vptr_src_val.add(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_3__576037>());
        V_VALUE src_val(vptr_src_val);
        __GQUERY__VertexVal_ptr__576037 vptr_tgt_val;
        V_VALUE tgt_val(vptr_tgt_val);
        // prepare message
        DefaultDelta tgt_delta = DefaultDelta();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // foreach s in x_4.@srcIdOnlyPropagAcc_2 do case when s.type == "v1" then insert into v_list values ( "2", 1 ), insert into e2 values ( s, t, 1, 1 ), insert into e6 values ( s, t, 1, 1, 1, 1 ) end end
        uint64_t __GQUERY__iter_8 = 0;
        const auto&  __GQUERY__accum_8 = src_val.srcIdOnlyPropagAcc_2_1;
        for (auto it = ( __GQUERY__accum_8).begin(); it != ( __GQUERY__accum_8).end(); it++) {
          auto& lvar_s = *it;
          
          // case when s.type == "v1" then insert into v_list values ( "2", 1 ), insert into e2 values ( s, t, 1, 1 ), insert into e6 values ( s, t, 1, 1, 1, 1 ) end
          if ((_schema_VTY_v1 >= 0 && graphAPI->GetVertexType(lvar_s) == (unsigned) _schema_VTY_v1)) {
            
            // insert into v_list values ( "2", 1 )
            {
              if (GSQL_UTIL::__to_string(string("2")) == string("")) {
                string msg("Runtime Error: empty vertex id in 'insert into v_list values ( \"2\", 1 )'");
                throw gutil::GsqlException(msg, gutil::error_t::E_VERTEX_ID);
              }
              topology4::DeltaVertexId deltaid_ (_schema_VTY_v_list, GSQL_UTIL::__to_string(string("2")));
              auto graphupdate_ = context.GetGraphUpdate();
              {
                gutil::GLock lock(graphupdate_->GetMutex());
                if (graphupdate_->GetAU(deltaid_)->IsUpdatable(_schema_VATT_v_list_576037_lucky_nums)) { 
                  graphupdate_->GetAU(deltaid_)->Set(_schema_VATT_v_list_576037_lucky_nums, ListAccum<int64_t > (1l).get_data(), topology4::DeltaAttributeOperator_Add);
                } else {
                  graphupdate_->GetAU(deltaid_)->ThrowDiscriminatorError(_schema_VATT_v_list_576037_lucky_nums);
                }
              }
              graphupdate_->CheckForFlush();
            }
            
            // insert into e2 values ( s, t, 1, 1 )
            {
              size_t e_typeIDVar = _schema_ETY_e2;
              uint32_t src_typeIDVar = graphAPI->GetVertexType(lvar_s);
              uint32_t tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              topology4::DeltaVertexId srcdeltaid_ (src_typeIDVar, lvar_s);
              topology4::DeltaVertexId tgtdeltaid_ (tgt_typeIDVar, tgt);
              if (srcdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e2") + "' with an invalid FROM vertex 's'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              if (tgtdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e2") + "' with an invalid TO vertex 'tgt'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              string srcTy = _serviceapi->GetTopologyMeta()->GetVertexType(src_typeIDVar).typename_;
              string tgtTy = _serviceapi->GetTopologyMeta()->GetVertexType(tgt_typeIDVar).typename_;
              topology4::EdgeTypeMeta& eMeta = _serviceapi->GetTopologyMeta()->GetEdgeType(e_typeIDVar);
              string eName = eMeta.typename_;
              bool _GQUERY_TYPE_CHECK_VALID = true;
              if (UNLIKELY(!eMeta.edgepairs_.empty())) {
                std::pair<string, string> p = std::make_pair(srcTy, tgtTy);
                if (std::find(eMeta.edgepairs_.begin(), eMeta.edgepairs_.end(), p) == eMeta.edgepairs_.end()) {
                  tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, tgt_typeIDVar);
                  __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                  _GQUERY_TYPE_CHECK_VALID = false;
                }
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.fromtypename_) != src_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, -1);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.totypename_) != tgt_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, -1, tgt_typeIDVar);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              }
              if (_GQUERY_TYPE_CHECK_VALID) {
                auto graphupdate_ = context.GetGraphUpdate();
                uint32_t esubtypeid = 0;
                std::stringstream multiedgeIdStream;
                bool isInsKey = false;
                if (eMeta.discriminator_count_ > 0) {
                  gutil::EnableDiscriminator(esubtypeid);
                  isInsKey = true;
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e2_576037_val].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e2_576037_l1].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                std::string multiedgeId = multiedgeIdStream.str();
                {
                  gutil::GLock lock(graphupdate_->GetMutex());
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e2_576037_val, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const ListAccum<int64_t > & attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e2_576037_l1, attrValue.get_data(), topology4::DeltaAttributeOperator_Add);
                  }
                }
                graphupdate_->CheckForFlush();
              }
            }
            
            // insert into e6 values ( s, t, 1, 1, 1, 1 )
            {
              size_t e_typeIDVar = _schema_ETY_e6;
              uint32_t src_typeIDVar = graphAPI->GetVertexType(lvar_s);
              uint32_t tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              topology4::DeltaVertexId srcdeltaid_ (src_typeIDVar, lvar_s);
              topology4::DeltaVertexId tgtdeltaid_ (tgt_typeIDVar, tgt);
              if (srcdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e6") + "' with an invalid FROM vertex 's'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              if (tgtdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e6") + "' with an invalid TO vertex 'tgt'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              string srcTy = _serviceapi->GetTopologyMeta()->GetVertexType(src_typeIDVar).typename_;
              string tgtTy = _serviceapi->GetTopologyMeta()->GetVertexType(tgt_typeIDVar).typename_;
              topology4::EdgeTypeMeta& eMeta = _serviceapi->GetTopologyMeta()->GetEdgeType(e_typeIDVar);
              string eName = eMeta.typename_;
              bool _GQUERY_TYPE_CHECK_VALID = true;
              if (UNLIKELY(!eMeta.edgepairs_.empty())) {
                std::pair<string, string> p = std::make_pair(srcTy, tgtTy);
                if (std::find(eMeta.edgepairs_.begin(), eMeta.edgepairs_.end(), p) == eMeta.edgepairs_.end()) {
                  tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, tgt_typeIDVar);
                  __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                  _GQUERY_TYPE_CHECK_VALID = false;
                }
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.fromtypename_) != src_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, -1);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.totypename_) != tgt_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, -1, tgt_typeIDVar);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              }
              if (_GQUERY_TYPE_CHECK_VALID) {
                auto graphupdate_ = context.GetGraphUpdate();
                uint32_t esubtypeid = 0;
                std::stringstream multiedgeIdStream;
                bool isInsKey = false;
                if (eMeta.discriminator_count_ > 0) {
                  gutil::EnableDiscriminator(esubtypeid);
                  isInsKey = true;
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_ii].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_iv].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_val].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_l1].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                std::string multiedgeId = multiedgeIdStream.str();
                {
                  gutil::GLock lock(graphupdate_->GetMutex());
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_ii, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_iv, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_val, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const ListAccum<int64_t > & attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_l1, attrValue.get_data(), topology4::DeltaAttributeOperator_Add);
                  }
                }
                graphupdate_->CheckForFlush();
              }
            }
          }
          if (UNLIKELY((++__GQUERY__iter_8 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
            HF_set_error(_request, msg, true);
            return;
          }
        }
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_4(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_3__576037 l_val_3(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_3__576037>());
        lptr.add(l_val_3);
        V_VALUE l_val(lptr);
        
        // x_4.@srcIdOnlyPropagAcc_2.clear ( )
        if (delta.__GQUERY__isSrc__576037) {
          l_val.srcIdOnlyPropagAcc_2_1.clear();
        }
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_3, 0);
      }
      void EdgeMap_6(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_6 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_v2 >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_v2)
          return;
        
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        Delta_6 tgt_delta = Delta_6();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        
        // x_9.@srcIdOnlyPropagAcc_4 += s
        tgt_delta.srcIdOnlyPropagAcc_4_1 += src;
        tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1 = true;
        context.Write(tgt, tgt_delta, 0);
        context.Activate(tgt, 0);
      }
      void Reduce_6(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_6 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_6>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_1__576037 l_val_1(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_1__576037>());
        lptr.add(l_val_1);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_4_1) {
          l_val.srcIdOnlyPropagAcc_4_1 += delta.srcIdOnlyPropagAcc_4_1;
        } else if (delta.__GQUERY__set___576037srcIdOnlyPropagAcc_4_1) {
          l_val.srcIdOnlyPropagAcc_4_1 = delta.srcIdOnlyPropagAcc_4_1;
        }
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_1, 0);
      }
      void EdgeMap_7(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_7 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        
        __GQUERY__VertexVal_ptr__576037 vptr_src_val;
        vptr_src_val.add(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_1__576037>());
        V_VALUE src_val(vptr_src_val);
        __GQUERY__VertexVal_ptr__576037 vptr_tgt_val;
        V_VALUE tgt_val(vptr_tgt_val);
        // prepare message
        Delta_7 tgt_delta = Delta_7();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        Delta_7 src_delta = Delta_7();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // x_12.@srcIdOnlyPropagAcc_5 += x_9.@srcIdOnlyPropagAcc_4
        tgt_delta.srcIdOnlyPropagAcc_5_1 += src_val.srcIdOnlyPropagAcc_4_1;
        tgt_delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1 = true;
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_7(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_7 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_7>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_1__576037 l_val_1(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_1__576037>());
        lptr.add(l_val_1);
        __GQUERY__VertexVal_2__576037 l_val_2(*vVertex.GetValuePtr(2).GetRawPtr<__GQUERY__VertexVal_2__576037>());
        lptr.add(l_val_2);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037srcIdOnlyPropagAcc_5_1) {
          l_val.srcIdOnlyPropagAcc_5_1 += delta.srcIdOnlyPropagAcc_5_1;
        } else if (delta.__GQUERY__set___576037srcIdOnlyPropagAcc_5_1) {
          l_val.srcIdOnlyPropagAcc_5_1 = delta.srcIdOnlyPropagAcc_5_1;
        }
        
        // x_9.@srcIdOnlyPropagAcc_4.clear ( )
        if (delta.__GQUERY__isSrc__576037) {
          l_val.srcIdOnlyPropagAcc_4_1.clear();
        }
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_1, 0);
        context.Write(v, l_val_2, 1);
      }
      void EdgeMap_8(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        // type filters
        if (_schema_VTY_v_list >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_v_list)
          return;
        
        __GQUERY__VertexVal_ptr__576037 vptr_src_val;
        vptr_src_val.add(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_2__576037>());
        V_VALUE src_val(vptr_src_val);
        __GQUERY__VertexVal_ptr__576037 vptr_tgt_val;
        V_VALUE tgt_val(vptr_tgt_val);
        // prepare message
        DefaultDelta tgt_delta = DefaultDelta();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // foreach s in x_12.@srcIdOnlyPropagAcc_5 do case when s.type == "v1" then insert into v_list1 ( primary_id, lucky_nums ) values ( "2", 1 ), insert into e2 ( from, to, l1 ) values ( s, t, 1 ), insert into e6 ( from, to, discriminator ( ii, iv ), l1 ) values ( s, t, 1, 1, 1 ) end end
        uint64_t __GQUERY__iter_17 = 0;
        const auto&  __GQUERY__accum_17 = src_val.srcIdOnlyPropagAcc_5_1;
        for (auto it = ( __GQUERY__accum_17).begin(); it != ( __GQUERY__accum_17).end(); it++) {
          auto& lvar_s = *it;
          
          // case when s.type == "v1" then insert into v_list1 ( primary_id, lucky_nums ) values ( "2", 1 ), insert into e2 ( from, to, l1 ) values ( s, t, 1 ), insert into e6 ( from, to, discriminator ( ii, iv ), l1 ) values ( s, t, 1, 1, 1 ) end
          if ((_schema_VTY_v1 >= 0 && graphAPI->GetVertexType(lvar_s) == (unsigned) _schema_VTY_v1)) {
            
            // insert into v_list1 ( primary_id, lucky_nums ) values ( "2", 1 )
            {
              if (GSQL_UTIL::__to_string(string("2")) == string("")) {
                string msg("Runtime Error: empty vertex id in 'insert into v_list1 ( primary_id, lucky_nums ) values ( \"2\", 1 )'");
                throw gutil::GsqlException(msg, gutil::error_t::E_VERTEX_ID);
              }
              topology4::DeltaVertexId deltaid_ (_schema_VTY_v_list1, GSQL_UTIL::__to_string(string("2")));
              auto graphupdate_ = context.GetGraphUpdate();
              {
                gutil::GLock lock(graphupdate_->GetMutex());
                if (graphupdate_->GetAU(deltaid_)->IsUpdatable(_schema_VATT_v_list1_576037_lucky_nums)) { 
                  graphupdate_->GetAU(deltaid_)->Set(_schema_VATT_v_list1_576037_lucky_nums, ListAccum<int64_t > (1l).get_data(), topology4::DeltaAttributeOperator_Add);
                } else {
                  graphupdate_->GetAU(deltaid_)->ThrowDiscriminatorError(_schema_VATT_v_list1_576037_lucky_nums);
                }
              }
              graphupdate_->CheckForFlush();
            }
            
            // insert into e2 ( from, to, l1 ) values ( s, t, 1 )
            {
              size_t e_typeIDVar = _schema_ETY_e2;
              uint32_t src_typeIDVar = graphAPI->GetVertexType(lvar_s);
              uint32_t tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              topology4::DeltaVertexId srcdeltaid_ (src_typeIDVar, lvar_s);
              topology4::DeltaVertexId tgtdeltaid_ (tgt_typeIDVar, tgt);
              if (srcdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e2") + "' with an invalid FROM vertex 's'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              if (tgtdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e2") + "' with an invalid TO vertex 'tgt'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              string srcTy = _serviceapi->GetTopologyMeta()->GetVertexType(src_typeIDVar).typename_;
              string tgtTy = _serviceapi->GetTopologyMeta()->GetVertexType(tgt_typeIDVar).typename_;
              topology4::EdgeTypeMeta& eMeta = _serviceapi->GetTopologyMeta()->GetEdgeType(e_typeIDVar);
              string eName = eMeta.typename_;
              bool _GQUERY_TYPE_CHECK_VALID = true;
              if (UNLIKELY(!eMeta.edgepairs_.empty())) {
                std::pair<string, string> p = std::make_pair(srcTy, tgtTy);
                if (std::find(eMeta.edgepairs_.begin(), eMeta.edgepairs_.end(), p) == eMeta.edgepairs_.end()) {
                  tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, tgt_typeIDVar);
                  __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                  _GQUERY_TYPE_CHECK_VALID = false;
                }
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.fromtypename_) != src_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, -1);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.totypename_) != tgt_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, -1, tgt_typeIDVar);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              }
              if (_GQUERY_TYPE_CHECK_VALID) {
                auto graphupdate_ = context.GetGraphUpdate();
                uint32_t esubtypeid = 0;
                std::stringstream multiedgeIdStream;
                bool isInsKey = false;
                if (eMeta.discriminator_count_ > 0) {
                  gutil::EnableDiscriminator(esubtypeid);
                  isInsKey = true;
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e2_576037_l1].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                std::string multiedgeId = multiedgeIdStream.str();
                {
                  gutil::GLock lock(graphupdate_->GetMutex());
                  {
                    const ListAccum<int64_t > & attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e2_576037_l1, attrValue.get_data(), topology4::DeltaAttributeOperator_Add);
                  }
                }
                graphupdate_->CheckForFlush();
              }
            }
            
            // insert into e6 ( from, to, discriminator ( ii, iv ), l1 ) values ( s, t, 1, 1, 1 )
            {
              size_t e_typeIDVar = _schema_ETY_e6;
              uint32_t src_typeIDVar = graphAPI->GetVertexType(lvar_s);
              uint32_t tgt_typeIDVar = graphAPI->GetVertexType(tgt);
              topology4::DeltaVertexId srcdeltaid_ (src_typeIDVar, lvar_s);
              topology4::DeltaVertexId tgtdeltaid_ (tgt_typeIDVar, tgt);
              if (srcdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e6") + "' with an invalid FROM vertex 's'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              if (tgtdeltaid_.isInvalid()) {
                string msg("Runtime Error: Inserting an edge of type '" + std::string("e6") + "' with an invalid TO vertex 'tgt'.");
                throw gutil::GsqlException(msg, gutil::error_t::E_EDGE_FROM_TO);
              }
              string srcTy = _serviceapi->GetTopologyMeta()->GetVertexType(src_typeIDVar).typename_;
              string tgtTy = _serviceapi->GetTopologyMeta()->GetVertexType(tgt_typeIDVar).typename_;
              topology4::EdgeTypeMeta& eMeta = _serviceapi->GetTopologyMeta()->GetEdgeType(e_typeIDVar);
              string eName = eMeta.typename_;
              bool _GQUERY_TYPE_CHECK_VALID = true;
              if (UNLIKELY(!eMeta.edgepairs_.empty())) {
                std::pair<string, string> p = std::make_pair(srcTy, tgtTy);
                if (std::find(eMeta.edgepairs_.begin(), eMeta.edgepairs_.end(), p) == eMeta.edgepairs_.end()) {
                  tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, tgt_typeIDVar);
                  __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                  _GQUERY_TYPE_CHECK_VALID = false;
                }
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.fromtypename_) != src_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, src_typeIDVar, -1);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              } else if (UNLIKELY(GSQL_UTIL::GetVertexTypeId(_serviceapi->GetTopologyMeta(), _request, eMeta.totypename_) != tgt_typeIDVar)) {
                tuple_eid_svid_tvid key(e_typeIDVar, -1, tgt_typeIDVar);
                __GQUERY_UNIVERSAL_INSERTION_MAP.Value() += std::make_pair(key, 1);
                _GQUERY_TYPE_CHECK_VALID = false;
              }
              if (_GQUERY_TYPE_CHECK_VALID) {
                auto graphupdate_ = context.GetGraphUpdate();
                uint32_t esubtypeid = 0;
                std::stringstream multiedgeIdStream;
                bool isInsKey = false;
                if (eMeta.discriminator_count_ > 0) {
                  gutil::EnableDiscriminator(esubtypeid);
                  isInsKey = true;
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_ii].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_iv].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                if (isInsKey && eMeta.attributes_.attributelist_[_schema_EATT_e6_576037_l1].is_discriminator_) {
                  MergeAttrToMultiedgeId(multiedgeIdStream, 1l);
                }
                std::string multiedgeId = multiedgeIdStream.str();
                {
                  gutil::GLock lock(graphupdate_->GetMutex());
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_ii, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const int64_t& attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_iv, attrValue, topology4::DeltaAttributeOperator_Overwrite);
                  }
                  {
                    const ListAccum<int64_t > & attrValue = 1l;
                    graphupdate_->GetAU(srcdeltaid_, tgtdeltaid_, e_typeIDVar, esubtypeid, multiedgeId, isInsKey)->Set(_schema_EATT_e6_576037_l1, attrValue.get_data(), topology4::DeltaAttributeOperator_Add);
                  }
                }
                graphupdate_->CheckForFlush();
              }
            }
          }
          if (UNLIKELY((++__GQUERY__iter_17 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
            HF_set_error(_request, msg, true);
            return;
          }
        }
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_8(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_2__576037 l_val_2(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_2__576037>());
        lptr.add(l_val_2);
        V_VALUE l_val(lptr);
        
        // x_12.@srcIdOnlyPropagAcc_5.clear ( )
        if (delta.__GQUERY__isSrc__576037) {
          l_val.srcIdOnlyPropagAcc_5_1.clear();
        }
        context.GlobalVariableAdd(1, __GQUERY_UNIVERSAL_INSERTION_MAP.Value());
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_2, 0);
      }
      
      ///gpr driver
      void run_impl () {
        if (!manager.Start("q1")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        if (!isQueryCalled) __GQUERY__graphupdate = _serviceapi->CreateGraphUpdates(&_request);
        // create global variable
        
        gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_;
        
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__4_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__4_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__4_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__2_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__2_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__2_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__1_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__1_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__1_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_W_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_W_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_W_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__5_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__5_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__5_vector_;
        
        {
          /*
          VS__1 = select x_1 from v1 : s - ( e3 > : x ) - v2 : x_1 accum x_1.@srcIdOnlyPropagAcc_1 += s;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_VS__1_size = __GQUERY_GV_Global_Variable__vset_VS__1_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_2_reduce";
          if (!manager.RunCMD(
            action_2_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_2_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_VS__1_SIZE__.Value() -= __vset_VS__1_size;
          __GQUERY_GV_Global_Variable__vset_VS__1_hasOrder_.Value() = false;
          
        }
        {
          /*
          VS__2 = select x_4 from VS__1 : x_1 - ( e4 > : x_3 ) - _ : x_4 accum x_4.@srcIdOnlyPropagAcc_2 += x_1.@srcIdOnlyPropagAcc_1 post-accum x_1.@srcIdOnlyPropagAcc_1.clear ( );
          */
          int64_t __vset_VS__2_size = __GQUERY_GV_Global_Variable__vset_VS__2_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_3_map";
          if (!manager.RunCMD(
            action_3_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_3_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_3_reduce";
          if (!manager.RunCMD(
            action_3_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_3_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_VS__2_SIZE__.Value() -= __vset_VS__2_size;
          __GQUERY_GV_Global_Variable__vset_VS__2_hasOrder_.Value() = false;
          
        }
        {
          /*
          W = select t from VS__2 : x_4 - ( e5 > : x_3 ) - v_list : t accum foreach s in x_4.@srcIdOnlyPropagAcc_2 do case when s.type == "v1" then insert into v_list values ( "2", 1 ), insert into e2 values ( s, t, 1, 1 ), insert into e6 values ( s, t, 1, 1, 1, 1 ) end end post-accum x_4.@srcIdOnlyPropagAcc_2.clear ( );
          */
          int64_t __vset_W_size = __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_4_map";
          if (!manager.RunCMD(
            action_4_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_W_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_4_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_4_reduce";
          if (!manager.RunCMD(
            action_4_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_W_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_4_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value() -= __vset_W_size;
          __GQUERY_GV_Global_Variable__vset_W_hasOrder_.Value() = false;
          
        }
        {
          /*
          VS__4 = select x_9 from v1 : s - ( e3 > : x ) - v2 : x_9 accum x_9.@srcIdOnlyPropagAcc_4 += s;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_5_";
          if (!manager.RunCMD(
            action_5_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_5_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_VS__4_size = __GQUERY_GV_Global_Variable__vset_VS__4_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_6_map";
          if (!manager.RunCMD(
            action_6_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_6_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_6_reduce";
          if (!manager.RunCMD(
            action_6_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_6_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_VS__4_SIZE__.Value() -= __vset_VS__4_size;
          __GQUERY_GV_Global_Variable__vset_VS__4_hasOrder_.Value() = false;
          
        }
        {
          /*
          VS__5 = select x_12 from VS__4 : x_9 - ( e4 > : x_11 ) - _ : x_12 accum x_12.@srcIdOnlyPropagAcc_5 += x_9.@srcIdOnlyPropagAcc_4 post-accum x_9.@srcIdOnlyPropagAcc_4.clear ( );
          */
          int64_t __vset_VS__5_size = __GQUERY_GV_Global_Variable__vset_VS__5_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_7_map";
          if (!manager.RunCMD(
            action_7_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_7_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_7_reduce";
          if (!manager.RunCMD(
            action_7_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_7_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_VS__5_SIZE__.Value() -= __vset_VS__5_size;
          __GQUERY_GV_Global_Variable__vset_VS__5_hasOrder_.Value() = false;
          
        }
        {
          /*
          W = select t from VS__5 : x_12 - ( e5 > : x_11 ) - v_list : t accum foreach s in x_12.@srcIdOnlyPropagAcc_5 do case when s.type == "v1" then insert into v_list1 ( primary_id, lucky_nums ) values ( "2", 1 ), insert into e2 ( from, to, l1 ) values ( s, t, 1 ), insert into e6 ( from, to, discriminator ( ii, iv ), l1 ) values ( s, t, 1, 1, 1 ) end end post-accum x_12.@srcIdOnlyPropagAcc_5.clear ( );
          */
          int64_t __vset_W_size = __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_8_map";
          if (!manager.RunCMD(
            action_8_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_W_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_8_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_8_reduce";
          if (!manager.RunCMD(
            action_8_reduce, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_W_SIZE__, &__GQUERY_UNIVERSAL_INSERTION_MAP} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_8_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value() -= __vset_W_size;
          __GQUERY_GV_Global_Variable__vset_W_hasOrder_.Value() = false;
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          {
            // default/implicit commit action
            if (!isQueryCalled) _request.ResetQueryState();
            _serviceapi->QueryResponse_VIdtoUId(&_request);
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "implicit commit";
            if (!manager.RunCMD(
              action_implicit_commit, 
              {}, //input gvs
              {} //output gvs
              )) {
                GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_implicit_commit RunCMD failed.";
              return;
            }
            // set ignored insertion message before commit
            this->setIgnoredInsertionCountMsg(__GQUERY_UNIVERSAL_INSERTION_MAP.Value());
            if (!_request.error_) {
              if (!isQueryCalled) {
                _request.ResetQueryState();
                if (!__GQUERY__graphupdate->Commit()) {
                  throw gutil::GsqlException("Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
                }
              } else {
                __GQUERY__graphupdate->FlushAttribute();
              }
            }
          }
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          gpr::GPR_Container* __GQUERY__value_container1 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_1__576037>();
          gutil::ScopedCleanUp sc_val1(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container1, &_request));
          gpr::GPR_Container* __GQUERY__value_container2 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_2__576037>();
          gutil::ScopedCleanUp sc_val2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container2, &_request));
          gpr::GPR_Container* __GQUERY__value_container3 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_3__576037>();
          gutil::ScopedCleanUp sc_val3(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container3, &_request));
          gpr::GPR_Container* __GQUERY__value_container4 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_4__576037>();
          gutil::ScopedCleanUp sc_val4(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container4, &_request));
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_VS__4 = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_VS__2 = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_VS__1 = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_W = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_VS__5 = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_;
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__4_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__4_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__4_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__2_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__2_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__2_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__1_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__1_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__1_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_W_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_W_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_W_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_VS__5_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_VS__5_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_VS__5_vector_;
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<Delta_3>();
          gutil::ScopedCleanUp sc_msg3(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request));
          gpr::GPR_Container* __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg4(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request));
          gpr::GPR_Container* __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
          gutil::ScopedCleanUp sc_msg6(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request));
          gpr::GPR_Container* __GQUERY__delta_container7 = gpr->CreateMsgContainer_RawPtr<Delta_7>();
          gutil::ScopedCleanUp sc_msg7(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container7, &_request));
          gpr::GPR_Container* __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg8(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request));
          if (!isQueryCalled) __GQUERY__graphupdate = _serviceapi->CreateGraphUpdates(&_request);
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_v1, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__1_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e3);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_2, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container2},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container2}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_VS__1_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_2_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__1_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_VS__1->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_2, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container2,__GQUERY__value_container4},//input container(old v_value and delta)
                  {__GQUERY__value_container4},//output container(new v_value)
                  {__GQUERY__vSet_VS__1.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_VS__1_SIZE__.Value() = __GQUERY__vSet_VS__1->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output.Value() = __GQUERY__vSet_VS__1->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__1_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_3_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_3_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__2_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<Delta_3>();
                sc_msg3 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e4);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_3, this),//action function
                  __GQUERY__vSet_VS__1.get(),//input bitset
                  {__GQUERY__value_container4},//input container(v_value)
                  {__GQUERY__delta_container3},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container3}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_3_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_VS__2_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_3_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__2_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_VS__2->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_3, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container3,__GQUERY__value_container4,__GQUERY__value_container3},//input container(old v_value and delta)
                  {__GQUERY__value_container4,__GQUERY__value_container3},//output container(new v_value)
                  {__GQUERY__vSet_VS__2.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_VS__2_SIZE__.Value() = __GQUERY__vSet_VS__2->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output.Value() = __GQUERY__vSet_VS__2->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__2_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_4_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_4_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_W_SIZE__) __GQUERY_GV_Global_Variable__vset_W_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg4 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e5);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_4, this),//action function
                  __GQUERY__vSet_VS__2.get(),//input bitset
                  {__GQUERY__value_container3},//input container(v_value)
                  {__GQUERY__delta_container4},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container4}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_4_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP});
                }
                __GQUERY_GV_Global_Variable__vset_W_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_4_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_W_SIZE__) __GQUERY_GV_Global_Variable__vset_W_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_W->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_4, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container4,__GQUERY__value_container3},//input container(old v_value and delta)
                  {__GQUERY__value_container3},//output container(new v_value)
                  {__GQUERY__vSet_W.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value() = __GQUERY__vSet_W->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_W_SIZE___output.Value() = __GQUERY__vSet_W->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP});
                }
              }
              else if (worker->GetAction() == action_5_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_5_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_v1, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_5_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_6_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_6_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__4_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
                sc_msg6 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e3);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_6, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container6},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container6}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_6_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_VS__4_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_6_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__4_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_VS__4->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_6, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container6,__GQUERY__value_container1},//input container(old v_value and delta)
                  {__GQUERY__value_container1},//output container(new v_value)
                  {__GQUERY__vSet_VS__4.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_VS__4_SIZE__.Value() = __GQUERY__vSet_VS__4->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output.Value() = __GQUERY__vSet_VS__4->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__4_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_7_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_7_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__5_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container7 = gpr->CreateMsgContainer_RawPtr<Delta_7>();
                sc_msg7 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container7, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e4);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_7, this),//action function
                  __GQUERY__vSet_VS__4.get(),//input bitset
                  {__GQUERY__value_container1},//input container(v_value)
                  {__GQUERY__delta_container7},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container7}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_7_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_VS__5_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_7_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_VS__5_SIZE__) __GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_VS__5->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_7, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container7,__GQUERY__value_container1,__GQUERY__value_container2},//input container(old v_value and delta)
                  {__GQUERY__value_container1,__GQUERY__value_container2},//output container(new v_value)
                  {__GQUERY__vSet_VS__5.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_VS__5_SIZE__.Value() = __GQUERY__vSet_VS__5->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output.Value() = __GQUERY__vSet_VS__5->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_VS__5_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_8_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_8_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_W_SIZE__) __GQUERY_GV_Global_Variable__vset_W_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container8 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg8 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container8, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_e5);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::complex_attribute::GPR_q1::EdgeMap_8, this),//action function
                  __GQUERY__vSet_VS__5.get(),//input bitset
                  {__GQUERY__value_container2},//input container(v_value)
                  {__GQUERY__delta_container8},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
                );
                edgeAction->AddInputObject(this);
                edgeAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container8}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_8_map";
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP});
                }
                __GQUERY_GV_Global_Variable__vset_W_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_8_reduce) {
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_W_SIZE__) __GQUERY_GV_Global_Variable__vset_W_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_W->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::complex_attribute::GPR_q1::Reduce_8, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container8,__GQUERY__value_container2},//input container(old v_value and delta)
                  {__GQUERY__value_container2},//output container(new v_value)
                  {__GQUERY__vSet_W.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP}//output gv
                );
                reduceAction->AddInputObject(this);
                reduceAction->SetGraphUpdate(__GQUERY__graphupdate.get());
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_W_SIZE__.Value() = __GQUERY__vSet_W->count();
                if (__GQUERY__graphupdate->Error()){
                  throw gutil::GsqlException("GraphUpdate hit internal error. Transaction aborted.", gutil::error_t::E_RUNTIME_EXCP);
                }
                //update vset size
                __GQUERY_GV_Global_Variable__vset_W_SIZE___output.Value() = __GQUERY__vSet_W->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_W_SIZE___output, &__GQUERY_UNIVERSAL_INSERTION_MAP});
                }
              }
              else if (worker->GetAction() == action_implicit_commit) {
                // set ignored insertion message before commit
                this->setIgnoredInsertionCountMsg(__GQUERY_UNIVERSAL_INSERTION_MAP.Value());
                if (!_request.error_) {
                  if (!isQueryCalled) {
                    _request.ResetQueryState();
                    if (!__GQUERY__graphupdate->Commit()) {
                      throw gutil::GsqlException("Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
                    }
                  } else {
                    __GQUERY__graphupdate->FlushAttribute();
                  }
                }
                worker->Response({});
              }
              else if (worker->GetAction() == action_explicit_commit) {
                // set ignored insertion message before commit
                this->setIgnoredInsertionCountMsg(__GQUERY_UNIVERSAL_INSERTION_MAP.Value());
                if (!_request.error_ && !_request.IsAbort()) {
                  _request.ResetQueryState();
                  if (!__GQUERY__graphupdate->Commit()) {
                    throw gutil::GsqlException(" Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
                  }
                }
                worker->Response({});
              }
              else if (worker->GetAction() == action_abort) {
                // set ignored insertion message before commit
                this->setIgnoredInsertionCountMsg(__GQUERY_UNIVERSAL_INSERTION_MAP.Value());
                if (!_request.error_ && !_request.IsAbort()) {
                  _request.ResetQueryState();
                  if (!__GQUERY__graphupdate->Rollback()) {
                    throw gutil::GsqlException(" Abort may not succeed.", gutil::error_t::E_OTHER_EXCP);
                  }
                }
                worker->Response({});
              }
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      // tuple detail: edge id, source vertex id, target vertex id
      typedef std::tuple<size_t,uint32_t,uint32_t> tuple_eid_svid_tvid;
      ///method for count of ignored universal insert statement
      void setIgnoredInsertionCountMsg(const MapAccum<tuple_eid_svid_tvid, uint32_t> & data) {
        if (isQueryCalled) {
          if (__GQUERY_universal_insertion_data != nullptr){
            *__GQUERY_universal_insertion_data += data;
          }
          return;
        }
        if (data.size() == 0) return;
        std::stringstream msgss;
        auto topologyMeta = _serviceapi->GetTopologyMeta();
        char _buffer[1024];
        for (auto & it : data) {
          auto& _tuple = it.first;
          string eName = topologyMeta->GetEdgeType(std::get<0>(_tuple)).typename_;
          string srcTy, tgtTy;
          if (std::get<1>(_tuple) != -1) {
            srcTy = topologyMeta->GetVertexType(std::get<1>(_tuple)).typename_;
          }
          if (std::get<2>(_tuple) != -1) {
            tgtTy = topologyMeta->GetVertexType(std::get<2>(_tuple)).typename_;
          }
          if (std::get<1>(_tuple) == -1) {
            sprintf(_buffer, "WARNING: '%s' is not a valid TO vertex type for edge type '%s'.", tgtTy.c_str(), eName.c_str());
          } else if (std::get<2>(_tuple) == -1) {
            sprintf(_buffer, "WARNING: '%s' is not a valid FROM vertex type for edge type '%s'.", srcTy.c_str(), eName.c_str());
          } else {
            sprintf(_buffer, "WARNING: (%s, %s) is not a valid vertex pair for edge type '%s'.",srcTy.c_str(), tgtTy.c_str(), eName.c_str());
          }
          msgss << _buffer << " Ignored edge count: " << it.second << std::endl;
        }
        _request.SetErrorMessage(msgss.str());
      }
      void setUniversalInsertionData(MapAccum<tuple_eid_svid_tvid, uint32_t>* _data){
        this->__GQUERY_universal_insertion_data = _data;
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_v1 = 0;
      const int _schema_VTY_v2 = 1;
      const int _schema_VTY_v_list = 4;
      const int _schema_VTY_v_list1 = 5;
      const int _schema_ETY_e2 = 1;
      const int _schema_ETY_e3 = 2;
      const int _schema_ETY_e4 = 3;
      const int _schema_ETY_e5 = 4;
      const int _schema_ETY_e6 = 5;
      int _schema_VATT_v_list_576037_lucky_nums = -1;
      int _schema_VATT_v_list1_576037_lucky_nums = -1;
      int _schema_EATT_e2_576037_val = -1;
      int _schema_EATT_e2_576037_l1 = -1;
      int _schema_EATT_e6_576037_ii = -1;
      int _schema_EATT_e6_576037_iv = -1;
      int _schema_EATT_e6_576037_val = -1;
      int _schema_EATT_e6_576037_l1 = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_map = "action_3_map";
      const std::string action_3_reduce = "action_3_reduce";
      const std::string action_3_post = "action_3_post";
      const std::string action_4_map = "action_4_map";
      const std::string action_4_reduce = "action_4_reduce";
      const std::string action_4_post = "action_4_post";
      const std::string action_5_ = "action_5_";
      const std::string action_6_map = "action_6_map";
      const std::string action_6_reduce = "action_6_reduce";
      const std::string action_6_post = "action_6_post";
      const std::string action_7_map = "action_7_map";
      const std::string action_7_reduce = "action_7_reduce";
      const std::string action_7_post = "action_7_post";
      const std::string action_8_map = "action_8_map";
      const std::string action_8_reduce = "action_8_reduce";
      const std::string action_8_post = "action_8_post";
      const std::string action_implicit_commit = "action_implicit_commit";
      const std::string action_explicit_commit = "action_explicit_commit";
      const std::string action_abort = "action_abort";
      gpelib4::SumVariable<MapAccum<tuple_eid_svid_tvid, uint32_t>> __GQUERY_UNIVERSAL_INSERTION_MAP;
      topology4::GraphUpdatesPointer __GQUERY__graphupdate;
      MapAccum<tuple_eid_svid_tvid, uint32_t>* __GQUERY_universal_insertion_data = nullptr;
      /// worker manager
      gperun::WorkerManager manager;
      public:
        
        ///return vars
      };//end class GPR_q1
    bool call_q1(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::complex_attribute::GPR_q1 gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_q1_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::complex_attribute::GPR_q1 gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_q1(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::complex_attribute::GPR_q1 gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_q1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::complex_attribute::GPR_q1 gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_q1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::complex_attribute::GPR_q1 gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.setUniversalInsertionData(_mapaccum);
      gpr.run();
      
    }
  }//end namespace complex_attribute
}//end namespace UDIMPL
