/********** query start ***************
CREATE DISTRIBUTED QUERY nestedForLoopGroupByAccum(/* Parameters here */) {
  TYPEDEF TUPLE<STRING tag_name, INT popularity> tagInfo;
  HeapAccum<tagInfo>(5, popularity DESC, tag_name ASC) @@tagInfoTop;
  SumAccum<Int> @@gSum;
  SetAccum<String> @@gSet;
  GroupByAccum<INT year, INT month, GroupByAccum<STRING tagName, GroupByAccum<String tagname2, SumAccum<int> sum2> tagGroup2> tagGroup> @@monthlyGroup;
  FOREACH (y,m,tg) IN @@monthlyGroup DO
    @@gSum += y;
    FOREACH (tn, tg2) IN tg DO
      @@gSet += tn;
      FOREACH t IN tg2 DO
          @@tagInfoTop += tagInfo(t.tagname2, t.sum2);
      END;
    END;
  END;
  Print "hello";
}
********** query end ***************/
#include "empty_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace empty_graph {
    class GPR_nestedForLoopGroupByAccum {
      struct tagInfo {
        string tag_name;
        int64_t popularity;
        
        tagInfo() {
          tag_name = string();
          popularity = 0;
        }
        
        tagInfo(string tag_name_, int64_t popularity_) {
          tag_name = tag_name_;
          popularity = popularity_;
        }
        
        tagInfo(const std::tuple<string, int64_t>& __GQUERY__other__576037) {
          tag_name = std::get<0>(__GQUERY__other__576037);
          popularity = std::get<1>(__GQUERY__other__576037);
        }
        
        friend std::ostream& operator<<(std::ostream& os, const tagInfo& __GQUERY__other__576037) {
          os << "[";
          os << "tag_name " << __GQUERY__other__576037.tag_name << "|";
          os << "popularity " << __GQUERY__other__576037.popularity << "]";
          return os;
        }
        
        bool operator==(const tagInfo& __GQUERY__other__576037) const {
          return
            tag_name == __GQUERY__other__576037.tag_name &&
            popularity == __GQUERY__other__576037.popularity;
        }
        
        tagInfo& operator+=(const tagInfo& __GQUERY__other__576037) {
          tag_name += __GQUERY__other__576037.tag_name;
          popularity += __GQUERY__other__576037.popularity;
          return *this;
        }
        
        bool operator<(const tagInfo& __GQUERY__other__576037) const {
          if (tag_name < __GQUERY__other__576037.tag_name) return true;
          if (tag_name > __GQUERY__other__576037.tag_name) return false;
          if (popularity < __GQUERY__other__576037.popularity) return true;
          if (popularity > __GQUERY__other__576037.popularity) return false;
          return false;
        }
        
        bool operator>(const tagInfo& __GQUERY__other__576037) const {
          if (tag_name > __GQUERY__other__576037.tag_name) return true;
          if (tag_name < __GQUERY__other__576037.tag_name) return false;
          if (popularity > __GQUERY__other__576037.popularity) return true;
          if (popularity < __GQUERY__other__576037.popularity) return false;
          return false;
        }
        
        operator std::tuple<string, int64_t>() const {
          return std::make_tuple(tag_name,popularity);
        }
        
        friend std::size_t hash_value(const tagInfo& other) {
          std::size_t seed = 0;
          boost::hash_combine(seed, MurmurHash64A(other.tag_name.c_str(), other.tag_name.size(), 0));
          boost::hash_combine(seed, other.popularity);
          return seed;
        }
        void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            writer.WriteStartObject();
          writer.WriteNameString("tag_name");
          writer.WriteString(tag_name);
          writer.WriteNameString("popularity");
          writer.WriteInt(popularity);
          writer.WriteEndObject();
        }
        gutil::JSONWriter& json_write_name (
          gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            std::string ss = boost::lexical_cast<std::string>(*this);
          return writer.WriteNameString(ss.c_str());
        }
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(tag_name, popularity);
        }
      };
      template <typename TUPLE_t>
      class __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare {
        bool _reverse;
        public: 
          __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare() : _reverse(false) {}
        __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare(bool reverse) : _reverse(reverse) {}
        bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
          if (lhs.popularity > rhs.popularity) return _reverse^true;
          if (lhs.popularity < rhs.popularity) return _reverse^false;
          if (lhs.tag_name < rhs.tag_name) return _reverse^true;
          if (lhs.tag_name > rhs.tag_name) return _reverse^false;
          return false;
        }
      };
      
      template<typename tagname2_t, class sum2_t>
      struct GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 {
        MapAccum<string, SumAccum<int64_t > >  map;
        string baseItem;
        SumAccum<int64_t >  accumItem;
        
        GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2() { }
        
        GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2(string base_0, SumAccum<int64_t >  accum_0) {
          map = MapAccum<string, SumAccum<int64_t > >(base_0, accum_0);
        }
        
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(map, baseItem, accumItem);
        }
        friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2& m) {
          os << m.map;
          return os ;
        }
        bool operator==(GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) const {
          return map == __GQUERY__other__576037.map;
        }
        bool operator!=(GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) const {
          return map != __GQUERY__other__576037.map;
        }
        GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 operator+ (GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) {
          GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 newG;
          newG += *this;
          newG += __GQUERY__other__576037;
          return newG;
        }
        void operator= (GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) {
          map = __GQUERY__other__576037.map;
        }
        void operator+=(GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) {
          map += __GQUERY__other__576037.map;
        }
        void json_printer (gutil::JSONWriter& writer,
          gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
            writer.WriteStartArray();
          for (auto it = map.begin(); it != map.end(); it++) {
            writer.WriteStartObject();
            writer.WriteName("tagname2");
            json_printer_util(it->first, writer, _request, graphAPI, verbose);
            writer.WriteName("sum2");
            json_printer_util(it->second, writer, _request, graphAPI, verbose);
            writer.WriteEndObject();
          }
          writer.WriteEndArray();
        }
        SumAccum<int64_t >  get (string base_0) const {
          return map.get(base_0);
        }
        int size () const {
          return map.size();
        }
        bool containskey (string base_0) const {
          return map.containskey(base_0);
        }
        void clear () {
          map.clear();
        }
        void remove (string base_0) {
          map.remove(base_0);
        }
        void remove (GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2 const &__GQUERY__other__576037) {
          map.remove(__GQUERY__other__576037.map);
        }
        decltype(map.begin()) begin () {
          return map.begin();
        }
        decltype(map.end()) end () {
          return map.end();
        }
      };
      
      template<typename tagName_t, class tagGroup2_t>
      struct GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 {
        MapAccum<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  > >  map;
        string baseItem;
        GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  accumItem;
        
        GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2() { }
        
        GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2(string base_0, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  accum_0) {
          map = MapAccum<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  > >(base_0, accum_0);
        }
        
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(map, baseItem, accumItem);
        }
        friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2& m) {
          os << m.map;
          return os ;
        }
        bool operator==(GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) const {
          return map == __GQUERY__other__576037.map;
        }
        bool operator!=(GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) const {
          return map != __GQUERY__other__576037.map;
        }
        GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 operator+ (GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) {
          GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 newG;
          newG += *this;
          newG += __GQUERY__other__576037;
          return newG;
        }
        void operator= (GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) {
          map = __GQUERY__other__576037.map;
        }
        void operator+=(GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) {
          map += __GQUERY__other__576037.map;
        }
        void json_printer (gutil::JSONWriter& writer,
          gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
            writer.WriteStartArray();
          for (auto it = map.begin(); it != map.end(); it++) {
            writer.WriteStartObject();
            writer.WriteName("tagName");
            json_printer_util(it->first, writer, _request, graphAPI, verbose);
            writer.WriteName("tagGroup2");
            json_printer_util(it->second, writer, _request, graphAPI, verbose);
            writer.WriteEndObject();
          }
          writer.WriteEndArray();
        }
        GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  get (string base_0) const {
          return map.get(base_0);
        }
        int size () const {
          return map.size();
        }
        bool containskey (string base_0) const {
          return map.containskey(base_0);
        }
        void clear () {
          map.clear();
        }
        void remove (string base_0) {
          map.remove(base_0);
        }
        void remove (GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2 const &__GQUERY__other__576037) {
          map.remove(__GQUERY__other__576037.map);
        }
        decltype(map.begin()) begin () {
          return map.begin();
        }
        decltype(map.end()) end () {
          return map.end();
        }
      };
      
      template<typename year_t, typename month_t, class tagGroup_t>
      struct GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup {
        struct baseTuple {
          year_t year;
          month_t month;
          
          baseTuple() {
            year = year_t();
            month = month_t();
          }
          
          baseTuple(year_t year_, month_t month_) {
            year = year_;
            month = month_;
          }
          
          baseTuple(const std::tuple<year_t, month_t>& __GQUERY__other__576037) {
            year = std::get<0>(__GQUERY__other__576037);
            month = std::get<1>(__GQUERY__other__576037);
          }
          
          friend std::ostream& operator<<(std::ostream& os, const baseTuple& __GQUERY__other__576037) {
            os << "[";
            os << "year " << __GQUERY__other__576037.year << "|";
            os << "month " << __GQUERY__other__576037.month << "]";
            return os;
          }
          
          bool operator==(const baseTuple& __GQUERY__other__576037) const {
            return
              year == __GQUERY__other__576037.year &&
              month == __GQUERY__other__576037.month;
          }
          
          baseTuple& operator+=(const baseTuple& __GQUERY__other__576037) {
            year += __GQUERY__other__576037.year;
            month += __GQUERY__other__576037.month;
            return *this;
          }
          
          bool operator<(const baseTuple& __GQUERY__other__576037) const {
            if (year > __GQUERY__other__576037.year) return true;
            if (year < __GQUERY__other__576037.year) return false;
            if (month > __GQUERY__other__576037.month) return true;
            if (month < __GQUERY__other__576037.month) return false;
            return false;
          }
          
          bool operator>(const baseTuple& __GQUERY__other__576037) const {
            if (year < __GQUERY__other__576037.year) return true;
            if (year > __GQUERY__other__576037.year) return false;
            if (month < __GQUERY__other__576037.month) return true;
            if (month > __GQUERY__other__576037.month) return false;
            return false;
          }
          
          operator std::tuple<year_t, month_t>() const {
            return std::make_tuple(year,month);
          }
          
          friend std::size_t hash_value(const baseTuple& other) {
            std::size_t seed = 0;
            boost::hash_combine(seed, other.year);
            boost::hash_combine(seed, other.month);
            return seed;
          }
          void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
            gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
              writer.WriteStartObject();
            writer.WriteNameString("year");
            (year).json_printer(writer, _request, graphAPI, true);
            writer.WriteNameString("month");
            (month).json_printer(writer, _request, graphAPI, true);
            writer.WriteEndObject();
          }
          gutil::JSONWriter& json_write_name (
            gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
            gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
              std::string ss = boost::lexical_cast<std::string>(*this);
            return writer.WriteNameString(ss.c_str());
          }
          template <class ARCHIVE>
          void serialize(ARCHIVE& __GQUERY__ar_) {
            __GQUERY__ar_(year, month);
          }
        };
        MapAccum<baseTuple, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  > >  map;
        baseTuple baseItem;
        GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  accumItem;
        
        GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup() { }
        
        GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup(year_t base_0, month_t base_1, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  accum_0) {
          map = MapAccum<baseTuple, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  > >(baseTuple(base_0, base_1), accum_0);
        }
        
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(map, baseItem, accumItem);
        }
        friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup& m) {
          os << m.map;
          return os ;
        }
        bool operator==(GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) const {
          return map == __GQUERY__other__576037.map;
        }
        bool operator!=(GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) const {
          return map != __GQUERY__other__576037.map;
        }
        GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup operator+ (GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) {
          GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup newG;
          newG += *this;
          newG += __GQUERY__other__576037;
          return newG;
        }
        void operator= (GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) {
          map = __GQUERY__other__576037.map;
        }
        void operator+=(GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) {
          map += __GQUERY__other__576037.map;
        }
        void json_printer (gutil::JSONWriter& writer,
          gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
            writer.WriteStartArray();
          for (auto it = map.begin(); it != map.end(); it++) {
            writer.WriteStartObject();
            writer.WriteName("year");
            json_printer_util(it->first.year, writer, _request, graphAPI, verbose);
            writer.WriteName("month");
            json_printer_util(it->first.month, writer, _request, graphAPI, verbose);
            writer.WriteName("tagGroup");
            json_printer_util(it->second, writer, _request, graphAPI, verbose);
            writer.WriteEndObject();
          }
          writer.WriteEndArray();
        }
        GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  get (year_t base_0, month_t base_1) const {
          return map.get(baseTuple(base_0, base_1));
        }
        int size () const {
          return map.size();
        }
        bool containskey (year_t base_0, month_t base_1) const {
          return map.containskey(baseTuple(base_0, base_1));
        }
        void clear () {
          map.clear();
        }
        void remove (year_t base_0, month_t base_1) {
          map.remove(baseTuple(base_0, base_1));
        }
        void remove (GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup const &__GQUERY__other__576037) {
          map.remove(__GQUERY__other__576037.map);
        }
        decltype(map.begin()) begin () {
          return map.begin();
        }
        decltype(map.end()) end () {
          return map.end();
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_nestedForLoopGroupByAccum (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_nestedForLoopGroupByAccum (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_nestedForLoopGroupByAccum () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("nestedForLoopGroupByAccum")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<HeapAccum<tagInfo, __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare<tagInfo>  > > __GQUERY_GV_tagInfoTop_1_;
        gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_gSum_1_;
        gpelib4::SumVariable<SetAccum<string > > __GQUERY_GV_gSet_1_;
        gpelib4::SumVariable<GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup<int64_t, int64_t, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  > > __GQUERY_GV_monthlyGroup_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        {
          /*
          @@tagInfoTop_1 reinitiate
          */
          
          // @@tagInfoTop_1 reinitiate
          __GQUERY_GV_tagInfoTop_1_.Value().clear();
          __GQUERY_GV_tagInfoTop_1_.Value().resize(5l);
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          
        }
        {
          /*
          @@gSum_1 reinitiate
          */
          
          // @@gSum_1 reinitiate
          __GQUERY_GV_gSum_1_.Value().clear();
          
        }
        {
          /*
          @@gSet_1 reinitiate
          */
          
          // @@gSet_1 reinitiate
          __GQUERY_GV_gSet_1_.Value().clear();
          
        }
        {
          /*
          @@monthlyGroup_1 reinitiate
          */
          
          // @@monthlyGroup_1 reinitiate
          __GQUERY_GV_monthlyGroup_1_.Value().clear();
          
        }
        {
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_y_;
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_m_;
          gpelib4::StateVariable<GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  > > __GQUERY_GV_Global_Variable_tg_;
          uint64_t __GQUERY__iter_5 = 0;
          auto  __GQUERY__accum_5 = __HF_GPR_retrieveGV<GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup<int64_t, int64_t, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  >  >(__GQUERY_GV_monthlyGroup_1_, true, "monthlyGroup_1");
          for (auto it = ( __GQUERY__accum_5).begin(); it != ( __GQUERY__accum_5).end(); it++) {
            __GQUERY_GV_Global_Variable_y_.Value() = it->first.year;
            __GQUERY_GV_Global_Variable_m_.Value() = it->first.month;
            __GQUERY_GV_Global_Variable_tg_.Value() = it->second;
            {
              /*
              @@gSum += y;
              */
              
              // @@gSum += y;
              __GQUERY_GV_gSum_1_.Value() += __HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_y_, true, "y");
              
            }
            
            {
              gpelib4::StateVariable<string> __GQUERY_GV_Global_Variable_tn_;
              gpelib4::StateVariable<GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  > > __GQUERY_GV_Global_Variable_tg2_;
              uint64_t __GQUERY__iter_7 = 0;
              auto  __GQUERY__accum_7 = __HF_GPR_retrieveGV<GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  >(__GQUERY_GV_Global_Variable_tg_, true, "tg");
              for (auto it = ( __GQUERY__accum_7).begin(); it != ( __GQUERY__accum_7).end(); it++) {
                __GQUERY_GV_Global_Variable_tn_.Value() = it->first;
                __GQUERY_GV_Global_Variable_tg2_.Value() = it->second;
                {
                  /*
                  @@gSet += tn;
                  */
                  
                  // @@gSet += tn;
                  __GQUERY_GV_gSet_1_.Value() += __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_tn_, true, "tn");
                  
                }
                
                {
                  gpelib4::StateVariable<GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  > > __GQUERY_GV_Global_Variable_t_;
                  uint64_t __GQUERY__iter_9 = 0;
                  auto  __GQUERY__accum_9 = __HF_GPR_retrieveGV<GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >(__GQUERY_GV_Global_Variable_tg2_, true, "tg2");
                  for (auto it = ( __GQUERY__accum_9).begin(); it != ( __GQUERY__accum_9).end(); it++) {
                    __GQUERY_GV_Global_Variable_t_.Value().baseItem = it->first;
                    __GQUERY_GV_Global_Variable_t_.Value().accumItem = it->second;
                    {
                      /*
                      @@tagInfoTop += tagInfo ( t.tagname2, t.sum2 );
                      */
                      
                      // @@tagInfoTop += tagInfo ( t.tagname2, t.sum2 );
                      __GQUERY_GV_tagInfoTop_1_.Value() += tagInfo(__HF_GPR_retrieveGV<GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >(__GQUERY_GV_Global_Variable_t_, true, "t").baseItem, __HF_GPR_retrieveGV<GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >(__GQUERY_GV_Global_Variable_t_, true, "t").accumItem.data_);
                      
                    }
                    
                    if (UNLIKELY((++__GQUERY__iter_9 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
                      std::string msg("Aborted due to timeout or system memory in critical state.");
                      HF_set_error(_request, msg, true);
                      return;
                    }
                  }
                  
                }
                
                if (UNLIKELY((++__GQUERY__iter_7 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
                  std::string msg("Aborted due to timeout or system memory in critical state.");
                  HF_set_error(_request, msg, true);
                  return;
                }
              }
              
            }
            
            if (UNLIKELY((++__GQUERY__iter_5 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
              std::string msg("Aborted due to timeout or system memory in critical state.");
              HF_set_error(_request, msg, true);
              return;
            }
          }
          
        }
        {
          /*
          print "hello";
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("\"hello\"");
            writer.WriteString(string("hello"));
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          gpelib4::SumVariable<HeapAccum<tagInfo, __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare<tagInfo>  > > __GQUERY_GV_tagInfoTop_1_;
          gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_gSum_1_;
          gpelib4::SumVariable<SetAccum<string > > __GQUERY_GV_gSet_1_;
          gpelib4::SumVariable<GroupByAccum_2_year_month_1_GroupByAccum_GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2_tagGroup<int64_t, int64_t, GroupByAccum_1_string_tagName_1_GroupByAccum_GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2_tagGroup2<string, GroupByAccum_1_string_tagname2_1_SumAccum_int_sum2<string, SumAccum<int64_t >  >  >  > > __GQUERY_GV_monthlyGroup_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                //run action
                
                // Initialize the size of HeapAccum tagInfoTop_1
                __HF_GPR_retrieveGV<HeapAccum<tagInfo, __GSQL_tuple_tagInfo_string_int__sortKey_1_desc_0_asc_Compare<tagInfo>  >  >(__GQUERY_GV_tagInfoTop_1_, true, "tagInfoTop_1").resize(5l);
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|" << "worker finish action_1_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_ = "action_2_";
      public:
        
        ///return vars
      };//end class GPR_nestedForLoopGroupByAccum
    bool call_nestedForLoopGroupByAccum(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::empty_graph::GPR_nestedForLoopGroupByAccum gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_nestedForLoopGroupByAccum_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::empty_graph::GPR_nestedForLoopGroupByAccum gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_nestedForLoopGroupByAccum(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::empty_graph::GPR_nestedForLoopGroupByAccum gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "nestedForLoopGroupByAccum") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_nestedForLoopGroupByAccum(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::empty_graph::GPR_nestedForLoopGroupByAccum gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_nestedForLoopGroupByAccum(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::empty_graph::GPR_nestedForLoopGroupByAccum gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace empty_graph
}//end namespace UDIMPL
