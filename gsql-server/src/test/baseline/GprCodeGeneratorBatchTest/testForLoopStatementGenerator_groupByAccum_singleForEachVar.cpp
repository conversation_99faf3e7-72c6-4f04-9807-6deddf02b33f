/********** query start ***************
CREATE DISTRIBUTED QUERY ut() {
  GroupByAccum<INT a, MaxAccum<INT> ma> @@gba;
  @@gba += (1 -> 1);
  FOREACH g IN @@gba DO
    PRINT g.a;
  END;
}
********** query end ***************/
#include "empty_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace empty_graph {
    class GPR_ut {
      template<typename a_t, class ma_t>
      struct GroupByAccum_1_int_a_1_MaxAccum_int_ma {
        MapAccum<int64_t, MaxAccum<int64_t > >  map;
        int64_t baseItem;
        MaxAccum<int64_t >  accumItem;
        
        GroupByAccum_1_int_a_1_MaxAccum_int_ma() { }
        
        GroupByAccum_1_int_a_1_MaxAccum_int_ma(int64_t base_0, MaxAccum<int64_t >  accum_0) {
          map = MapAccum<int64_t, MaxAccum<int64_t > >(base_0, accum_0);
        }
        
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(map, baseItem, accumItem);
        }
        friend std::ostream& operator<<(std::ostream& os, const GroupByAccum_1_int_a_1_MaxAccum_int_ma& m) {
          os << m.map;
          return os ;
        }
        bool operator==(GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) const {
          return map == __GQUERY__other__576037.map;
        }
        bool operator!=(GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) const {
          return map != __GQUERY__other__576037.map;
        }
        GroupByAccum_1_int_a_1_MaxAccum_int_ma operator+ (GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) {
          GroupByAccum_1_int_a_1_MaxAccum_int_ma newG;
          newG += *this;
          newG += __GQUERY__other__576037;
          return newG;
        }
        void operator= (GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) {
          map = __GQUERY__other__576037.map;
        }
        void operator+=(GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) {
          map += __GQUERY__other__576037.map;
        }
        void json_printer (gutil::JSONWriter& writer,
          gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) {
            writer.WriteStartArray();
          for (auto it = map.begin(); it != map.end(); it++) {
            writer.WriteStartObject();
            writer.WriteName("a");
            json_printer_util(it->first, writer, _request, graphAPI, verbose);
            writer.WriteName("ma");
            json_printer_util(it->second, writer, _request, graphAPI, verbose);
            writer.WriteEndObject();
          }
          writer.WriteEndArray();
        }
        MaxAccum<int64_t >  get (int64_t base_0) const {
          return map.get(base_0);
        }
        int size () const {
          return map.size();
        }
        bool containskey (int64_t base_0) const {
          return map.containskey(base_0);
        }
        void clear () {
          map.clear();
        }
        void remove (int64_t base_0) {
          map.remove(base_0);
        }
        void remove (GroupByAccum_1_int_a_1_MaxAccum_int_ma const &__GQUERY__other__576037) {
          map.remove(__GQUERY__other__576037.map);
        }
        decltype(map.begin()) begin () {
          return map.begin();
        }
        decltype(map.end()) end () {
          return map.end();
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_ut (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_ut (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_ut () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("ut")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  > > __GQUERY_GV_gba_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        {
          /*
          @@gba_1 reinitiate
          */
          
          // @@gba_1 reinitiate
          __GQUERY_GV_gba_1_.Value().clear();
          
        }
        {
          /*
          @@gba += ( 1 -> 1 );
          */
          
          // @@gba += ( 1 -> 1 );
          __GQUERY_GV_gba_1_.Value() += GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  > (1l,1l);
          
        }
        {
          gpelib4::StateVariable<GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  > > __GQUERY_GV_Global_Variable_g_;
          uint64_t __GQUERY__iter_3 = 0;
          auto  __GQUERY__accum_3 = __HF_GPR_retrieveGV<GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  >  >(__GQUERY_GV_gba_1_, true, "gba_1");
          for (auto it = ( __GQUERY__accum_3).begin(); it != ( __GQUERY__accum_3).end(); it++) {
            __GQUERY_GV_Global_Variable_g_.Value().baseItem = it->first;
            __GQUERY_GV_Global_Variable_g_.Value().accumItem = it->second;
            {
              /*
              print g.a;
              */
              __GQUERY__local_writer->WriteStartObject();
              {
                gutil::JSONWriter& writer = *__GQUERY__local_writer;
                std::cout << "HERE print global" << std::endl;
                writer.WriteNameString("g.a");
                writer.WriteInt(__HF_GPR_retrieveGV<GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  >  >(__GQUERY_GV_Global_Variable_g_, true, "g").baseItem);
                
              }
              __GQUERY__local_writer->WriteEndObject();
              
            }
            
            if (UNLIKELY((++__GQUERY__iter_3 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
              std::string msg("Aborted due to timeout or system memory in critical state.");
              HF_set_error(_request, msg, true);
              return;
            }
          }
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          gpelib4::SumVariable<GroupByAccum_1_int_a_1_MaxAccum_int_ma<int64_t, MaxAccum<int64_t >  > > __GQUERY_GV_gba_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "ut") << _request.requestid_ << "|" << "worker get new action";
            try {
              
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      public:
        
        ///return vars
      };//end class GPR_ut
    bool call_ut(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::empty_graph::GPR_ut gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_ut_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::empty_graph::GPR_ut gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_ut(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::empty_graph::GPR_ut gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "ut") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_ut(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::empty_graph::GPR_ut gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_ut(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::empty_graph::GPR_ut gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace empty_graph
}//end namespace UDIMPL
