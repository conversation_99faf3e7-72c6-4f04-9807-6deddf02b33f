/********** query start ***************
CREATE DISTRIBUTED QUERY q1() {
SumAccum<INT> @testSumAccum;
t = select v from v1:v where v.val >20 accum v.@testSumAccum+=1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum;
t = select v from v1:v where v.val <20 accum v.@testSumAccum+=1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum;
t = select v from v1:v where v.val ==20 accum v.@testSumAccum+=1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum limit 20;
}
********** query end ***************/
#include "testTup.hpp"

#include "g1-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace g1 {
    class GPR_q1 {
      struct orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc {
        VERTEX vid;
        int64_t key_0;
        string key_1;
        string key_2;
        DATETIME key_3;
        int64_t key_4;
        
        orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc() {
          key_0 = 0;
          key_1 = string();
          key_2 = string();
          key_3 = DATETIME(0);
          key_4 = 0;
        }
        
        orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc(VERTEX vid_, int64_t key_0_, string key_1_, string key_2_, DATETIME key_3_, int64_t key_4_) {
          vid = vid_;
          key_0 = key_0_;
          key_1 = key_1_;
          key_2 = key_2_;
          key_3 = key_3_;
          key_4 = key_4_;
        }
        
        orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc(const std::tuple<VERTEX, int64_t, string, string, DATETIME, int64_t>& __GQUERY__other__576037) {
          vid = std::get<0>(__GQUERY__other__576037);
          key_0 = std::get<1>(__GQUERY__other__576037);
          key_1 = std::get<2>(__GQUERY__other__576037);
          key_2 = std::get<3>(__GQUERY__other__576037);
          key_3 = std::get<4>(__GQUERY__other__576037);
          key_4 = std::get<5>(__GQUERY__other__576037);
        }
        
        friend std::ostream& operator<<(std::ostream& os, const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& __GQUERY__other__576037) {
          os << "[";
          os << "vid " << __GQUERY__other__576037.vid << "|";
          os << "key_0 " << __GQUERY__other__576037.key_0 << "|";
          os << "key_1 " << __GQUERY__other__576037.key_1 << "|";
          os << "key_2 " << __GQUERY__other__576037.key_2 << "|";
          os << "key_3 " << __GQUERY__other__576037.key_3 << "|";
          os << "key_4 " << __GQUERY__other__576037.key_4 << "]";
          return os;
        }
        
        bool operator==(const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& __GQUERY__other__576037) const {
          return
            vid == __GQUERY__other__576037.vid &&
            key_0 == __GQUERY__other__576037.key_0 &&
            key_1 == __GQUERY__other__576037.key_1 &&
            key_2 == __GQUERY__other__576037.key_2 &&
            key_3 == __GQUERY__other__576037.key_3 &&
            key_4 == __GQUERY__other__576037.key_4;
        }
        
        orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& operator+=(const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& __GQUERY__other__576037) {
          vid += __GQUERY__other__576037.vid;
          key_0 += __GQUERY__other__576037.key_0;
          key_1 += __GQUERY__other__576037.key_1;
          key_2 += __GQUERY__other__576037.key_2;
          key_3 += __GQUERY__other__576037.key_3;
          key_4 += __GQUERY__other__576037.key_4;
          return *this;
        }
        
        bool operator<(const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& __GQUERY__other__576037) const {
          if (vid < __GQUERY__other__576037.vid) return true;
          if (vid > __GQUERY__other__576037.vid) return false;
          if (key_0 < __GQUERY__other__576037.key_0) return true;
          if (key_0 > __GQUERY__other__576037.key_0) return false;
          if (key_1 < __GQUERY__other__576037.key_1) return true;
          if (key_1 > __GQUERY__other__576037.key_1) return false;
          if (key_2 < __GQUERY__other__576037.key_2) return true;
          if (key_2 > __GQUERY__other__576037.key_2) return false;
          if (key_3 < __GQUERY__other__576037.key_3) return true;
          if (key_3 > __GQUERY__other__576037.key_3) return false;
          if (key_4 < __GQUERY__other__576037.key_4) return true;
          if (key_4 > __GQUERY__other__576037.key_4) return false;
          return false;
        }
        
        bool operator>(const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& __GQUERY__other__576037) const {
          if (vid > __GQUERY__other__576037.vid) return true;
          if (vid < __GQUERY__other__576037.vid) return false;
          if (key_0 > __GQUERY__other__576037.key_0) return true;
          if (key_0 < __GQUERY__other__576037.key_0) return false;
          if (key_1 > __GQUERY__other__576037.key_1) return true;
          if (key_1 < __GQUERY__other__576037.key_1) return false;
          if (key_2 > __GQUERY__other__576037.key_2) return true;
          if (key_2 < __GQUERY__other__576037.key_2) return false;
          if (key_3 > __GQUERY__other__576037.key_3) return true;
          if (key_3 < __GQUERY__other__576037.key_3) return false;
          if (key_4 > __GQUERY__other__576037.key_4) return true;
          if (key_4 < __GQUERY__other__576037.key_4) return false;
          return false;
        }
        
        operator std::tuple<VERTEX, int64_t, string, string, DATETIME, int64_t>() const {
          return std::make_tuple(vid,key_0,key_1,key_2,key_3,key_4);
        }
        
        friend std::size_t hash_value(const orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc& other) {
          std::size_t seed = 0;
          boost::hash_combine(seed, other.vid);
          boost::hash_combine(seed, other.key_0);
          boost::hash_combine(seed, MurmurHash64A(other.key_1.c_str(), other.key_1.size(), 0));
          boost::hash_combine(seed, MurmurHash64A(other.key_2.c_str(), other.key_2.size(), 0));
          boost::hash_combine(seed, other.key_3);
          boost::hash_combine(seed, other.key_4);
          return seed;
        }
        void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            writer.WriteStartObject();
          writer.WriteNameString("vid");
          (vid).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("key_0");
          writer.WriteInt(key_0);
          writer.WriteNameString("key_1");
          writer.WriteString(key_1);
          writer.WriteNameString("key_2");
          writer.WriteString(key_2);
          writer.WriteNameString("key_3");
          writer.WriteString((std::string)DATETIME(key_3));
          writer.WriteNameString("key_4");
          writer.WriteInt(key_4);
          writer.WriteEndObject();
        }
        gutil::JSONWriter& json_write_name (
          gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            std::string ss = boost::lexical_cast<std::string>(*this);
          return writer.WriteNameString(ss.c_str());
        }
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(vid, key_0, key_1, key_2, key_3, key_4);
        }
      };
      template <typename TUPLE_t>
      class orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare {
        bool _reverse;
        public: 
          orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare() : _reverse(false) {}
        orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare(bool reverse) : _reverse(reverse) {}
        bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
          if (lhs.key_0 < rhs.key_0) return _reverse^true;
          if (lhs.key_0 > rhs.key_0) return _reverse^false;
          if (lhs.key_1 < rhs.key_1) return _reverse^true;
          if (lhs.key_1 > rhs.key_1) return _reverse^false;
          if (lhs.key_2 < rhs.key_2) return _reverse^true;
          if (lhs.key_2 > rhs.key_2) return _reverse^false;
          if (lhs.key_3 < rhs.key_3) return _reverse^true;
          if (lhs.key_3 > rhs.key_3) return _reverse^false;
          if (lhs.key_4 < rhs.key_4) return _reverse^true;
          if (lhs.key_4 > rhs.key_4) return _reverse^false;
          return false;
        }
      };
      
      struct Delta_2 {
        // accumulators
        SumAccum<int64_t >  testSumAccum_1;
        bool __GQUERY__hasChanged___576037testSumAccum_1;
        bool __GQUERY__set___576037testSumAccum_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_2 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037testSumAccum_1 = false;
          __GQUERY__set___576037testSumAccum_1 = false;
        }
        // message combinator
        Delta_2& operator+= (const Delta_2& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037testSumAccum_1) {
            testSumAccum_1 += __GQUERY__other_.testSumAccum_1;
            __GQUERY__hasChanged___576037testSumAccum_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037testSumAccum_1) {
            testSumAccum_1 = __GQUERY__other_.testSumAccum_1;
            __GQUERY__set___576037testSumAccum_1 = true;
          }
          return *this;
        }
      };
      struct Delta_4 {
        // accumulators
        SumAccum<int64_t >  testSumAccum_1;
        bool __GQUERY__hasChanged___576037testSumAccum_1;
        bool __GQUERY__set___576037testSumAccum_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_4 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037testSumAccum_1 = false;
          __GQUERY__set___576037testSumAccum_1 = false;
        }
        // message combinator
        Delta_4& operator+= (const Delta_4& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037testSumAccum_1) {
            testSumAccum_1 += __GQUERY__other_.testSumAccum_1;
            __GQUERY__hasChanged___576037testSumAccum_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037testSumAccum_1) {
            testSumAccum_1 = __GQUERY__other_.testSumAccum_1;
            __GQUERY__set___576037testSumAccum_1 = true;
          }
          return *this;
        }
      };
      struct Delta_6 {
        // accumulators
        SumAccum<int64_t >  testSumAccum_1;
        bool __GQUERY__hasChanged___576037testSumAccum_1;
        bool __GQUERY__set___576037testSumAccum_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_6 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037testSumAccum_1 = false;
          __GQUERY__set___576037testSumAccum_1 = false;
        }
        // message combinator
        Delta_6& operator+= (const Delta_6& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037testSumAccum_1) {
            testSumAccum_1 += __GQUERY__other_.testSumAccum_1;
            __GQUERY__hasChanged___576037testSumAccum_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037testSumAccum_1) {
            testSumAccum_1 = __GQUERY__other_.testSumAccum_1;
            __GQUERY__set___576037testSumAccum_1 = true;
          }
          return *this;
        }
      };
      struct __GQUERY__VertexVal_0__576037 {
        // accumulators:
        SumAccum<int64_t >  testSumAccum_1;
        // default constructor
        __GQUERY__VertexVal_0__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_0__576037 (const __GQUERY__VertexVal_0__576037& __GQUERY__other_) {
          testSumAccum_1 = __GQUERY__other_.testSumAccum_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(testSumAccum_1);
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
        SumAccum<int64_t > * testSumAccum_1;
        void add (const __GQUERY__VertexVal_0__576037& __GQUERY__other_) {
          testSumAccum_1 = const_cast<SumAccum<int64_t > * > (&__GQUERY__other_.testSumAccum_1);
        }
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        SumAccum<int64_t > * testSumAccum_1Ptr;
        SumAccum<int64_t > & testSumAccum_1 = * testSumAccum_1Ptr;
        // default constructor
        __GQUERY__VertexVal__576037 () {};
        // constructor
        __GQUERY__VertexVal__576037 (__GQUERY__VertexVal_ptr__576037& __GQUERY__other_) : testSumAccum_1(*__GQUERY__other_.testSumAccum_1)
          {}
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_q1 (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_v1_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_v1_576037_val = VTY_v1_attrMeta.GetAttributePosition("val", true);
        _schema_VATT_v1_576037_name = VTY_v1_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_v1_576037_label = VTY_v1_attrMeta.GetAttributePosition("label", true);
        _schema_VATT_v1_576037_dt = VTY_v1_attrMeta.GetAttributePosition("dt", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_q1 (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_v1_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_v1_576037_val = VTY_v1_attrMeta.GetAttributePosition("val", true);
        _schema_VATT_v1_576037_name = VTY_v1_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_v1_576037_label = VTY_v1_attrMeta.GetAttributePosition("label", true);
        _schema_VATT_v1_576037_dt = VTY_v1_attrMeta.GetAttributePosition("dt", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_q1 () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void VertexMap_2(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_2 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        int64_t src_val_int64_t = 0;
        bool src_val_int64_t_flag = false;
        string src_name_string = string();
        bool src_name_string_flag = false;
        string src_label_string = string();
        bool src_label_string_flag = false;
        DATETIME src_dt_DATETIME = DATETIME(0);
        bool src_dt_DATETIME_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_v1) {
          if (srcVertex.GetAttr().IsValid()) {
            src_val_int64_t = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_val, 0);
            src_val_int64_t_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_name_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_name);
            src_name_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_label_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_label);
            src_label_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_dt_DATETIME = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_dt, 0);
            src_dt_DATETIME_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_val_int64_t_flag && (src_val_int64_t > 20l)))) return;
        // prepare message
        Delta_2 src_delta = Delta_2();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // v.@testSumAccum += 1
        src_delta.testSumAccum_1 += 1l;
        src_delta.__GQUERY__hasChanged___576037testSumAccum_1 = true;
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (src_delta.__GQUERY__hasChanged___576037testSumAccum_1) {
          l_val.testSumAccum_1 += src_delta.testSumAccum_1;
        } else if (src_delta.__GQUERY__set___576037testSumAccum_1) {
          l_val.testSumAccum_1 = src_delta.testSumAccum_1;
        }
        context.Write(src, l_val_0, 0);
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
          {
            auto __tmp_orderkey_1 = src_val_int64_t_flag && src_delta.__GQUERY__isSrc__576037? src_val_int64_t : std::numeric_limits<int64_t>::max();
            auto __tmp_orderkey_2 = src_name_string_flag && src_delta.__GQUERY__isSrc__576037? src_name_string : std::string(1, 255);
            auto __tmp_orderkey_3 = src_label_string_flag && src_delta.__GQUERY__isSrc__576037? src_label_string : std::string(1, 255);
            auto __tmp_orderkey_4 = src_dt_DATETIME_flag && src_delta.__GQUERY__isSrc__576037? src_dt_DATETIME : DATETIME(253402214400);
            auto __tmp_orderkey_5 = src_delta.__GQUERY__isSrc__576037? l_val.testSumAccum_1.data_ : std::numeric_limits<int64_t>::max();
            context.GlobalVariableAdd(1, HeapAccum<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc, orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc> > (orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc(src, __tmp_orderkey_1, __tmp_orderkey_2, __tmp_orderkey_3, __tmp_orderkey_4, __tmp_orderkey_5)));
          }
        }
      }
      
      void VertexMap_4(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_4 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        int64_t src_val_int64_t = 0;
        bool src_val_int64_t_flag = false;
        string src_name_string = string();
        bool src_name_string_flag = false;
        string src_label_string = string();
        bool src_label_string_flag = false;
        DATETIME src_dt_DATETIME = DATETIME(0);
        bool src_dt_DATETIME_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_v1) {
          if (srcVertex.GetAttr().IsValid()) {
            src_val_int64_t = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_val, 0);
            src_val_int64_t_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_name_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_name);
            src_name_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_label_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_label);
            src_label_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_dt_DATETIME = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_dt, 0);
            src_dt_DATETIME_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_val_int64_t_flag && (src_val_int64_t < 20l)))) return;
        // prepare message
        Delta_4 src_delta = Delta_4();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // v.@testSumAccum += 1
        src_delta.testSumAccum_1 += 1l;
        src_delta.__GQUERY__hasChanged___576037testSumAccum_1 = true;
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (src_delta.__GQUERY__hasChanged___576037testSumAccum_1) {
          l_val.testSumAccum_1 += src_delta.testSumAccum_1;
        } else if (src_delta.__GQUERY__set___576037testSumAccum_1) {
          l_val.testSumAccum_1 = src_delta.testSumAccum_1;
        }
        context.Write(src, l_val_0, 0);
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
          {
            auto __tmp_orderkey_1 = src_val_int64_t_flag && src_delta.__GQUERY__isSrc__576037? src_val_int64_t : std::numeric_limits<int64_t>::max();
            auto __tmp_orderkey_2 = src_name_string_flag && src_delta.__GQUERY__isSrc__576037? src_name_string : std::string(1, 255);
            auto __tmp_orderkey_3 = src_label_string_flag && src_delta.__GQUERY__isSrc__576037? src_label_string : std::string(1, 255);
            auto __tmp_orderkey_4 = src_dt_DATETIME_flag && src_delta.__GQUERY__isSrc__576037? src_dt_DATETIME : DATETIME(253402214400);
            auto __tmp_orderkey_5 = src_delta.__GQUERY__isSrc__576037? l_val.testSumAccum_1.data_ : std::numeric_limits<int64_t>::max();
            context.GlobalVariableAdd(1, HeapAccum<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc, orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc> > (orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc(src, __tmp_orderkey_1, __tmp_orderkey_2, __tmp_orderkey_3, __tmp_orderkey_4, __tmp_orderkey_5)));
          }
        }
      }
      
      void VertexMap_6(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_6 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        int64_t src_val_int64_t = 0;
        bool src_val_int64_t_flag = false;
        string src_name_string = string();
        bool src_name_string_flag = false;
        string src_label_string = string();
        bool src_label_string_flag = false;
        DATETIME src_dt_DATETIME = DATETIME(0);
        bool src_dt_DATETIME_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_v1) {
          if (srcVertex.GetAttr().IsValid()) {
            src_val_int64_t = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_val, 0);
            src_val_int64_t_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_name_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_name);
            src_name_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_label_string = srcVertex.GetAttr().GetString(_schema_VATT_v1_576037_label);
            src_label_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_dt_DATETIME = srcVertex.GetAttr().GetInt(_schema_VATT_v1_576037_dt, 0);
            src_dt_DATETIME_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_val_int64_t_flag && (src_val_int64_t == 20l)))) return;
        // prepare message
        Delta_6 src_delta = Delta_6();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // v.@testSumAccum += 1
        src_delta.testSumAccum_1 += 1l;
        src_delta.__GQUERY__hasChanged___576037testSumAccum_1 = true;
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (src_delta.__GQUERY__hasChanged___576037testSumAccum_1) {
          l_val.testSumAccum_1 += src_delta.testSumAccum_1;
        } else if (src_delta.__GQUERY__set___576037testSumAccum_1) {
          l_val.testSumAccum_1 = src_delta.testSumAccum_1;
        }
        context.Write(src, l_val_0, 0);
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
          {
            auto __tmp_orderkey_1 = src_val_int64_t_flag && src_delta.__GQUERY__isSrc__576037? src_val_int64_t : std::numeric_limits<int64_t>::max();
            auto __tmp_orderkey_2 = src_name_string_flag && src_delta.__GQUERY__isSrc__576037? src_name_string : std::string(1, 255);
            auto __tmp_orderkey_3 = src_label_string_flag && src_delta.__GQUERY__isSrc__576037? src_label_string : std::string(1, 255);
            auto __tmp_orderkey_4 = src_dt_DATETIME_flag && src_delta.__GQUERY__isSrc__576037? src_dt_DATETIME : DATETIME(253402214400);
            auto __tmp_orderkey_5 = src_delta.__GQUERY__isSrc__576037? l_val.testSumAccum_1.data_ : std::numeric_limits<int64_t>::max();
            context.GlobalVariableAdd(1, HeapAccum<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc, orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc> > (orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc(src, __tmp_orderkey_1, __tmp_orderkey_2, __tmp_orderkey_3, __tmp_orderkey_4, __tmp_orderkey_5)));
          }
        }
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("q1")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_t_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_t_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_t_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
        gpelib4::SumVariable<HeapAccum<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc, orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc> > > __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_;
        
        {
          /*
          t = select v from v1 : v where v.val > 20 accum v.@testSumAccum += 1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_t_size = __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_t_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() -= __vset_t_size;
          __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
          __GQUERY_GV_Global_Variable__vset_t_vector_.Value().clear();
          for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().end(); ++it) {
            __GQUERY_GV_Global_Variable__vset_t_vector_.Value().push_back(it->vid);
          }
          
        }
        {
          /*
          t = select v from v1 : v where v.val < 20 accum v.@testSumAccum += 1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_3_";
          if (!manager.RunCMD(
            action_3_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_3_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_t_size = __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_4_map";
          if (!manager.RunCMD(
            action_4_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_t_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_4_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() -= __vset_t_size;
          __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
          __GQUERY_GV_Global_Variable__vset_t_vector_.Value().clear();
          for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().end(); ++it) {
            __GQUERY_GV_Global_Variable__vset_t_vector_.Value().push_back(it->vid);
          }
          
        }
        {
          /*
          t = select v from v1 : v where v.val == 20 accum v.@testSumAccum += 1 order by v.val, v.name, v.label, v.dt, v.@testSumAccum limit 20;
          */
          int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_5_";
          if (!manager.RunCMD(
            action_5_, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_5_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
          int64_t __vset_t_size = __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().resize(20l);
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master starts action_6_map";
          if (!manager.RunCMD(
            action_6_map, 
            {}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_t_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_6_map RunCMD failed.";
            return;
          }
          // limit
          __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() -= __vset_t_size;
          __vset_t_size = __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value();
          //post action
          gpelib4::StateVariable<int64_t> __limit_(20l);
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "master limit vset";
          if (!manager.RunCMD(
            action_6_post, 
            {&__limit_, &__GQUERY_GV_Global_Variable__vset_t_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_t_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "action_6_post RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() -= __vset_t_size;
          __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
          __GQUERY_GV_Global_Variable__vset_t_vector_.Value().clear();
          for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().end(); ++it) {
            __GQUERY_GV_Global_Variable__vset_t_vector_.Value().push_back(it->vid);
          }
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          gpr::GPR_Container* __GQUERY__value_container0 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_0__576037>();
          gutil::ScopedCleanUp sc_val0(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container0, &_request));
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_t = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_t_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_t_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_t_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          gpelib4::SumVariable<HeapAccum<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc, orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_ascCompare<orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc> > > __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_;
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<Delta_4>();
          gutil::ScopedCleanUp sc_msg4(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request));
          gpr::GPR_Container* __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
          gutil::ScopedCleanUp sc_msg6(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_v1, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_t_SIZE__) __GQUERY_GV_Global_Variable__vset_t_SIZE___output;
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::g1::GPR_q1::VertexMap_2, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {__GQUERY__value_container0},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<int64_t> hint_VATT_v1_val0 = {20l, GSQL_INT_MAX};
                    if (_schema_VTY_v1 != -1 && _schema_VATT_v1_576037_val != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_int64_t(gindex::RANGE_GT_LE, _schema_VTY_v1, _schema_VATT_v1_576037_val, hint_VATT_v1_val0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() = __GQUERY__vSet_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_t_SIZE___output.Value() = __GQUERY__vSet_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_});
                }
                __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
              }
              else if (worker->GetAction() == action_3_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_3_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_v1, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_3_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_4_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_4_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_t_SIZE__) __GQUERY_GV_Global_Variable__vset_t_SIZE___output;
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<Delta_4>();
                sc_msg4 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::g1::GPR_q1::VertexMap_4, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {__GQUERY__value_container0},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<int64_t> hint_VATT_v1_val0 = {GSQL_INT_MIN, 20l};
                    if (_schema_VTY_v1 != -1 && _schema_VATT_v1_576037_val != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_int64_t(gindex::RANGE_GE_LT, _schema_VTY_v1, _schema_VATT_v1_576037_val, hint_VATT_v1_val0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() = __GQUERY__vSet_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_t_SIZE___output.Value() = __GQUERY__vSet_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_4_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_});
                }
                __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
              }
              else if (worker->GetAction() == action_5_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_5_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_v1, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_5_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_6_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_6_map";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_t_SIZE__) __GQUERY_GV_Global_Variable__vset_t_SIZE___output;
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().clear();
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().resize(20l);
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
                sc_msg6 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::g1::GPR_q1::VertexMap_6, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {__GQUERY__value_container0},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_}//output gv
                );
                vertexAction->AddInputObject(this);
                
                {
                  gvector<gvector<gindex::IndexPredicate>> hints_vertexAction;
                  {
                    gvector<gindex::IndexPredicate> hint_vertexAction;
                    gvector<int64_t> hint_VATT_v1_val0 = {20l};
                    if (_schema_VTY_v1 != -1 && _schema_VATT_v1_576037_val != -1) {
                      hint_vertexAction.emplace_back(gindex::IndexHint::CreateIndexPredicate_int64_t(gindex::EQUAL, _schema_VTY_v1, _schema_VATT_v1_576037_val, hint_VATT_v1_val0));
                    }
                    hints_vertexAction.push_back(std::move(hint_vertexAction));
                  }
                  gindex::IndexHint indexHint_vertexAction(std::move(hints_vertexAction));
                  vertexAction->SetSrcIndexHint(std::move(indexHint_vertexAction));
                }
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_t, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() = __GQUERY__vSet_t->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_t_SIZE___output.Value() = __GQUERY__vSet_t->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker finish action_6_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_t_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_});
                }
                __GQUERY_GV_Global_Variable__vset_t_hasOrder_.Value() = true;
              }
              else if (worker->GetAction() == action_6_post) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "q1") << _request.requestid_ << "|" << "worker start action_6_post";
                gpelib4::StateVariable<int64_t> __limit_;
                //deserialize limit and vset size
                worker->Deserialize({&__limit_, &__GQUERY_GV_Global_Variable__vset_t_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_});
                //limit vset size
                __GQUERY__vSet_t->SetAllActiveFlag(false);
                for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_string_asc_string_asc_datetime_asc_int_asc_.Value().end(); ++it) {
                  __GQUERY__vSet_t->SetActiveFlag(it->vid);
                }
                __GQUERY_GV_Global_Variable__vset_t_SIZE__.Value() = __GQUERY__vSet_t->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_t_SIZE__});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      const int _schema_VTY_v1 = 0;
      int _schema_VATT_v1_576037_val = -1;
      int _schema_VATT_v1_576037_name = -1;
      int _schema_VATT_v1_576037_label = -1;
      int _schema_VATT_v1_576037_dt = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_ = "action_3_";
      const std::string action_4_map = "action_4_map";
      const std::string action_4_reduce = "action_4_reduce";
      const std::string action_4_post = "action_4_post";
      const std::string action_5_ = "action_5_";
      const std::string action_6_map = "action_6_map";
      const std::string action_6_reduce = "action_6_reduce";
      const std::string action_6_post = "action_6_post";
      public:
        
        ///return vars
      };//end class GPR_q1
    bool call_q1(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::g1::GPR_q1 gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_q1_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::g1::GPR_q1 gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_q1(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::g1::GPR_q1 gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "q1") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_q1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::g1::GPR_q1 gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_q1(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::g1::GPR_q1 gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace g1
}//end namespace UDIMPL
