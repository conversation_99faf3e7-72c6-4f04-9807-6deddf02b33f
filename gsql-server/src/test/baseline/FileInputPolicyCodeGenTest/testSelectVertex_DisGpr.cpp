/********** query start ***************
create distributed query testSelectVertex_Dis_Dynamic(String filename)
  for graph poc_graph {
  S1 = SelectVertex(filename, $0, company, ",", false);
  print S1;
}
********** query end ***************/
#include "poc_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"
#include "utility/gutil/fileinputoutputpolicy/FileInputOutputPolicy.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace poc_graph {
    class GPR_testSelectVertex_Dis_Dynamic {
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testSelectVertex_Dis_Dynamic (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_company_576037_label = VTY_company_attrMeta.GetAttributePosition("label", true);
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        _schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
        if (request.jsoptions_.isMember("filename")) {
          _filename = request.jsoptions_["filename"][0].asString();
          filename_flag = true;
        } else {
          // parameter is not given (null case)
          _filename = string();
          filename_flag = false;
        }
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        // construct file policy: input
        if (request.jsoptions_.isMember ("__GQUERY__FILE_INPUT_POLICY__")) {
          gvector<std::string> file_policy_list;
          const auto& file_policy_json = request.jsoptions_["__GQUERY__FILE_INPUT_POLICY__"];
          for (Json::Value::const_iterator it=file_policy_json.begin(); it!=file_policy_json.end(); ++it) {
            file_policy_list.push_back(it->asString());
          }
          __GQUERY__input_policy__576037 = std::make_unique<gutil::FileInputOutputPolicy>(file_policy_list, true);
        } else {
          std::string msg("Runtime Error: File input policy is not built!");
          throw gutil::GsqlException(msg, gutil::error_t::E_FILE_INPUT_POLICY);
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_testSelectVertex_Dis_Dynamic (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, string filename, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_id = VTY_company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_company_576037_label = VTY_company_attrMeta.GetAttributePosition("label", true);
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        _schema_VATT_company_576037_country = VTY_company_attrMeta.GetAttributePosition("country", true);
        _filename = filename;
        filename_flag = true;
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        // construct file policy: input
        if (request.jsoptions_.isMember ("__GQUERY__FILE_INPUT_POLICY__")) {
          gvector<std::string> file_policy_list;
          const auto& file_policy_json = request.jsoptions_["__GQUERY__FILE_INPUT_POLICY__"];
          for (Json::Value::const_iterator it=file_policy_json.begin(); it!=file_policy_json.end(); ++it) {
            file_policy_list.push_back(it->asString());
          }
          __GQUERY__input_policy__576037 = std::make_unique<gutil::FileInputOutputPolicy>(file_policy_list, true);
        } else {
          std::string msg("Runtime Error: File input policy is not built!");
          throw gutil::GsqlException(msg, gutil::error_t::E_FILE_INPUT_POLICY);
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_testSelectVertex_Dis_Dynamic () {}
      
      ///vertex actions for write
      void Write_2(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(2);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int S1_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_company != -1 && _schema_VTY_company == S1_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_company_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_company_576037_id));
          }
          if (_schema_VATT_company_576037_label != -1) {
            writer.WriteNameString("label");
            writer.WriteBool(vVertex.GetAttr().GetBool(_schema_VATT_company_576037_label, 0));
          }
          if (_schema_VATT_company_576037_company_name != -1) {
            writer.WriteNameString("company_name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_company_576037_company_name));
          }
          if (_schema_VATT_company_576037_country != -1) {
            writer.WriteNameString("country");
            (string_compress(&vVertex.GetAttr(), _schema_VATT_company_576037_country)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("testSelectVertex_Dis_Dynamic")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_filename_(_filename);
        gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_filename_flag(filename_flag);
        
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_S1_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_S1_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_S1_vector_;
        {
          /*
          S1 = selectvertex ( filename, $ 0, company, ",", false );
          */
          int64_t __vset_S1_size = __GQUERY_GV_Global_Variable__vset_S1_SIZE__.Value();
          gpelib4::StateVariable<SetAccum<VERTEX>> __GQUERY_GV_Global_Variable_vList_4_File__;
          // read vertex from file
          {
            std::string fileName = __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_filename_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_filename_flag, true, "filename_flag$"), "filename");
            // check file policy: input
            std::string _file_input_policy_err_msg;
            if (!__GQUERY__input_policy__576037->allows(fileName, _file_input_policy_err_msg)) {
              _request.SetErrorCode(gutil::error_t::E_FILE_INPUT_POLICY);
              HF_set_error(_request, _file_input_policy_err_msg, true);
              return;
            }
            char separator = GSQL_UTIL::HF_convertSeparator(string(","));
            bool useHeader = false;
            ghash_map<std::string, uint32_t> header2Index;
            if (!__gquery__file_exists(fileName)) {
              std::string msg("Runtime Error: File '" + fileName + "' does not exist.");
              _request.SetErrorCode(gutil::error_t::E_FILE_NOT_EXIST);
              _request.SetErrorMessage(msg);
              _request.error_ = true;
              return;
            }
            boost::filesystem::path gsql_f_path_(fileName);
            boost::filesystem::path gsql_c_path_ = boost::filesystem::canonical(gsql_f_path_);
            if (!boost::filesystem::is_regular_file(gsql_c_path_)) {
              std::string msg("Runtime Error: File '" + fileName + "' is not a regular file, please make sure it is a regular file.");
              _request.SetErrorCode(gutil::error_t::E_FILE_NOT_REGULAR);
              _request.SetErrorMessage(msg);
              _request.error_ = true;
              return;
            }
            gutil::FileLineReader fileReader(fileName);
            GSQL_UTIL::HF_checkFileAndGetHeader(fileName, fileReader, separator, useHeader, &header2Index);
            int idIndx = -1;
            int typeIndx = -1;
            char* chrPtr = NULL;
            size_t chrLen;
            idIndx = 0l;
            gvector<std::pair<std::string, std::string> > type_uid_list;
            std::pair<std::string, std::string> tmpPair;
            int maxPosition = (idIndx > typeIndx) ? idIndx : typeIndx;
            while (fileReader.MoveNextLine()) {
              for (int i = 0; i <= maxPosition; ++i) {
                if (!fileReader.NextString(chrPtr, chrLen, separator)) {
                  std::string msg("Runtime Error: The row does not contain enough tokens.");
                  _request.SetErrorCode(gutil::error_t::E_INDEX_OUT_BND);
                  _request.SetErrorMessage(msg);
                  _request.error_ = true;
                  return;
                }
                if (i == idIndx) {
                  tmpPair.second = std::string(chrPtr, chrLen);
                }
                tmpPair.first = "company";
              }
              type_uid_list.push_back(tmpPair);
            }
            std::stringstream error_ss;
            gvector<VertexLocalId_t> vidList = _serviceapi->Convert_UIdtoVId(&_request, type_uid_list, error_ss);
            if (vidList.empty()) {
              HF_set_error(_request, error_ss.str(), false);
            }else{
              uint32_t failedToConvert = 0;
              for (uint32_t i = 0; i < vidList.size(); ++i) {
                if (vidList[i] == (VertexLocalId_t)-1) {
                  failedToConvert++;
                  std::string msg("Runtime Warning: " + boost::lexical_cast<string>(failedToConvert) + " vertices failed to convert. Check input file to verify!");
                  HF_set_error(_request, msg, false);
                } else {
                  __GQUERY_GV_Global_Variable_vList_4_File__.Value() += vidList[i];
                }
              }
            }
          }//end read vertex from file
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {&__GQUERY_GV_Global_Variable_vList_4_File__, &__GQUERY_GV_Global_Variable_filename_, &__GQUERY_GV_Global_Variable_filename_flag}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_S1_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_S1_SIZE__.Value() -= __vset_S1_size;
          
        }
        {
          /*
          print S1;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "master starts action_2_";
          if (__GQUERY_GV_Global_Variable__vset_S1_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_2_, 
            {&__GQUERY_GV_Global_Variable_filename_, &__GQUERY_GV_Global_Variable_filename_flag, &__GQUERY_GV_Global_Variable__vset_S1_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "action_2_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_S1_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("S1");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_S1_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_S1_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("S1");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_S1 = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_filename_(_filename);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_filename_flag(filename_flag);
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_S1_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_S1_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_S1_vector_;
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "worker start action_1_";
                gpelib4::StateVariable<SetAccum<VERTEX>> __GQUERY_GV_Global_Variable_vList_4_File__;
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_vList_4_File__, &__GQUERY_GV_Global_Variable_filename_, &__GQUERY_GV_Global_Variable_filename_flag});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_S1_SIZE__) __GQUERY_GV_Global_Variable__vset_S1_SIZE___output;
                //run action
                __GQUERY__vSet_S1->SetAllActiveFlag(false);
                ActiveVertex(__GQUERY__vSet_S1, __GQUERY_GV_Global_Variable_vList_4_File__.Value());
                __GQUERY_GV_Global_Variable__vset_S1_SIZE__.Value() = __GQUERY__vSet_S1->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_S1_SIZE___output.Value() = __GQUERY__vSet_S1->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_S1_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "worker start action_2_";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_filename_, &__GQUERY_GV_Global_Variable_filename_flag, &__GQUERY_GV_Global_Variable__vset_S1_hasOrder_});
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_S1_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic::Write_2, this),//action function
                  __GQUERY__vSet_S1.get(),//input bitset
                  {},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable_filename_, &__GQUERY_GV_Global_Variable_filename_flag, &__GQUERY_GV_Global_Variable__vset_S1_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|" << "worker finish action_2_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      string _filename;
      bool filename_flag = true;
      gunique_ptr<gutil::FileInputOutputPolicy> __GQUERY__input_policy__576037 = nullptr;
      const int _schema_VTY_company = 0;
      int _schema_VATT_company_576037_id = -1;
      int _schema_VATT_company_576037_label = -1;
      int _schema_VATT_company_576037_company_name = -1;
      int _schema_VATT_company_576037_country = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_ = "action_2_";
      public:
        
        ///return vars
      };//end class GPR_testSelectVertex_Dis_Dynamic
    bool call_testSelectVertex_Dis_Dynamic(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testSelectVertex_Dis_Dynamic_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_testSelectVertex_Dis_Dynamic(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 1) {
          HF_set_error(_request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        string filename = values[0]->GetStringInternal();
        
        UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic gpr(graphAPI.get(), _request, serviceapi, graphupdates, filename, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testSelectVertex_Dis_Dynamic") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testSelectVertex_Dis_Dynamic(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string filename) {
      UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic gpr(graphAPI, request, serviceapi, _graphupdates_, filename, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testSelectVertex_Dis_Dynamic(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum, string filename) {
      UDIMPL::poc_graph::GPR_testSelectVertex_Dis_Dynamic gpr(graphAPI, request, serviceapi, _graphupdates_, filename, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace poc_graph
}//end namespace UDIMPL
