/********** query start ***************
CREATE DISTRIBUTED QUERY test()
FOR GRAPH poc_graph {
  SumAccum<INT> @@testaccum;
  foreach i in range[1,3] do
    string index = to_string(i);
    file f ("/tmp/file"+ index +".txt");
    vs = select tgt
         from members:src -(member_follow_company>)- company:tgt
         accum f.println(tgt.company_name + index);
    f.close();
  end;
}
********** query end ***************/
#include "poc_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"
#include "utility/gutil/fileinputoutputpolicy/FileInputOutputPolicy.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace poc_graph {
    class GPR_test {
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_test (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_test (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_test () {}
      
      ///vertex actions for write
      
      ///vertex/edge actions
      void EdgeMap_3(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_company >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_company)
          return;
        string tgt_company_name_string = string();
        bool tgt_company_name_string_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_company) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_company_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_company_576037_company_name);
            tgt_company_name_string_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        DefaultDelta tgt_delta = DefaultDelta();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        auto& gstream_f_2 = context.GetOStream(0);
        
        // f.println ( tgt.company_name + index )
        if (tgt_company_name_string_flag) {
          gstream_f_2 << (tgt_company_name_string + __HF_GPR_retrieveGV<string >(context, 0, true, "index_2")) << "\n";
          ;
        }
        context.Write(tgt, tgt_delta, 0);
        context.Activate(tgt, 0);
      }
      void Reduce_3(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        auto& gstream_f_2 = context.GetOStream(0);
        if (delta.__GQUERY__isTgt__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("test")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        
        gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_1_;
        gpelib4::StateVariable<string> __GQUERY_GV_Global_Variable_index_2_;
        gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_testaccum_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vs_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vs_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vs_vector_;
        
        {
          /*
          @@testaccum_1 reinitiate
          */
          
          // @@testaccum_1 reinitiate
          __GQUERY_GV_testaccum_1_.Value().clear();
          
        }
        {
          {
            RangeObject _rangeobject = RangeObject(1l, 3l, 1l);
            if (!_rangeobject.is_valid()) {
              return;
            }
            gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_;
            uint64_t __GQUERY__iter_3 = 0;
            auto  __GQUERY__accum_3 = _rangeobject;
            for (auto it = ( __GQUERY__accum_3).begin(); it != ( __GQUERY__accum_3).end(); it++) {
              __GQUERY_GV_Global_Variable_i_.Value() = *it;
              {
                /*
                string index = to_string ( i );
                */
                
                // string index = to_string ( i );
                __GQUERY_GV_Global_Variable_index_2_.Value() = to_string(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_i_, true, "i"));
                
              }
              
              {
                /*
                file f ( "/tmp/file" + index + ".txt" );
                */
                
                // file f ( "/tmp/file" + index + ".txt" );
                {
                  std::string __GQUERY__file_name__576037(std::string() + ((string("/tmp/file") + __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_index_2_, true, "index_2")) + string(".txt")));
                  if (!__GQUERY__file_name__576037.empty()) {
                    gutil::FileInputOutputPolicy __GQUERY__output_policy__576037({"/"}, false);
                    std::string _file_output_policy_err_msg;
                    if (!__GQUERY__output_policy__576037.allows(__GQUERY__file_name__576037, _file_output_policy_err_msg)) {
                      HF_set_error(_request, _file_output_policy_err_msg, true);
                      return;
                    }
                    _f_2 = _serviceapi->CreateOutStream(__GQUERY__file_name__576037, true);
                    if (!_f_2->IsFileOpen()) {
                      std::string msg("Runtime Error: Cannot open file " + __GQUERY__file_name__576037);
                      HF_set_error(_request, msg, true);
                      return;
                    }
                  } else {
                    f_2_flag = false;
                  }
                }
                GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "master starts action_1_";
                if (!manager.RunCMD(
                  action_1_, 
                  {&__GQUERY_GV_Global_Variable_index_2_}, //input gvs
                  {} //output gvs
                  )) {
                    GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
                  return;
                }
                
              }
              
              {
                /*
                vs = select tgt from members : src - ( member_follow_company > : x ) - company : tgt accum f.println ( tgt.company_name + index );
                */
                int64_t __vset___GQUERY__source_size = __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value();
                GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "master starts action_2_";
                if (!manager.RunCMD(
                  action_2_, 
                  {}, //input gvs
                  {&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__} //output gvs
                  )) {
                    GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "action_2_ RunCMD failed.";
                  return;
                }
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() -= __vset___GQUERY__source_size;
                int64_t __vset_vs_size = __GQUERY_GV_Global_Variable__vset_vs_SIZE__.Value();
                // map
                GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "master starts action_3_map";
                if (!manager.RunCMD(
                  action_3_map, 
                  {&__GQUERY_GV_Global_Variable_index_2_}, //input gvs
                  {&__GQUERY_GV_Global_Variable__vset_vs_SIZE__} //output gvs
                  )) {
                    GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "action_3_map RunCMD failed.";
                  return;
                }
                // reduce
                GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "master starts action_3_reduce";
                if (!manager.RunCMD(
                  action_3_reduce, 
                  {&__GQUERY_GV_Global_Variable_index_2_}, //input gvs
                  {&__GQUERY_GV_Global_Variable__vset_vs_SIZE__} //output gvs
                  )) {
                    GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "action_3_reduce RunCMD failed.";
                  return;
                }
                __GQUERY_GV_Global_Variable__vset_vs_SIZE__.Value() -= __vset_vs_size;
                __GQUERY_GV_Global_Variable__vset_vs_hasOrder_.Value() = false;
                
              }
              
              {
                /*
                f.close ( );
                */
                
                // f.close ( );
                {
                  if (_f_2 != nullptr) {
                    _f_2.reset();
                    _f_2 = nullptr;
                  } else {
                    std::string msg("Runtime Error: Cannot close null file.");
                    throw gutil::GsqlException(msg, gutil::error_t::E_FILE_PERM_DENY);
                  }
                }
                ;
                GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "master starts action_4_";
                if (!manager.RunCMD(
                  action_4_, 
                  {}, //input gvs
                  {} //output gvs
                  )) {
                    GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "action_4_ RunCMD failed.";
                  return;
                }
                
              }
              
              if (UNLIKELY((++__GQUERY__iter_3 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
                std::string msg("Aborted due to timeout or system memory in critical state.");
                HF_set_error(_request, msg, true);
                return;
              }
            }
          }
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet___GQUERY__source = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vs = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          
          gpelib4::StateVariable<int64_t> __GQUERY_GV_Global_Variable_i_1_1_;
          gpelib4::StateVariable<string> __GQUERY_GV_Global_Variable_index_2_;
          gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_testaccum_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset___GQUERY__source_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset___GQUERY__source_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vs_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vs_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vs_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg3(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_index_2_});
                //declare output gvs
                //run action
                
                // file f ( "/tmp/file" + index + ".txt" );
                {
                  std::string __GQUERY__file_name__576037(std::string() + ((string("/tmp/file") + __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_index_2_, true, "index_2")) + string(".txt")));
                  if (!__GQUERY__file_name__576037.empty()) {
                    gutil::FileInputOutputPolicy __GQUERY__output_policy__576037({"/"}, false);
                    std::string _file_output_policy_err_msg;
                    if (!__GQUERY__output_policy__576037.allows(__GQUERY__file_name__576037, _file_output_policy_err_msg)) {
                      HF_set_error(_request, _file_output_policy_err_msg, true);
                      return;
                    }
                    _f_2 = _serviceapi->CreateOutStream(__GQUERY__file_name__576037, true);
                    if (!_f_2->IsFileOpen()) {
                      std::string msg("Runtime Error: Cannot open file " + __GQUERY__file_name__576037);
                      HF_set_error(_request, msg, true);
                      return;
                    }
                  } else {
                    f_2_flag = false;
                  }
                }
                
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker finish action_1_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({});
                }
              }
              else if (worker->GetAction() == action_2_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker start action_2_";
                //deserialize input gvs
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__) __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output;
                //run action
                __GQUERY__vSet___GQUERY__source->SetAllActiveFlag(false);
                __GQUERY__vSet___GQUERY__source->SetActiveFlagByType(_schema_VTY_members, true);
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE__.Value() = __GQUERY__vSet___GQUERY__source->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker finish action_2_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output.Value() = __GQUERY__vSet___GQUERY__source->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset___GQUERY__source_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_3_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker start action_3_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_index_2_});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vs_SIZE__) __GQUERY_GV_Global_Variable__vset_vs_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg3 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_member_follow_company);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::poc_graph::GPR_test::EdgeMap_3, this),//action function
                  __GQUERY__vSet___GQUERY__source.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container3},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_index_2_},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_vs_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                
                if (_f_2 != nullptr || !f_2_flag) {
                  GSQL_UTIL::AddOutputFile(edgeAction, _f_2);
                } else {
                  std::string msg("Runtime Error: Cannot output to null file.");
                  throw gutil::GsqlException(msg, gutil::error_t::E_FILE_PERM_DENY);
                }
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                filter.addTgtAttribute(
                  _schema_VTY_company, {(unsigned)_schema_VATT_company_576037_company_name}
                );
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container3}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker finish action_3_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vs_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_vs_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_3_reduce) {
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_index_2_});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_vs_SIZE__) __GQUERY_GV_Global_Variable__vset_vs_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_vs->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test::Reduce_3, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container3},//input container(old v_value and delta)
                  {},//output container(new v_value)
                  {__GQUERY__vSet_vs.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_index_2_},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_vs_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                
                if (_f_2 != nullptr || !f_2_flag) {
                  GSQL_UTIL::AddOutputFile(reduceAction, _f_2);
                } else {
                  std::string msg("Runtime Error: Cannot output to null file.");
                  throw gutil::GsqlException(msg, gutil::error_t::E_FILE_PERM_DENY);
                }
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_vs_SIZE__.Value() = __GQUERY__vSet_vs->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_vs_SIZE___output.Value() = __GQUERY__vSet_vs->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_vs_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_4_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker start action_4_";
                //deserialize input gvs
                //declare output gvs
                //run action
                
                // f.close ( );
                {
                  if (_f_2 != nullptr) {
                    _f_2.reset();
                    _f_2 = nullptr;
                  } else {
                    std::string msg("Runtime Error: Cannot close null file.");
                    throw gutil::GsqlException(msg, gutil::error_t::E_FILE_PERM_DENY);
                  }
                }
                ;
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test") << _request.requestid_ << "|" << "worker finish action_4_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      gshared_ptr<gutil::GOutputStreamFile>  _f_2;
      bool f_2_flag = true;
      const int _schema_VTY_company = 0;
      const int _schema_VTY_members = 1;
      const int _schema_ETY_member_follow_company = 1;
      int _schema_VATT_company_576037_company_name = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_ = "action_2_";
      const std::string action_3_map = "action_3_map";
      const std::string action_3_reduce = "action_3_reduce";
      const std::string action_3_post = "action_3_post";
      const std::string action_4_ = "action_4_";
      public:
        
        ///return vars
      };//end class GPR_test
    bool call_test(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::poc_graph::GPR_test gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_test_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::poc_graph::GPR_test gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_test(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 0) {
          HF_set_error(_request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        
        UDIMPL::poc_graph::GPR_test gpr(graphAPI.get(), _request, serviceapi, graphupdates, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_test(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
      UDIMPL::poc_graph::GPR_test gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_test(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum) {
      UDIMPL::poc_graph::GPR_test gpr(graphAPI, request, serviceapi, _graphupdates_, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace poc_graph
}//end namespace UDIMPL
