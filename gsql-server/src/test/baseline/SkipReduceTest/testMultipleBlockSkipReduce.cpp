/********** query start ***************
create distributed query test_reduce(int k) for graph poc_graph {
  SumAccum<int> @va;
  SumAccum<string> @va2;
  SumAccum<string> @label;
  SumAccum<int> @@cnt;
  start = { members.* };
  start = select s from start:s
       accum @@cnt += s.registrationDate, s.@va += s.registrationDate;
  res = select t
        from start:s-(member_work_company:e)->:t
        accum s.@va += s.registrationDate
        post-accum (t) @@cnt += 1
        having t.company_name == "com1";
  start = select s from start:s
       accum s.@label += "_sufix", @@cnt += s.@va;
  start = select s from start:s
       order by s.@va asc
       limit k;
  start = select s
       from start:s -(:e)-> company:t
       accum @@cnt += s.registrationDate, s.@va2 += t.company_name
       order by s.@va asc
       limit k;
  print @@cnt;
  print start;
}
********** query end ***************/
#include "poc_graph-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace poc_graph {
    class GPR_test_reduce {
      struct orderby_heap_int_asc {
        VERTEX vid;
        int64_t key_0;
        
        orderby_heap_int_asc() {
          key_0 = 0;
        }
        
        orderby_heap_int_asc(VERTEX vid_, int64_t key_0_) {
          vid = vid_;
          key_0 = key_0_;
        }
        
        orderby_heap_int_asc(const std::tuple<VERTEX, int64_t>& __GQUERY__other__576037) {
          vid = std::get<0>(__GQUERY__other__576037);
          key_0 = std::get<1>(__GQUERY__other__576037);
        }
        
        friend std::ostream& operator<<(std::ostream& os, const orderby_heap_int_asc& __GQUERY__other__576037) {
          os << "[";
          os << "vid " << __GQUERY__other__576037.vid << "|";
          os << "key_0 " << __GQUERY__other__576037.key_0 << "]";
          return os;
        }
        
        bool operator==(const orderby_heap_int_asc& __GQUERY__other__576037) const {
          return
            vid == __GQUERY__other__576037.vid &&
            key_0 == __GQUERY__other__576037.key_0;
        }
        
        orderby_heap_int_asc& operator+=(const orderby_heap_int_asc& __GQUERY__other__576037) {
          vid += __GQUERY__other__576037.vid;
          key_0 += __GQUERY__other__576037.key_0;
          return *this;
        }
        
        bool operator<(const orderby_heap_int_asc& __GQUERY__other__576037) const {
          if (vid < __GQUERY__other__576037.vid) return true;
          if (vid > __GQUERY__other__576037.vid) return false;
          if (key_0 < __GQUERY__other__576037.key_0) return true;
          if (key_0 > __GQUERY__other__576037.key_0) return false;
          return false;
        }
        
        bool operator>(const orderby_heap_int_asc& __GQUERY__other__576037) const {
          if (vid > __GQUERY__other__576037.vid) return true;
          if (vid < __GQUERY__other__576037.vid) return false;
          if (key_0 > __GQUERY__other__576037.key_0) return true;
          if (key_0 < __GQUERY__other__576037.key_0) return false;
          return false;
        }
        
        operator std::tuple<VERTEX, int64_t>() const {
          return std::make_tuple(vid,key_0);
        }
        
        friend std::size_t hash_value(const orderby_heap_int_asc& other) {
          std::size_t seed = 0;
          boost::hash_combine(seed, other.vid);
          boost::hash_combine(seed, other.key_0);
          return seed;
        }
        void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            writer.WriteStartObject();
          writer.WriteNameString("vid");
          (vid).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("key_0");
          writer.WriteInt(key_0);
          writer.WriteEndObject();
        }
        gutil::JSONWriter& json_write_name (
          gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
          gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
            std::string ss = boost::lexical_cast<std::string>(*this);
          return writer.WriteNameString(ss.c_str());
        }
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(vid, key_0);
        }
      };
      template <typename TUPLE_t>
      class orderby_heap_int_ascCompare {
        bool _reverse;
        public: 
          orderby_heap_int_ascCompare() : _reverse(false) {}
        orderby_heap_int_ascCompare(bool reverse) : _reverse(reverse) {}
        bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
          if (lhs.key_0 < rhs.key_0) return _reverse^true;
          if (lhs.key_0 > rhs.key_0) return _reverse^false;
          return false;
        }
      };
      
      struct Delta_2 {
        // accumulators
        SumAccum<int64_t >  va_1;
        bool __GQUERY__hasChanged___576037va_1;
        bool __GQUERY__set___576037va_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_2 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037va_1 = false;
          __GQUERY__set___576037va_1 = false;
        }
        // message combinator
        Delta_2& operator+= (const Delta_2& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037va_1) {
            va_1 += __GQUERY__other_.va_1;
            __GQUERY__hasChanged___576037va_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037va_1) {
            va_1 = __GQUERY__other_.va_1;
            __GQUERY__set___576037va_1 = true;
          }
          return *this;
        }
      };
      struct Delta_3 {
        // accumulators
        SumAccum<int64_t >  va_1;
        bool __GQUERY__hasChanged___576037va_1;
        bool __GQUERY__set___576037va_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_3 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037va_1 = false;
          __GQUERY__set___576037va_1 = false;
        }
        // message combinator
        Delta_3& operator+= (const Delta_3& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037va_1) {
            va_1 += __GQUERY__other_.va_1;
            __GQUERY__hasChanged___576037va_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037va_1) {
            va_1 = __GQUERY__other_.va_1;
            __GQUERY__set___576037va_1 = true;
          }
          return *this;
        }
      };
      struct Delta_4 {
        // accumulators
        SumAccum<string >  label_1;
        bool __GQUERY__hasChanged___576037label_1;
        bool __GQUERY__set___576037label_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_4 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037label_1 = false;
          __GQUERY__set___576037label_1 = false;
        }
        // message combinator
        Delta_4& operator+= (const Delta_4& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037label_1) {
            label_1 += __GQUERY__other_.label_1;
            __GQUERY__hasChanged___576037label_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037label_1) {
            label_1 = __GQUERY__other_.label_1;
            __GQUERY__set___576037label_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, label_1, __GQUERY__hasChanged___576037label_1, __GQUERY__set___576037label_1);
        }
      };
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct Delta_6 {
        // accumulators
        SumAccum<string >  va2_1;
        bool __GQUERY__hasChanged___576037va2_1;
        bool __GQUERY__set___576037va2_1;
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        Delta_6 () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
          __GQUERY__hasChanged___576037va2_1 = false;
          __GQUERY__set___576037va2_1 = false;
        }
        // message combinator
        Delta_6& operator+= (const Delta_6& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          if (__GQUERY__other_.__GQUERY__hasChanged___576037va2_1) {
            va2_1 += __GQUERY__other_.va2_1;
            __GQUERY__hasChanged___576037va2_1 = true;
          } else if (__GQUERY__other_.__GQUERY__set___576037va2_1) {
            va2_1 = __GQUERY__other_.va2_1;
            __GQUERY__set___576037va2_1 = true;
          }
          return *this;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(__GQUERY__isSrc__576037, __GQUERY__isTgt__576037, va2_1, __GQUERY__hasChanged___576037va2_1, __GQUERY__set___576037va2_1);
        }
      };
      struct __GQUERY__VertexVal_0__576037 {
        // accumulators:
        SumAccum<int64_t >  va_1;
        SumAccum<string >  va2_1;
        SumAccum<string >  label_1;
        // default constructor
        __GQUERY__VertexVal_0__576037 () {};
        // copy constructor
        __GQUERY__VertexVal_0__576037 (const __GQUERY__VertexVal_0__576037& __GQUERY__other_) {
          va_1 = __GQUERY__other_.va_1;
          va2_1 = __GQUERY__other_.va2_1;
          label_1 = __GQUERY__other_.label_1;
        }
        // serialise interface
        template <class ARCHIVE>
        void serialize(ARCHIVE& __GQUERY__ar_) {
          __GQUERY__ar_(va_1, va2_1, label_1);
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
        SumAccum<int64_t > * va_1;
        SumAccum<string > * va2_1;
        SumAccum<string > * label_1;
        void add (const __GQUERY__VertexVal_0__576037& __GQUERY__other_) {
          va_1 = const_cast<SumAccum<int64_t > * > (&__GQUERY__other_.va_1);
          va2_1 = const_cast<SumAccum<string > * > (&__GQUERY__other_.va2_1);
          label_1 = const_cast<SumAccum<string > * > (&__GQUERY__other_.label_1);
        }
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        SumAccum<int64_t > * va_1Ptr;
        SumAccum<string > * va2_1Ptr;
        SumAccum<string > * label_1Ptr;
        SumAccum<int64_t > & va_1 = * va_1Ptr;
        SumAccum<string > & va2_1 = * va2_1Ptr;
        SumAccum<string > & label_1 = * label_1Ptr;
        // default constructor
        __GQUERY__VertexVal__576037 () {};
        // constructor
        __GQUERY__VertexVal__576037 (__GQUERY__VertexVal_ptr__576037& __GQUERY__other_) : va_1(*__GQUERY__other_.va_1), va2_1(*__GQUERY__other_.va2_1), label_1(*__GQUERY__other_.label_1)
          {}
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_test_reduce (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
        _schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
        if (request.jsoptions_.isMember("k")) {
          _k = request.jsoptions_["k"][0].asInt64();
          k_flag = true;
        } else {
          // parameter is not given (null case)
          _k = 0;
          k_flag = false;
        }
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_test_reduce (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, int64_t k, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_company_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_company_576037_company_name = VTY_company_attrMeta.GetAttributePosition("company_name", true);
        topology4::AttributesMeta& VTY_members_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_members_576037_id = VTY_members_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_members_576037_profileIndustryId = VTY_members_attrMeta.GetAttributePosition("profileIndustryId", true);
        _schema_VATT_members_576037_registrationDate = VTY_members_attrMeta.GetAttributePosition("registrationDate", true);
        _k = k;
        k_flag = true;
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_test_reduce () {}
      
      ///vertex actions for write
      void Write_8(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(2);
        __GQUERY__VertexVal_ptr__576037 vptr_v_val;
        vptr_v_val.add(*vVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        V_VALUE v_val(vptr_v_val);
        
        // json writer
        gutil::JSONWriter writer;
        int start_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_members != -1 && _schema_VTY_members == start_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_members_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_members_576037_id));
          }
          if (_schema_VATT_members_576037_profileIndustryId != -1) {
            writer.WriteNameString("profileIndustryId");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_members_576037_profileIndustryId));
          }
          if (_schema_VATT_members_576037_registrationDate != -1) {
            writer.WriteNameString("registrationDate");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_members_576037_registrationDate, 0));
          }
          writer.WriteName("@va");
          v_val.va_1.json_printer(writer, _request, context.GraphAPI(), true);
          writer.WriteName("@va2");
          v_val.va2_1.json_printer(writer, _request, context.GraphAPI(), true);
          writer.WriteName("@label");
          v_val.label_1.json_printer(writer, _request, context.GraphAPI(), true);
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      void VertexMap_2(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_2 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        uint64_t src_registrationDate_uint64_t = 0;
        bool src_registrationDate_uint64_t_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_members) {
          if (srcVertex.GetAttr().IsValid()) {
            src_registrationDate_uint64_t = srcVertex.GetAttr().GetUInt(_schema_VATT_members_576037_registrationDate, 0);
            src_registrationDate_uint64_t_flag = true;
          }
        }
        V_VALUE src_val;
        // prepare message
        Delta_2 src_delta = Delta_2();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // @@cnt += s.registrationDate
        if (src_registrationDate_uint64_t_flag) {
          context.GlobalVariableAdd(0, SumAccum<int64_t > (src_registrationDate_uint64_t));
        }
        
        // s.@va += s.registrationDate
        if (src_registrationDate_uint64_t_flag) {
          src_delta.va_1 += src_registrationDate_uint64_t;
          src_delta.__GQUERY__hasChanged___576037va_1 = true;
          
        }
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (src_delta.__GQUERY__hasChanged___576037va_1) {
          l_val.va_1 += src_delta.va_1;
        } else if (src_delta.__GQUERY__set___576037va_1) {
          l_val.va_1 = src_delta.va_1;
        }
        context.Write(src, l_val_0, 0);
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void EdgeMap_3(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_3 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        uint64_t src_registrationDate_uint64_t = 0;
        bool src_registrationDate_uint64_t_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_members) {
          if (srcVertex.GetAttr().IsValid()) {
            src_registrationDate_uint64_t = srcVertex.GetAttr().GetUInt(_schema_VATT_members_576037_registrationDate, 0);
            src_registrationDate_uint64_t_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        Delta_3 tgt_delta = Delta_3();
        tgt_delta.__GQUERY__isTgt__576037 = true;
        // prepare message
        Delta_3 src_delta = Delta_3();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // s.@va += s.registrationDate
        if (src_registrationDate_uint64_t_flag) {
          src_delta.va_1 += src_registrationDate_uint64_t;
          src_delta.__GQUERY__hasChanged___576037va_1 = true;
          
        }
        context.Write(tgt, tgt_delta, 0);
        context.Write(src, src_delta, 0);
        context.Activate(tgt, 0);
        context.Activate(src, 0);
      }
      void Reduce_3(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_3 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_3>();
        string v_company_name_string = string();
        bool v_company_name_string_flag = false;
        
        //get v's attribute
        int v_typeIDVar = graphAPI->GetVertexType(v);
        if (v_typeIDVar == _schema_VTY_company) {
          if (vVertex.GetAttr().IsValid()) {
            v_company_name_string = vVertex.GetAttr().GetString(_schema_VATT_company_576037_company_name);
            v_company_name_string_flag = true;
          }
        }
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037va_1) {
          l_val.va_1 += delta.va_1;
        } else if (delta.__GQUERY__set___576037va_1) {
          l_val.va_1 = delta.va_1;
        }
        
        // @@cnt += 1
        if (delta.__GQUERY__isTgt__576037) {
          context.GlobalVariableAdd(0, SumAccum<int64_t > (1l));
        }
        if (delta.__GQUERY__isTgt__576037 && (v_company_name_string_flag && delta.__GQUERY__isTgt__576037 && (v_company_name_string == string("com1")))) {
          context.Activate(v, 0);
        }
        context.Write(v, l_val_0, 0);
      }
      void VertexMap_4(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_4 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        __GQUERY__VertexVal_ptr__576037 vptr_src_val;
        vptr_src_val.add(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        V_VALUE src_val(vptr_src_val);
        // prepare message
        Delta_4 src_delta = Delta_4();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // s.@label += "_sufix"
        src_delta.label_1 += string("_sufix");
        src_delta.__GQUERY__hasChanged___576037label_1 = true;
        
        // @@cnt += s.@va
        context.GlobalVariableAdd(0, SumAccum<int64_t > (src_val.va_1.data_));
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (src_delta.__GQUERY__hasChanged___576037label_1) {
          l_val.label_1 += src_delta.label_1;
        } else if (src_delta.__GQUERY__set___576037label_1) {
          l_val.label_1 = src_delta.label_1;
        }
        context.Write(src, l_val_0, 0);
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_5(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*srcVertex.GetValuePtr(0).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
          {
            auto __tmp_orderkey_1 = src_delta.__GQUERY__isSrc__576037? l_val.va_1.data_ : std::numeric_limits<int64_t>::max();
            context.GlobalVariableAdd(1, HeapAccum<orderby_heap_int_asc, orderby_heap_int_ascCompare<orderby_heap_int_asc> > (orderby_heap_int_asc(src, __tmp_orderkey_1)));
          }
        }
      }
      
      void EdgeMap_6(gpr::VertexEntity& srcVertex,
        gpr::VertexEntity& tgtVertex,
        gpr::EdgeEntity& edge,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef Delta_6 MESSAGE;
        
        VERTEX src = srcVertex.vid();
        VERTEX tgt = tgtVertex.vid();
        auto graphAPI = context.GraphAPI();
        // type filters
        if (_schema_VTY_company >= 0 && graphAPI->GetVertexType(tgt) != (unsigned)_schema_VTY_company)
          return;
        uint64_t src_registrationDate_uint64_t = 0;
        bool src_registrationDate_uint64_t_flag = false;
        string tgt_company_name_string = string();
        bool tgt_company_name_string_flag = false;
        
        //get tgt's attribute
        int tgt_typeIDVar = graphAPI->GetVertexType(tgt);
        if (tgt_typeIDVar == _schema_VTY_company) {
          if (tgtVertex.GetAttr().IsValid()) {
            tgt_company_name_string = tgtVertex.GetAttr().GetString(_schema_VATT_company_576037_company_name);
            tgt_company_name_string_flag = true;
          }
        }
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_members) {
          if (srcVertex.GetAttr().IsValid()) {
            src_registrationDate_uint64_t = srcVertex.GetAttr().GetUInt(_schema_VATT_members_576037_registrationDate, 0);
            src_registrationDate_uint64_t_flag = true;
          }
        }
        V_VALUE src_val;
        V_VALUE tgt_val;
        // prepare message
        Delta_6 src_delta = Delta_6();
        src_delta.__GQUERY__isSrc__576037 = true;
        
        // @@cnt += s.registrationDate
        if (src_registrationDate_uint64_t_flag) {
          context.GlobalVariableAdd(0, SumAccum<int64_t > (src_registrationDate_uint64_t));
        }
        
        // s.@va2 += t.company_name
        if (tgt_company_name_string_flag) {
          src_delta.va2_1 += tgt_company_name_string;
          src_delta.__GQUERY__hasChanged___576037va2_1 = true;
          
        }
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_6(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef Delta_6 MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<Delta_6>();
        
        __GQUERY__VertexVal_ptr__576037 lptr;
        __GQUERY__VertexVal_0__576037 l_val_0(*vVertex.GetValuePtr(1).GetRawPtr<__GQUERY__VertexVal_0__576037>());
        lptr.add(l_val_0);
        V_VALUE l_val(lptr);
        if (delta.__GQUERY__hasChanged___576037va2_1) {
          l_val.va2_1 += delta.va2_1;
        } else if (delta.__GQUERY__set___576037va2_1) {
          l_val.va2_1 = delta.va2_1;
        }
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
          {
            auto __tmp_orderkey_1 = delta.__GQUERY__isSrc__576037? l_val.va_1.data_ : std::numeric_limits<int64_t>::max();
            context.GlobalVariableAdd(2, HeapAccum<orderby_heap_int_asc, orderby_heap_int_ascCompare<orderby_heap_int_asc> > (orderby_heap_int_asc(v, __tmp_orderkey_1)));
          }
        }
        context.Write(v, l_val_0, 0);
      }
      
      ///gpr driver
      void run_impl () {
        /// worker manager
        gperun::WorkerManager manager(_serviceapi, &_request);
        if (!manager.Start("test_reduce")) {
          return false;
        }
        // for subquery, use the graphAPI from main query
        auto graphAPI = _graphAPI;
        if (!isQueryCalled) {
          // for normal query, create graph api
          graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
        }
        
        // create global variable
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable_k_(_k);
        gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_k_flag(k_flag);
        
        gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_cnt_1_;
        gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
        gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
        gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_vector_;
        gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_start_SIZE__;
        gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_start_hasOrder_;
        gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_start_vector_;
        gpelib4::SumVariable<HeapAccum<orderby_heap_int_asc, orderby_heap_int_ascCompare<orderby_heap_int_asc> > > __GQUERY_GV_Global_Variable_orderby_heap_int_asc_;
        
        {
          /*
          @@cnt_1 reinitiate
          */
          
          // @@cnt_1 reinitiate
          __GQUERY_GV_cnt_1_.Value().clear();
          
        }
        {
          /*
          start = { members .* };
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_1_";
          if (!manager.RunCMD(
            action_1_, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_1_ RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          
        }
        {
          /*
          start = select s from start : s accum @@cnt += s.registrationDate, s.@va += s.registrationDate;
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_2_map";
          if (!manager.RunCMD(
            action_2_map, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_2_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
          
        }
        {
          /*
          res = select t from start : s -(member_work_company : e)-> : t accum s.@va += s.registrationDate post-accum ( t ) @@cnt += 1 having t.company_name == "com1";
          */
          int64_t __vset_res_size = __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_3_map";
          if (!manager.RunCMD(
            action_3_map, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_res_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_3_map RunCMD failed.";
            return;
          }
          // reduce
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_3_reduce";
          if (!manager.RunCMD(
            action_3_reduce, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_res_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_3_reduce RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value() -= __vset_res_size;
          __GQUERY_GV_Global_Variable__vset_res_hasOrder_.Value() = false;
          
        }
        {
          /*
          start = select s from start : s accum s.@label += "_sufix", @@cnt += s.@va;
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_4_map";
          if (!manager.RunCMD(
            action_4_map, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_4_map RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
          
        }
        {
          /*
          start = select s from start : s order by s.@va asc limit k;
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().clear();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().resize(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_5_map";
          if (!manager.RunCMD(
            action_5_map, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_5_map RunCMD failed.";
            return;
          }
          // limit
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          //post action
          gpelib4::StateVariable<int64_t> __limit_(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master limit vset";
          if (!manager.RunCMD(
            action_5_post, 
            {&__limit_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_5_post RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = true;
          __GQUERY_GV_Global_Variable__vset_start_vector_.Value().clear();
          for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().end(); ++it) {
            __GQUERY_GV_Global_Variable__vset_start_vector_.Value().push_back(it->vid);
          }
          
        }
        {
          /*
          start = select s from start : s -(: e)-> company : t accum @@cnt += s.registrationDate, s.@va2 += t.company_name order by s.@va asc limit k;
          */
          int64_t __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().clear();
          // clear order by heap
          __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().resize(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
          // map
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_6_map";
          if (!manager.RunCMD(
            action_6_map, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag}, //input gvs
            {&__GQUERY_GV_cnt_1_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_6_map RunCMD failed.";
            return;
          }
          // limit
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __vset_start_size = __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value();
          //post action
          gpelib4::StateVariable<int64_t> __limit_(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master limit vset";
          if (!manager.RunCMD(
            action_6_post, 
            {&__limit_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_}, //input gvs
            {&__GQUERY_GV_Global_Variable__vset_start_SIZE__} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_6_post RunCMD failed.";
            return;
          }
          __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() -= __vset_start_size;
          __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = true;
          __GQUERY_GV_Global_Variable__vset_start_vector_.Value().clear();
          for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().end(); ++it) {
            __GQUERY_GV_Global_Variable__vset_start_vector_.Value().push_back(it->vid);
          }
          
        }
        {
          /*
          print @@cnt;
          */
          __GQUERY__local_writer->WriteStartObject();
          {
            gutil::JSONWriter& writer = *__GQUERY__local_writer;
            std::cout << "HERE print global" << std::endl;
            writer.WriteNameString("@@cnt");
            writer.WriteInt(__HF_GPR_retrieveGV<SumAccum<int64_t >  >(__GQUERY_GV_cnt_1_, true, "cnt_1").data_);
            
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        {
          /*
          print start;
          */
          __GQUERY__local_writer->WriteStartObject();
          gpelib4::BaseVariableObject* outputWriterGv;
          GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "master starts action_8_";
          if (__GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value()) {
            outputWriterGv = &__GQUERY_GV_SYS_Map;
          } else {
            outputWriterGv = &__GQUERY_GV_SYS_JSON;
          }
          if (!manager.RunCMD(
            action_8_, 
            {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag, &__GQUERY_GV_Global_Variable__vset_start_hasOrder_}, //input gvs
            {outputWriterGv} //output gvs
            )) {
              GUDFInfo((0<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "action_8_ RunCMD failed.";
            return;
          }
          if (__GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value()) {
            __GQUERY__local_writer->WriteName("start");
            __GQUERY__local_writer->WriteStartArray();
            for (auto it = __GQUERY_GV_Global_Variable__vset_start_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_start_vector_.Value().end(); ++it) {
              JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
              __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
              // add the mark vids inside json writer from each worker for translate vid use
              __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
            }
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY_GV_SYS_Map.Value().clear();
          } else {
            __GQUERY__local_writer->WriteName("start");
            __GQUERY__local_writer->WriteStartArray();
            __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
            __GQUERY__local_writer->WriteEndArray();
            __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
            __GQUERY_GV_SYS_JSON.Value().clear();
          }
          __GQUERY__local_writer->WriteEndObject();
          
        }
        
      }//end run_impl
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          run_impl();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void run_worker () {
        try {
          auto gpr = _serviceapi->CreateGPR(&_request);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          gpr::GPR_Container* __GQUERY__value_container0 = gpr->CreateValueContainer_RawPtr<__GQUERY__VertexVal_0__576037>();
          gutil::ScopedCleanUp sc_val0(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__value_container0, &_request));
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_res = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_start = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable_k_(_k);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_k_flag(k_flag);
          
          gpelib4::SumVariable<SumAccum<int64_t > > __GQUERY_GV_cnt_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_res_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_res_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_res_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_start_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_start_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_start_vector_;
          gpelib4::SumVariable<HeapAccum<orderby_heap_int_asc, orderby_heap_int_ascCompare<orderby_heap_int_asc> > > __GQUERY_GV_Global_Variable_orderby_heap_int_asc_;
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<Delta_3>();
          gutil::ScopedCleanUp sc_msg3(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request));
          gpr::GPR_Container* __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<Delta_4>();
          gutil::ScopedCleanUp sc_msg4(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request));
          gpr::GPR_Container* __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg5(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request));
          gpr::GPR_Container* __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
          gutil::ScopedCleanUp sc_msg6(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request));
          gperun::WorkerInstance* worker = _request.WorkerInstance();
          std::cout << "worker start" << std::endl;
          // waiting for each command to run
          while (worker->ReceiveNewCommand()) {
            std::cout << "worker get action " << worker->GetAction() << std::endl;
            if (_request.error_) {
              _request.WorkerInstance()->Response(
                gutil::error_t::E_WORKER_ACTION_CMD_FAILURE, "worker action failed",
                nullptr, false);
              continue;
            }
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker get new action";
            try {
              if (worker->GetAction() == action_1_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_1_";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                //run action
                __GQUERY__vSet_start->SetAllActiveFlag(false);
                __GQUERY__vSet_start->SetActiveFlagByType(_schema_VTY_members, true);
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_1_";
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_start_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_2_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_2_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<Delta_2>();
                sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::VertexMap_2, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {__GQUERY__value_container0},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_start, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_2_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_3_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_3_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_res_SIZE__) __GQUERY_GV_Global_Variable__vset_res_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<Delta_3>();
                sc_msg3 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                edgeFilterCtrl->EnableEdgeType(_schema_ETY_member_work_company);
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::EdgeMap_3, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container3},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_res_SIZE___output}//output gv
                );
                edgeAction->AddInputObject(this);
                
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container3}, {__GQUERY__vSet_target.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_3_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_res_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_res_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_3_reduce) {
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_res_SIZE__) __GQUERY_GV_Global_Variable__vset_res_SIZE___output;
                //run reduce action
                
                __GQUERY__vSet_res->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::Reduce_3, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container3,__GQUERY__value_container0},//input container(old v_value and delta)
                  {__GQUERY__value_container0},//output container(new v_value)
                  {__GQUERY__vSet_res.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_res_SIZE___output}//output gv
                );
                reduceAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_res_SIZE__.Value() = __GQUERY__vSet_res->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_res_SIZE___output.Value() = __GQUERY__vSet_res->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_res_SIZE___output});
                }
              }
              else if (worker->GetAction() == action_4_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_4_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container4 = gpr->CreateMsgContainer_RawPtr<Delta_4>();
                sc_msg4 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container4, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::VertexMap_4, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {__GQUERY__value_container0},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_start, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_4_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output});
                }
                __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = false;
              }
              else if (worker->GetAction() == action_5_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_5_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().clear();
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().resize(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container5 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
                sc_msg5 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container5, &_request)));
                auto vertexAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::VertexMap_5, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_Global_Variable__vset_start_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_}//output gv
                );
                vertexAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({vertexAction.get()});
                //assign the temp vset to the output vset directly
                std::swap(__GQUERY__vSet_start, __GQUERY__vSet_target);
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                //shuffle result
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_5_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_start_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_});
                }
                __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = true;
              }
              else if (worker->GetAction() == action_5_post) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_5_post";
                gpelib4::StateVariable<int64_t> __limit_;
                //deserialize limit and vset size
                worker->Deserialize({&__limit_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_});
                //limit vset size
                __GQUERY__vSet_start->SetAllActiveFlag(false);
                for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().end(); ++it) {
                  __GQUERY__vSet_start->SetActiveFlag(it->vid);
                }
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_start_SIZE__});
                }
              }
              else if (worker->GetAction() == action_6_map) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_6_map";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag});
                //declare output gvs
                decltype(__GQUERY_GV_cnt_1_) __GQUERY_GV_cnt_1__output;
                decltype(__GQUERY_GV_Global_Variable__vset_start_SIZE__) __GQUERY_GV_Global_Variable__vset_start_SIZE___output;
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().clear();
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().resize(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
                //run map action
                __GQUERY__vSet_target->Reset();
                __GQUERY__delta_container6 = gpr->CreateMsgContainer_RawPtr<Delta_6>();
                sc_msg6 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container6, &_request)));
                //edge filters
                auto edgeFilterCtrl = gpr->CreateTypeFilterController();
                edgeFilterCtrl->DisableAllEdgeTypes();
                enableAllEdgeTypesInGraph(edgeFilterCtrl.get());
                
                auto edgeAction = gpr->CreateEdgeAction(
                  EdgeActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::EdgeMap_6, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {},//input container(v_value)
                  {__GQUERY__delta_container6},//output container(delta)
                  {__GQUERY__vSet_target.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_}//output gv
                );
                edgeAction->AddInputObject(this);
                
                edgeAction->SetTypeFilterController(edgeFilterCtrl.get());
                
                gpr::RemoteTopology::Filter filter;
                filter.addTgtAttribute(
                  _schema_VTY_company, {(unsigned)_schema_VATT_company_576037_company_name}
                );
                edgeAction->AddFilter(&filter);
                if (__disable_filter_) edgeAction->DisableFilter();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "prepare topology starts";
                worker->PrepareTopology(edgeAction.get());
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "prepare topology finish";
                //run edge action
                gpr->Run({edgeAction.get()});
                //shuffle result
                worker->Shuffle({__GQUERY__delta_container6}, {__GQUERY__vSet_target.get()});
                //run vertex action
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().clear();
                // clear order by heap
                __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().resize(__HF_GPR_retrieveGV<int64_t >(__GQUERY_GV_Global_Variable_k_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_k_flag, true, "k_flag$"), "k"));
                __GQUERY__vSet_start->Reset();
                auto reduceAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::Reduce_6, this),//action function
                  __GQUERY__vSet_target.get(),//input bitset
                  {__GQUERY__delta_container6,__GQUERY__value_container0},//input container(old v_value and delta)
                  {__GQUERY__value_container0},//output container(new v_value)
                  {__GQUERY__vSet_start.get()},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag},//input gv
                  {&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_}//output gv
                );
                reduceAction->AddInputObject(this);
                
                //run vertex action
                gpr->Run({reduceAction.get()});
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                //update vset size
                __GQUERY_GV_Global_Variable__vset_start_SIZE___output.Value() = __GQUERY__vSet_start->count();
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_6_map";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_cnt_1__output, &__GQUERY_GV_Global_Variable__vset_start_SIZE___output, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_});
                }
                __GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value() = true;
              }
              else if (worker->GetAction() == action_6_post) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_6_post";
                gpelib4::StateVariable<int64_t> __limit_;
                //deserialize limit and vset size
                worker->Deserialize({&__limit_, &__GQUERY_GV_Global_Variable__vset_start_SIZE__, &__GQUERY_GV_Global_Variable_orderby_heap_int_asc_});
                //limit vset size
                __GQUERY__vSet_start->SetAllActiveFlag(false);
                for (auto it = __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().begin(); it != __GQUERY_GV_Global_Variable_orderby_heap_int_asc_.Value().end(); ++it) {
                  __GQUERY__vSet_start->SetActiveFlag(it->vid);
                }
                __GQUERY_GV_Global_Variable__vset_start_SIZE__.Value() = __GQUERY__vSet_start->count();
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({&__GQUERY_GV_Global_Variable__vset_start_SIZE__});
                }
              }
              else if (worker->GetAction() == action_8_) {
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker start action_8_";
                //deserialize input gvs
                worker->Deserialize({&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag, &__GQUERY_GV_Global_Variable__vset_start_hasOrder_});
                //run print action
                std::cout << "HERE print worker" << std::endl;
                gpelib4::BaseVariableObject* outputWriterGv;
                if (__GQUERY_GV_Global_Variable__vset_start_hasOrder_.Value()) {
                  __GQUERY_GV_SYS_Map.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_Map;
                } else {
                  __GQUERY_GV_SYS_JSON.Value().clear();
                  outputWriterGv = &__GQUERY_GV_SYS_JSON;
                }
                auto printAction = gpr->CreateVertexAction(
                  VertexActionFunc(&UDIMPL::poc_graph::GPR_test_reduce::Write_8, this),//action function
                  __GQUERY__vSet_start.get(),//input bitset
                  {__GQUERY__value_container0},//input container(v_value)
                  {},//output container(delta)
                  {},//result bitset
                  {&__GQUERY_GV_Global_Variable_k_, &__GQUERY_GV_Global_Variable_k_flag, &__GQUERY_GV_Global_Variable__vset_start_hasOrder_},//input gv
                  {outputWriterGv}//output gv
                );
                printAction->AddInputObject(this);
                gpr->Run({printAction.get()});
                GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|" << "worker finish action_8_";
                if (_request.error_) {
                  GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "action cmd encounter error";
                  worker->Response(gutil::error_t::E_WORKER_ACTION_CMD_FAILURE,
                    "worker action failed", nullptr, false);
                } else {
                  //go back to master
                  worker->Response({outputWriterGv});
                }
              }
              
              
              
            } catch(gutil::GsqlException& e) {
              worker->Response(e.errorcode(), e.msg());
            } catch (const boost::exception& bex) {
              GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " +
                boost::diagnostic_information(bex);
              worker->Response(8001, msg);
            } catch (const std::runtime_error& ex) {
              GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8002, msg);
            } catch (const std::exception& ex) {
              GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error " + std::string(ex.what());
              worker->Response(8003, msg);
            } catch (...) {
              GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
              std::string msg = "unexpected error";
              worker->Response(8999, msg);
            }
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run_worker
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      int64_t _k;
      bool k_flag = true;
      const int _schema_VTY_company = 0;
      const int _schema_VTY_members = 1;
      const int _schema_ETY_member_work_company = 0;
      int _schema_VATT_company_576037_company_name = -1;
      int _schema_VATT_members_576037_id = -1;
      int _schema_VATT_members_576037_profileIndustryId = -1;
      int _schema_VATT_members_576037_registrationDate = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_map = "action_3_map";
      const std::string action_3_reduce = "action_3_reduce";
      const std::string action_3_post = "action_3_post";
      const std::string action_4_map = "action_4_map";
      const std::string action_4_reduce = "action_4_reduce";
      const std::string action_4_post = "action_4_post";
      const std::string action_5_map = "action_5_map";
      const std::string action_5_reduce = "action_5_reduce";
      const std::string action_5_post = "action_5_post";
      const std::string action_6_map = "action_6_map";
      const std::string action_6_reduce = "action_6_reduce";
      const std::string action_6_post = "action_6_post";
      const std::string action_7_ = "action_7_";
      const std::string action_8_ = "action_8_";
      public:
        
        ///return vars
      };//end class GPR_test_reduce
    bool call_test_reduce(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::poc_graph::GPR_test_reduce gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_test_reduce_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      try {
        UDIMPL::poc_graph::GPR_test_reduce gpr(_request, serviceapi, true);
        gpr.run_worker();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    bool call_test_reduce(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 1) {
          HF_set_error(_request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        int64_t k = values[0]->GetIntInternal();
        
        UDIMPL::poc_graph::GPR_test_reduce gpr(graphAPI.get(), _request, serviceapi, graphupdates, k, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "test_reduce") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_test_reduce(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , int64_t k) {
      UDIMPL::poc_graph::GPR_test_reduce gpr(graphAPI, request, serviceapi, _graphupdates_, k, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_test_reduce(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum, int64_t k) {
      UDIMPL::poc_graph::GPR_test_reduce gpr(graphAPI, request, serviceapi, _graphupdates_, k, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace poc_graph
}//end namespace UDIMPL
