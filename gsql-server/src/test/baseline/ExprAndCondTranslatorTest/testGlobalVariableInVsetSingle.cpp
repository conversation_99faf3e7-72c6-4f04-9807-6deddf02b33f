/********** query start ***************
create query testGlobalVariableInVsetSingle(String vid, String vType) for graph ldbc_snb{
  Vertex myVt = to_vertex(vid, vType);
  vSet = {myVt};
  print vSet;
}
********** query end ***************/
#include "ldbc_snb-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace ldbc_snb {
    class GPR_testGlobalVariableInVsetSingle {
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testGlobalVariableInVsetSingle (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Comment_576037_creationDate = VTY_Comment_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Comment_576037_locationIP = VTY_Comment_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Comment_576037_browserUsed = VTY_Comment_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Comment_576037_content = VTY_Comment_attrMeta.GetAttributePosition("content", true);
        _schema_VATT_Comment_576037_length = VTY_Comment_attrMeta.GetAttributePosition("length", true);
        topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Post_576037_imageFile = VTY_Post_attrMeta.GetAttributePosition("imageFile", true);
        _schema_VATT_Post_576037_creationDate = VTY_Post_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Post_576037_locationIP = VTY_Post_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Post_576037_browserUsed = VTY_Post_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Post_576037_lang = VTY_Post_attrMeta.GetAttributePosition("lang", true);
        _schema_VATT_Post_576037_content = VTY_Post_attrMeta.GetAttributePosition("content", true);
        _schema_VATT_Post_576037_length = VTY_Post_attrMeta.GetAttributePosition("length", true);
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
        _schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
        _schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_City_576037_name = VTY_City_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_City_576037_url = VTY_City_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
        _schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Country_576037_name = VTY_Country_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Country_576037_url = VTY_Country_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
        _schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Continent_576037_name = VTY_Continent_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Continent_576037_url = VTY_Continent_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
        _schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
        _schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
        _schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
        _schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
        if (request.jsoptions_.isMember("vid")) {
          _vid = request.jsoptions_["vid"][0].asString();
          vid_flag = true;
        } else {
          // parameter is not given (null case)
          _vid = string();
          vid_flag = false;
        }
        if (request.jsoptions_.isMember("vType")) {
          _vType = request.jsoptions_["vType"][0].asString();
          vType_flag = true;
        } else {
          // parameter is not given (null case)
          _vType = string();
          vType_flag = false;
        }
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      //query calling query constructor
      GPR_testGlobalVariableInVsetSingle (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, string vid, string vType, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Comment_576037_creationDate = VTY_Comment_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Comment_576037_locationIP = VTY_Comment_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Comment_576037_browserUsed = VTY_Comment_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Comment_576037_content = VTY_Comment_attrMeta.GetAttributePosition("content", true);
        _schema_VATT_Comment_576037_length = VTY_Comment_attrMeta.GetAttributePosition("length", true);
        topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
        _schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Post_576037_imageFile = VTY_Post_attrMeta.GetAttributePosition("imageFile", true);
        _schema_VATT_Post_576037_creationDate = VTY_Post_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Post_576037_locationIP = VTY_Post_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Post_576037_browserUsed = VTY_Post_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Post_576037_lang = VTY_Post_attrMeta.GetAttributePosition("lang", true);
        _schema_VATT_Post_576037_content = VTY_Post_attrMeta.GetAttributePosition("content", true);
        _schema_VATT_Post_576037_length = VTY_Post_attrMeta.GetAttributePosition("length", true);
        topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
        _schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
        _schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
        _schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_City_576037_name = VTY_City_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_City_576037_url = VTY_City_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
        _schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Country_576037_name = VTY_Country_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Country_576037_url = VTY_Country_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
        _schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Continent_576037_name = VTY_Continent_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Continent_576037_url = VTY_Continent_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
        _schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
        _schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
        topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
        _schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
        _schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
        _schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
        _schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
        _schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
        _schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
        _schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
        _schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
        topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
        _schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
        topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
        _schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
        _schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
        _vid = vid;
        vid_flag = true;
        _vType = vType;
        vType_flag = true;
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
      }
      
      ///class destructor
      ~GPR_testGlobalVariableInVsetSingle () {}
      
      ///vertex actions for write
      void Write_2(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(4);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int vSet_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_Comment != -1 && _schema_VTY_Comment == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Comment_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Comment_576037_id, 0));
          }
          if (_schema_VATT_Comment_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Comment_576037_creationDate, 0)));
          }
          if (_schema_VATT_Comment_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Comment_576037_locationIP));
          }
          if (_schema_VATT_Comment_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Comment_576037_browserUsed));
          }
          if (_schema_VATT_Comment_576037_content != -1) {
            writer.WriteNameString("content");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Comment_576037_content));
          }
          if (_schema_VATT_Comment_576037_length != -1) {
            writer.WriteNameString("length");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Comment_576037_length, 0));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Company != -1 && _schema_VTY_Company == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Company_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Company_576037_id, 0));
          }
          if (_schema_VATT_Company_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_name));
          }
          if (_schema_VATT_Company_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Company_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Continent != -1 && _schema_VTY_Continent == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Continent_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Continent_576037_id, 0));
          }
          if (_schema_VATT_Continent_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Continent_576037_name));
          }
          if (_schema_VATT_Continent_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Continent_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_University != -1 && _schema_VTY_University == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_University_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_University_576037_id, 0));
          }
          if (_schema_VATT_University_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_name));
          }
          if (_schema_VATT_University_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_University_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Post != -1 && _schema_VTY_Post == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Post_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Post_576037_id, 0));
          }
          if (_schema_VATT_Post_576037_imageFile != -1) {
            writer.WriteNameString("imageFile");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Post_576037_imageFile));
          }
          if (_schema_VATT_Post_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Post_576037_creationDate, 0)));
          }
          if (_schema_VATT_Post_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Post_576037_locationIP));
          }
          if (_schema_VATT_Post_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Post_576037_browserUsed));
          }
          if (_schema_VATT_Post_576037_lang != -1) {
            writer.WriteNameString("lang");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Post_576037_lang));
          }
          if (_schema_VATT_Post_576037_content != -1) {
            writer.WriteNameString("content");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Post_576037_content));
          }
          if (_schema_VATT_Post_576037_length != -1) {
            writer.WriteNameString("length");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Post_576037_length, 0));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Country != -1 && _schema_VTY_Country == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Country_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Country_576037_id, 0));
          }
          if (_schema_VATT_Country_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Country_576037_name));
          }
          if (_schema_VATT_Country_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Country_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_City != -1 && _schema_VTY_City == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_City_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_City_576037_id, 0));
          }
          if (_schema_VATT_City_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_City_576037_name));
          }
          if (_schema_VATT_City_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_City_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Tag != -1 && _schema_VTY_Tag == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Tag_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Tag_576037_id, 0));
          }
          if (_schema_VATT_Tag_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Tag_576037_name));
          }
          if (_schema_VATT_Tag_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Tag_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_TagClass != -1 && _schema_VTY_TagClass == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_TagClass_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_TagClass_576037_id, 0));
          }
          if (_schema_VATT_TagClass_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_TagClass_576037_name));
          }
          if (_schema_VATT_TagClass_576037_url != -1) {
            writer.WriteNameString("url");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_TagClass_576037_url));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Person != -1 && _schema_VTY_Person == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Person_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Person_576037_id, 0));
          }
          if (_schema_VATT_Person_576037_firstName != -1) {
            writer.WriteNameString("firstName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_firstName));
          }
          if (_schema_VATT_Person_576037_lastName != -1) {
            writer.WriteNameString("lastName");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_lastName));
          }
          if (_schema_VATT_Person_576037_gender != -1) {
            writer.WriteNameString("gender");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_gender));
          }
          if (_schema_VATT_Person_576037_birthday != -1) {
            writer.WriteNameString("birthday");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_birthday, 0)));
          }
          if (_schema_VATT_Person_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Person_576037_creationDate, 0)));
          }
          if (_schema_VATT_Person_576037_locationIP != -1) {
            writer.WriteNameString("locationIP");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_locationIP));
          }
          if (_schema_VATT_Person_576037_browserUsed != -1) {
            writer.WriteNameString("browserUsed");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Person_576037_browserUsed));
          }
          if (_schema_VATT_Person_576037_speaks != -1) {
            writer.WriteNameString("speaks");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_Person_576037_email != -1) {
            writer.WriteNameString("email");
            (HF_get_attr_setstring_V(&vVertex.GetAttr(), _schema_VATT_Person_576037_email)).json_printer(writer, _request, graphAPI, true);
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        else if(_schema_VTY_Forum != -1 && _schema_VTY_Forum == vSet_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_Forum_576037_id != -1) {
            writer.WriteNameString("id");
            writer.WriteUnsignedInt(vVertex.GetAttr().GetUInt(_schema_VATT_Forum_576037_id, 0));
          }
          if (_schema_VATT_Forum_576037_title != -1) {
            writer.WriteNameString("title");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_Forum_576037_title));
          }
          if (_schema_VATT_Forum_576037_creationDate != -1) {
            writer.WriteNameString("creationDate");
            writer.WriteString((std::string)DATETIME(vVertex.GetAttr().GetInt(_schema_VATT_Forum_576037_creationDate, 0)));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      
      ///gpr driver
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          singleGprMainFlow();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void singleGprMainFlow () {
        try {
          gshared_ptr<gpr::GPR> gpr = _serviceapi->CreateGPR(&_request, true);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vSet = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_vid_(_vid);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_vid_flag(vid_flag);
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_vType_(_vType);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_vType_flag(vType_flag);
          gpelib4::StateVariable<VERTEX> __GQUERY_GV_Global_Variable_myVt_1_;
          
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vSet_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vSet_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vSet_vector_;
          {
            /*
            vertex myVt = to_vertex ( vid, vType );
            */
            
            // vertex myVt = to_vertex ( vid, vType );
            __GQUERY_GV_Global_Variable_myVt_1_.Value() = GSQL_UTIL::ToVertex(&_request, _serviceapi, __GQUERY__vts_, __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_vid_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_vid_flag, true, "vid_flag$"), "vid"), __HF_GPR_retrieveGV<string >(__GQUERY_GV_Global_Variable_vType_, __HF_GPR_retrieveGV<bool >(__GQUERY_GV_Global_Variable_vType_flag, true, "vType_flag$"), "vType"));
            
          }
          {
            /*
            vSet = { myVt };
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|" << "starts action_1_";
            //declare output gvs
            //run action
            __GQUERY__vSet_vSet->SetAllActiveFlag(false);
            ActiveVertex(__GQUERY__vSet_vSet, __HF_GPR_retrieveGV<VERTEX >(__GQUERY_GV_Global_Variable_myVt_1_, true, "myVt_1"));
            __GQUERY_GV_Global_Variable__vset_vSet_SIZE__.Value() = __GQUERY__vSet_vSet->count();
            
          }
          {
            /*
            print vSet;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|" << "starts action_2_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_vSet_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::ldbc_snb::GPR_testGlobalVariableInVsetSingle::Write_2, this),//action function
                __GQUERY__vSet_vSet.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable_vid_, &__GQUERY_GV_Global_Variable_vid_flag, &__GQUERY_GV_Global_Variable_vType_, &__GQUERY_GV_Global_Variable_vType_flag, &__GQUERY_GV_Global_Variable__vset_vSet_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_vSet_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("vSet");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_vSet_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_vSet_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("vSet");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end singleGprMainFlow
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      inline static SetAccum<string > HF_get_attr_setstring_V(V_ATTR* attr, int index) {
        SetAccum<string >  result;
        auto reader = attr->GetStringValueReader(index);
        while (reader.MoveNext()) {
          result += reader.value_;
        }
        return result;
      }
      
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      string _vid;
      bool vid_flag = true;
      string _vType;
      bool vType_flag = true;
      const int _schema_VTY_Comment = 0;
      const int _schema_VTY_Post = 1;
      const int _schema_VTY_Company = 2;
      const int _schema_VTY_University = 3;
      const int _schema_VTY_City = 4;
      const int _schema_VTY_Country = 5;
      const int _schema_VTY_Continent = 6;
      const int _schema_VTY_Forum = 7;
      const int _schema_VTY_Person = 8;
      const int _schema_VTY_Tag = 9;
      const int _schema_VTY_TagClass = 10;
      int _schema_VATT_Comment_576037_id = -1;
      int _schema_VATT_Comment_576037_creationDate = -1;
      int _schema_VATT_Comment_576037_locationIP = -1;
      int _schema_VATT_Comment_576037_browserUsed = -1;
      int _schema_VATT_Comment_576037_content = -1;
      int _schema_VATT_Comment_576037_length = -1;
      int _schema_VATT_Post_576037_id = -1;
      int _schema_VATT_Post_576037_imageFile = -1;
      int _schema_VATT_Post_576037_creationDate = -1;
      int _schema_VATT_Post_576037_locationIP = -1;
      int _schema_VATT_Post_576037_browserUsed = -1;
      int _schema_VATT_Post_576037_lang = -1;
      int _schema_VATT_Post_576037_content = -1;
      int _schema_VATT_Post_576037_length = -1;
      int _schema_VATT_Company_576037_id = -1;
      int _schema_VATT_Company_576037_name = -1;
      int _schema_VATT_Company_576037_url = -1;
      int _schema_VATT_University_576037_id = -1;
      int _schema_VATT_University_576037_name = -1;
      int _schema_VATT_University_576037_url = -1;
      int _schema_VATT_City_576037_id = -1;
      int _schema_VATT_City_576037_name = -1;
      int _schema_VATT_City_576037_url = -1;
      int _schema_VATT_Country_576037_id = -1;
      int _schema_VATT_Country_576037_name = -1;
      int _schema_VATT_Country_576037_url = -1;
      int _schema_VATT_Continent_576037_id = -1;
      int _schema_VATT_Continent_576037_name = -1;
      int _schema_VATT_Continent_576037_url = -1;
      int _schema_VATT_Forum_576037_id = -1;
      int _schema_VATT_Forum_576037_title = -1;
      int _schema_VATT_Forum_576037_creationDate = -1;
      int _schema_VATT_Person_576037_id = -1;
      int _schema_VATT_Person_576037_firstName = -1;
      int _schema_VATT_Person_576037_lastName = -1;
      int _schema_VATT_Person_576037_gender = -1;
      int _schema_VATT_Person_576037_birthday = -1;
      int _schema_VATT_Person_576037_creationDate = -1;
      int _schema_VATT_Person_576037_locationIP = -1;
      int _schema_VATT_Person_576037_browserUsed = -1;
      int _schema_VATT_Person_576037_speaks = -1;
      int _schema_VATT_Person_576037_email = -1;
      int _schema_VATT_Tag_576037_id = -1;
      int _schema_VATT_Tag_576037_name = -1;
      int _schema_VATT_Tag_576037_url = -1;
      int _schema_VATT_TagClass_576037_id = -1;
      int _schema_VATT_TagClass_576037_name = -1;
      int _schema_VATT_TagClass_576037_url = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_ = "action_2_";
      public:
        
        ///return vars
      };//end class GPR_testGlobalVariableInVsetSingle
    bool call_testGlobalVariableInVsetSingle(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::ldbc_snb::GPR_testGlobalVariableInVsetSingle gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_testGlobalVariableInVsetSingle_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      //single gpr doing nothing
    }
    bool call_testGlobalVariableInVsetSingle(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 2) {
          HF_set_error(_request, "Invalid parameter size: 2|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        string vid = values[0]->GetStringInternal();
        string vType = values[1]->GetStringInternal();
        
        UDIMPL::ldbc_snb::GPR_testGlobalVariableInVsetSingle gpr(graphAPI.get(), _request, serviceapi, graphupdates, vid, vType, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetSingle") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testGlobalVariableInVsetSingle(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string vid, string vType) {
      UDIMPL::ldbc_snb::GPR_testGlobalVariableInVsetSingle gpr(graphAPI, request, serviceapi, _graphupdates_, vid, vType, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testGlobalVariableInVsetSingle(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum, string vid, string vType) {
      UDIMPL::ldbc_snb::GPR_testGlobalVariableInVsetSingle gpr(graphAPI, request, serviceapi, _graphupdates_, vid, vType, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace ldbc_snb
}//end namespace UDIMPL
