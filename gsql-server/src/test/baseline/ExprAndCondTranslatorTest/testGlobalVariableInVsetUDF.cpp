/********** query start ***************
create query testGlobalVariableInVsetUDF(String vid, String vType) for graph ldbc_snb{
  Vertex myVt = to_vertex(vid, vType);
  vSet = {myVt};
  print vSet;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_testGlobalVariableInVsetUDF :public gpelib4::BaseUDF {
struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_testGlobalVariableInVsetUDF (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Comment_576037_creationDate = VTY_Comment_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Comment_576037_locationIP = VTY_Comment_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Comment_576037_browserUsed = VTY_Comment_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Comment_576037_content = VTY_Comment_attrMeta.GetAttributePosition("content", true);
_schema_VATT_Comment_576037_length = VTY_Comment_attrMeta.GetAttributePosition("length", true);
topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Post_576037_imageFile = VTY_Post_attrMeta.GetAttributePosition("imageFile", true);
_schema_VATT_Post_576037_creationDate = VTY_Post_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Post_576037_locationIP = VTY_Post_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Post_576037_browserUsed = VTY_Post_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Post_576037_lang = VTY_Post_attrMeta.GetAttributePosition("lang", true);
_schema_VATT_Post_576037_content = VTY_Post_attrMeta.GetAttributePosition("content", true);
_schema_VATT_Post_576037_length = VTY_Post_attrMeta.GetAttributePosition("length", true);
topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
_schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
_schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
_schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
_schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
_schema_VATT_City_576037_name = VTY_City_attrMeta.GetAttributePosition("name", true);
_schema_VATT_City_576037_url = VTY_City_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
_schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Country_576037_name = VTY_Country_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Country_576037_url = VTY_Country_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
_schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Continent_576037_name = VTY_Continent_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Continent_576037_url = VTY_Continent_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
_schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
_schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
_schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
_schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
_schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
_schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
_schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
_schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
_schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
_schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
      if (request.jsoptions_.isMember("vid")) {
        _vid = request.jsoptions_["vid"][0].asString();
        vid_flag = true;
      } else {
        // parameter is not given (null case)
        _vid = string();
        vid_flag = false;
      }
      if (request.jsoptions_.isMember("vType")) {
        _vType = request.jsoptions_["vType"][0].asString();
        vType_flag = true;
      } else {
        // parameter is not given (null case)
        _vType = string();
        vType_flag = false;
      }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_testGlobalVariableInVsetUDF (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string vid, string vType, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Comment_attrMeta = meta->GetVertexType(0).attributes_;
_schema_VATT_Comment_576037_id = VTY_Comment_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Comment_576037_creationDate = VTY_Comment_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Comment_576037_locationIP = VTY_Comment_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Comment_576037_browserUsed = VTY_Comment_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Comment_576037_content = VTY_Comment_attrMeta.GetAttributePosition("content", true);
_schema_VATT_Comment_576037_length = VTY_Comment_attrMeta.GetAttributePosition("length", true);
topology4::AttributesMeta& VTY_Post_attrMeta = meta->GetVertexType(1).attributes_;
_schema_VATT_Post_576037_id = VTY_Post_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Post_576037_imageFile = VTY_Post_attrMeta.GetAttributePosition("imageFile", true);
_schema_VATT_Post_576037_creationDate = VTY_Post_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Post_576037_locationIP = VTY_Post_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Post_576037_browserUsed = VTY_Post_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Post_576037_lang = VTY_Post_attrMeta.GetAttributePosition("lang", true);
_schema_VATT_Post_576037_content = VTY_Post_attrMeta.GetAttributePosition("content", true);
_schema_VATT_Post_576037_length = VTY_Post_attrMeta.GetAttributePosition("length", true);
topology4::AttributesMeta& VTY_Company_attrMeta = meta->GetVertexType(2).attributes_;
_schema_VATT_Company_576037_id = VTY_Company_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Company_576037_name = VTY_Company_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Company_576037_url = VTY_Company_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_University_attrMeta = meta->GetVertexType(3).attributes_;
_schema_VATT_University_576037_id = VTY_University_attrMeta.GetAttributePosition("id", true);
_schema_VATT_University_576037_name = VTY_University_attrMeta.GetAttributePosition("name", true);
_schema_VATT_University_576037_url = VTY_University_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_City_attrMeta = meta->GetVertexType(4).attributes_;
_schema_VATT_City_576037_id = VTY_City_attrMeta.GetAttributePosition("id", true);
_schema_VATT_City_576037_name = VTY_City_attrMeta.GetAttributePosition("name", true);
_schema_VATT_City_576037_url = VTY_City_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Country_attrMeta = meta->GetVertexType(5).attributes_;
_schema_VATT_Country_576037_id = VTY_Country_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Country_576037_name = VTY_Country_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Country_576037_url = VTY_Country_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Continent_attrMeta = meta->GetVertexType(6).attributes_;
_schema_VATT_Continent_576037_id = VTY_Continent_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Continent_576037_name = VTY_Continent_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Continent_576037_url = VTY_Continent_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_Forum_attrMeta = meta->GetVertexType(7).attributes_;
_schema_VATT_Forum_576037_id = VTY_Forum_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Forum_576037_title = VTY_Forum_attrMeta.GetAttributePosition("title", true);
_schema_VATT_Forum_576037_creationDate = VTY_Forum_attrMeta.GetAttributePosition("creationDate", true);
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Person_576037_firstName = VTY_Person_attrMeta.GetAttributePosition("firstName", true);
_schema_VATT_Person_576037_lastName = VTY_Person_attrMeta.GetAttributePosition("lastName", true);
_schema_VATT_Person_576037_gender = VTY_Person_attrMeta.GetAttributePosition("gender", true);
_schema_VATT_Person_576037_birthday = VTY_Person_attrMeta.GetAttributePosition("birthday", true);
_schema_VATT_Person_576037_creationDate = VTY_Person_attrMeta.GetAttributePosition("creationDate", true);
_schema_VATT_Person_576037_locationIP = VTY_Person_attrMeta.GetAttributePosition("locationIP", true);
_schema_VATT_Person_576037_browserUsed = VTY_Person_attrMeta.GetAttributePosition("browserUsed", true);
_schema_VATT_Person_576037_speaks = VTY_Person_attrMeta.GetAttributePosition("speaks", true);
_schema_VATT_Person_576037_email = VTY_Person_attrMeta.GetAttributePosition("email", true);
topology4::AttributesMeta& VTY_Tag_attrMeta = meta->GetVertexType(9).attributes_;
_schema_VATT_Tag_576037_id = VTY_Tag_attrMeta.GetAttributePosition("id", true);
_schema_VATT_Tag_576037_name = VTY_Tag_attrMeta.GetAttributePosition("name", true);
_schema_VATT_Tag_576037_url = VTY_Tag_attrMeta.GetAttributePosition("url", true);
topology4::AttributesMeta& VTY_TagClass_attrMeta = meta->GetVertexType(10).attributes_;
_schema_VATT_TagClass_576037_id = VTY_TagClass_attrMeta.GetAttributePosition("id", true);
_schema_VATT_TagClass_576037_name = VTY_TagClass_attrMeta.GetAttributePosition("name", true);
_schema_VATT_TagClass_576037_url = VTY_TagClass_attrMeta.GetAttributePosition("url", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_vid = vid;
vid_flag = true;
_vType = vType;
vType_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_testGlobalVariableInVsetUDF () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_vid, GV_SYS_vid_flag, GV_PARAM_vType, GV_SYS_vType_flag, GV_GV_myVt_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_vSet_SIZE, GV_SYS_vSet_ORDERBY, GV_SYS_vSet_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_vSet_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vSet_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vSet_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_vid, new  gpelib4::BroadcastVariable<string> (_vid));
   gvs->Register (GV_SYS_vid_flag, new  gpelib4::BroadcastVariable<bool> (vid_flag));
   gvs->Register (GV_PARAM_vType, new  gpelib4::BroadcastVariable<string> (_vType));
   gvs->Register (GV_SYS_vType_flag, new  gpelib4::BroadcastVariable<bool> (vType_flag));

   // global variables
   gvs->Register (GV_GV_myVt_1, new gpelib4::StateVariable<VERTEX> (VERTEX()));

   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_testGlobalVariableInVsetUDF::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}


void Write_2_vSet (gvector<gutil::GOutputStream*>&   ostream,
              const VERTEX& v,
              V_ATTR*           v_attr,
              const V_VALUE&     v_val,
              gpelib4::GlobalVariableContext* context) {
  GCOUT(Verbose_FrameworkHigh) << "[UDF_testGlobalVariableInVsetUDF INFO] " << "Enter function Write_2_vSet v: " << v << std::endl;

   //attributes' local var declaration


  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  gutil::JSONWriter& writer = *__GQUERY__local_writer;
  int vSet_typeIDVar =  context->GraphAPI()->GetVertexType(v);

    if (_schema_VTY_Comment != -1 && vSet_typeIDVar == _schema_VTY_Comment)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Comment_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Comment_576037_id, 0));
         }
         if (_schema_VATT_Comment_576037_creationDate != -1) {
         writer.WriteNameString("creationDate");
         writer.WriteString((std::string)DATETIME(v_attr->GetInt(_schema_VATT_Comment_576037_creationDate, 0)));
         }
         if (_schema_VATT_Comment_576037_locationIP != -1) {
         writer.WriteNameString("locationIP");
         writer.WriteString(v_attr->GetString(_schema_VATT_Comment_576037_locationIP));
         }
         if (_schema_VATT_Comment_576037_browserUsed != -1) {
         writer.WriteNameString("browserUsed");
         writer.WriteString(v_attr->GetString(_schema_VATT_Comment_576037_browserUsed));
         }
         if (_schema_VATT_Comment_576037_content != -1) {
         writer.WriteNameString("content");
         writer.WriteString(v_attr->GetString(_schema_VATT_Comment_576037_content));
         }
         if (_schema_VATT_Comment_576037_length != -1) {
         writer.WriteNameString("length");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Comment_576037_length, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Company != -1 && vSet_typeIDVar == _schema_VTY_Company)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Company_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Company_576037_id, 0));
         }
         if (_schema_VATT_Company_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_Company_576037_name));
         }
         if (_schema_VATT_Company_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_Company_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Continent != -1 && vSet_typeIDVar == _schema_VTY_Continent)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Continent_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Continent_576037_id, 0));
         }
         if (_schema_VATT_Continent_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_Continent_576037_name));
         }
         if (_schema_VATT_Continent_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_Continent_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_University != -1 && vSet_typeIDVar == _schema_VTY_University)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_University_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_University_576037_id, 0));
         }
         if (_schema_VATT_University_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_University_576037_name));
         }
         if (_schema_VATT_University_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_University_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Post != -1 && vSet_typeIDVar == _schema_VTY_Post)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Post_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Post_576037_id, 0));
         }
         if (_schema_VATT_Post_576037_imageFile != -1) {
         writer.WriteNameString("imageFile");
         writer.WriteString(v_attr->GetString(_schema_VATT_Post_576037_imageFile));
         }
         if (_schema_VATT_Post_576037_creationDate != -1) {
         writer.WriteNameString("creationDate");
         writer.WriteString((std::string)DATETIME(v_attr->GetInt(_schema_VATT_Post_576037_creationDate, 0)));
         }
         if (_schema_VATT_Post_576037_locationIP != -1) {
         writer.WriteNameString("locationIP");
         writer.WriteString(v_attr->GetString(_schema_VATT_Post_576037_locationIP));
         }
         if (_schema_VATT_Post_576037_browserUsed != -1) {
         writer.WriteNameString("browserUsed");
         writer.WriteString(v_attr->GetString(_schema_VATT_Post_576037_browserUsed));
         }
         if (_schema_VATT_Post_576037_lang != -1) {
         writer.WriteNameString("lang");
         writer.WriteString(v_attr->GetString(_schema_VATT_Post_576037_lang));
         }
         if (_schema_VATT_Post_576037_content != -1) {
         writer.WriteNameString("content");
         writer.WriteString(v_attr->GetString(_schema_VATT_Post_576037_content));
         }
         if (_schema_VATT_Post_576037_length != -1) {
         writer.WriteNameString("length");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Post_576037_length, 0));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Country != -1 && vSet_typeIDVar == _schema_VTY_Country)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Country_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Country_576037_id, 0));
         }
         if (_schema_VATT_Country_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_Country_576037_name));
         }
         if (_schema_VATT_Country_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_Country_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_City != -1 && vSet_typeIDVar == _schema_VTY_City)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_City_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_City_576037_id, 0));
         }
         if (_schema_VATT_City_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_City_576037_name));
         }
         if (_schema_VATT_City_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_City_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Tag != -1 && vSet_typeIDVar == _schema_VTY_Tag)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Tag_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Tag_576037_id, 0));
         }
         if (_schema_VATT_Tag_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_Tag_576037_name));
         }
         if (_schema_VATT_Tag_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_Tag_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_TagClass != -1 && vSet_typeIDVar == _schema_VTY_TagClass)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_TagClass_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_TagClass_576037_id, 0));
         }
         if (_schema_VATT_TagClass_576037_name != -1) {
         writer.WriteNameString("name");
         writer.WriteString(v_attr->GetString(_schema_VATT_TagClass_576037_name));
         }
         if (_schema_VATT_TagClass_576037_url != -1) {
         writer.WriteNameString("url");
         writer.WriteString(v_attr->GetString(_schema_VATT_TagClass_576037_url));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Person != -1 && vSet_typeIDVar == _schema_VTY_Person)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Person_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Person_576037_id, 0));
         }
         if (_schema_VATT_Person_576037_firstName != -1) {
         writer.WriteNameString("firstName");
         writer.WriteString(v_attr->GetString(_schema_VATT_Person_576037_firstName));
         }
         if (_schema_VATT_Person_576037_lastName != -1) {
         writer.WriteNameString("lastName");
         writer.WriteString(v_attr->GetString(_schema_VATT_Person_576037_lastName));
         }
         if (_schema_VATT_Person_576037_gender != -1) {
         writer.WriteNameString("gender");
         writer.WriteString(v_attr->GetString(_schema_VATT_Person_576037_gender));
         }
         if (_schema_VATT_Person_576037_birthday != -1) {
         writer.WriteNameString("birthday");
         writer.WriteString((std::string)DATETIME(v_attr->GetInt(_schema_VATT_Person_576037_birthday, 0)));
         }
         if (_schema_VATT_Person_576037_creationDate != -1) {
         writer.WriteNameString("creationDate");
         writer.WriteString((std::string)DATETIME(v_attr->GetInt(_schema_VATT_Person_576037_creationDate, 0)));
         }
         if (_schema_VATT_Person_576037_locationIP != -1) {
         writer.WriteNameString("locationIP");
         writer.WriteString(v_attr->GetString(_schema_VATT_Person_576037_locationIP));
         }
         if (_schema_VATT_Person_576037_browserUsed != -1) {
         writer.WriteNameString("browserUsed");
         writer.WriteString(v_attr->GetString(_schema_VATT_Person_576037_browserUsed));
         }
         if (_schema_VATT_Person_576037_speaks != -1) {
         writer.WriteNameString("speaks");
         (HF_get_attr_setstring_V(v_attr, _schema_VATT_Person_576037_speaks)).json_printer(writer, _request, context->GraphAPI(), true);
         }
         if (_schema_VATT_Person_576037_email != -1) {
         writer.WriteNameString("email");
         (HF_get_attr_setstring_V(v_attr, _schema_VATT_Person_576037_email)).json_printer(writer, _request, context->GraphAPI(), true);
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    } else if(_schema_VTY_Forum != -1 && vSet_typeIDVar == _schema_VTY_Forum)  {
         writer.WriteStartObject();
         writer.WriteNameString("v_id");
         (v).json_printer(writer, _request, context->GraphAPI(), true);
         
         writer.WriteNameString("v_type");
         writer.WriteString(context->GraphAPI()->GetVertexTypeName(v));
         
         writer.WriteName ("attributes");
         writer.WriteStartObject ();
         if (_schema_VATT_Forum_576037_id != -1) {
         writer.WriteNameString("id");
         writer.WriteUnsignedInt(v_attr->GetUInt(_schema_VATT_Forum_576037_id, 0));
         }
         if (_schema_VATT_Forum_576037_title != -1) {
         writer.WriteNameString("title");
         writer.WriteString(v_attr->GetString(_schema_VATT_Forum_576037_title));
         }
         if (_schema_VATT_Forum_576037_creationDate != -1) {
         writer.WriteNameString("creationDate");
         writer.WriteString((std::string)DATETIME(v_attr->GetInt(_schema_VATT_Forum_576037_creationDate, 0)));
         }
         writer.WriteEndObject ();
         writer.WriteEndObject ();
         
    }
  GCOUT(Verbose_FrameworkHigh) << "[UDF_testGlobalVariableInVsetUDF INFO] " << "Exit function Write_2_vSet v: " << v << std::endl;
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}


void (UDF_testGlobalVariableInVsetUDF::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_testGlobalVariableInVsetUDF::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_testGlobalVariableInVsetUDF INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("testGlobalVariableInVsetUDF");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_testGlobalVariableInVsetUDF DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 context->GlobalVariable_GetValue<VERTEX> (GV_GV_myVt_1)=( HF_toVertexFromUidType(context, ( HF_retrieve_param_856409387<string>(context, GV_PARAM_vid, GV_SYS_vid_flag, "vid")), ( HF_retrieve_param_856409387<string>(context, GV_PARAM_vType, GV_SYS_vType_flag, "vType"))));
                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

{
  const VERTEX& _activate_v = context->GlobalVariable_GetValue<VERTEX> (GV_GV_myVt_1);
  if (_activate_v == VERTEX(-1)) {
    //give error msg if not given yet
    if (!_request.error_) {
      std::string msg("system error: trying to activate invalid vid.");
      HF_set_error(_request, msg, true);
    }
    context->Abort();
    return;
  } else {
    context->SetActiveFlag (_activate_v);
  }
}

                 PC = 2;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vSet";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vSet_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(17L, "vSet", 3);
                     timer_.saveVSetCode(17L, "vSet = {myVt};");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_LASTSET) = 17L;
                   }
                 break;

           PC = 2; break;
         }

         case 2:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START

                 write = &UDF_testGlobalVariableInVsetUDF::Write_2_vSet;
                 context->set_udfprintsetting(1);

                 writer.WriteName("vSet");
                 writer.WriteStartArray();
                 context->AddPrintJob(gvector<std::string>{});
                 if (context->IsAborted()) return;
                 writer.WriteEndArray();
                 } //END
                 writer.WriteEndObject();
                 PC = 3;
                 break;

           PC = 3; break;
         }

         case 3:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

inline VERTEX HF_toVertexFromUidType(gpelib4::BaseContext* context, string uid, string vType) {
  if (vType != "Comment" && vType != "Post" && vType != "Company" && vType != "University" && vType != "City" && vType != "Country" && vType != "Continent" && vType != "Forum" && vType != "Person" && vType != "Tag" && vType != "TagClass") {
    std::string msg("Runtime Error: " + vType + " is not valid vertex type.");
    HF_set_error(_request, msg, true);
    context->Abort();
    return VERTEX(-1);
  }

  gvector<std::pair<std::string, std::string> > type_uid_list;
  type_uid_list.push_back(std::pair<string, string>(vType, uid));
  std::stringstream error_ss;
  gvector<VertexLocalId_t> vidList = _serviceapi->Convert_UIdtoVId (&_request, type_uid_list, error_ss);
  if (vidList.empty()) {
    HF_set_error(_request, error_ss.str(), false);
    context->Abort();
    throw gutil::GsqlException(error_ss.str(), gutil::error_t::E_VERTEX_TYPE);
  }
  VertexLocalId_t vid = vidList[0];
  if (vid == (VertexLocalId_t)-1) {
    std::string msg("Runtime Error: " + uid + " is not valid " + vType + " vertex.");
    HF_set_error(_request, msg, true);
    context->Abort();
    return VERTEX(-1);
  }
  return VERTEX(vid);
}

inline static SetAccum<string > HF_get_attr_setstring_V(V_ATTR* attr, int index) {
SetAccum<string >  result;
  auto reader = attr->GetStringValueReader(index);
  while (reader.MoveNext()) {
    result += reader.value_;
  }
  return result;
}

private:
   string _vid;
   bool vid_flag;
   string _vType;
   bool vType_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Comment = 0;
const int _schema_VTY_Post = 1;
const int _schema_VTY_Company = 2;
const int _schema_VTY_University = 3;
const int _schema_VTY_City = 4;
const int _schema_VTY_Country = 5;
const int _schema_VTY_Continent = 6;
const int _schema_VTY_Forum = 7;
const int _schema_VTY_Person = 8;
const int _schema_VTY_Tag = 9;
const int _schema_VTY_TagClass = 10;
int _schema_VATT_Comment_576037_id = -1;
int _schema_VATT_Comment_576037_creationDate = -1;
int _schema_VATT_Comment_576037_locationIP = -1;
int _schema_VATT_Comment_576037_browserUsed = -1;
int _schema_VATT_Comment_576037_content = -1;
int _schema_VATT_Comment_576037_length = -1;
int _schema_VATT_Post_576037_id = -1;
int _schema_VATT_Post_576037_imageFile = -1;
int _schema_VATT_Post_576037_creationDate = -1;
int _schema_VATT_Post_576037_locationIP = -1;
int _schema_VATT_Post_576037_browserUsed = -1;
int _schema_VATT_Post_576037_lang = -1;
int _schema_VATT_Post_576037_content = -1;
int _schema_VATT_Post_576037_length = -1;
int _schema_VATT_Company_576037_id = -1;
int _schema_VATT_Company_576037_name = -1;
int _schema_VATT_Company_576037_url = -1;
int _schema_VATT_University_576037_id = -1;
int _schema_VATT_University_576037_name = -1;
int _schema_VATT_University_576037_url = -1;
int _schema_VATT_City_576037_id = -1;
int _schema_VATT_City_576037_name = -1;
int _schema_VATT_City_576037_url = -1;
int _schema_VATT_Country_576037_id = -1;
int _schema_VATT_Country_576037_name = -1;
int _schema_VATT_Country_576037_url = -1;
int _schema_VATT_Continent_576037_id = -1;
int _schema_VATT_Continent_576037_name = -1;
int _schema_VATT_Continent_576037_url = -1;
int _schema_VATT_Forum_576037_id = -1;
int _schema_VATT_Forum_576037_title = -1;
int _schema_VATT_Forum_576037_creationDate = -1;
int _schema_VATT_Person_576037_id = -1;
int _schema_VATT_Person_576037_firstName = -1;
int _schema_VATT_Person_576037_lastName = -1;
int _schema_VATT_Person_576037_gender = -1;
int _schema_VATT_Person_576037_birthday = -1;
int _schema_VATT_Person_576037_creationDate = -1;
int _schema_VATT_Person_576037_locationIP = -1;
int _schema_VATT_Person_576037_browserUsed = -1;
int _schema_VATT_Person_576037_speaks = -1;
int _schema_VATT_Person_576037_email = -1;
int _schema_VATT_Tag_576037_id = -1;
int _schema_VATT_Tag_576037_name = -1;
int _schema_VATT_Tag_576037_url = -1;
int _schema_VATT_TagClass_576037_id = -1;
int _schema_VATT_TagClass_576037_name = -1;
int _schema_VATT_TagClass_576037_url = -1;
   V_VALUE* vvalptr = nullptr;
};

  bool call_testGlobalVariableInVsetUDF(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query testGlobalVariableInVsetUDF in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_testGlobalVariableInVsetUDF udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetUDF") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetUDF") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetUDF") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "testGlobalVariableInVsetUDF") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_testGlobalVariableInVsetUDF(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,string vid,string vType) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_testGlobalVariableInVsetUDF udf(request, serviceapi, _graphupdates_ , vid, vType, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_testGlobalVariableInVsetUDF_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_testGlobalVariableInVsetUDF(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,string vid,string vType){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_testGlobalVariableInVsetUDF(graphAPI, request, serviceapi,_graphupdates_ , vid, vType);
}
  bool call_testGlobalVariableInVsetUDF(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query testGlobalVariableInVsetUDF in all vetext active mode." << std::endl;
  }

if (values.size() != 2) {
    HF_set_error(request, "Invalid parameter size: 2|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
string vid = values[0]->GetStringInternal();
string vType = values[1]->GetStringInternal();

  UDIMPL::ldbc_snb::UDF_testGlobalVariableInVsetUDF udf(request, serviceapi, graphupdates, vid, vType, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
