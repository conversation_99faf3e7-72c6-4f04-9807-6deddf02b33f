/********** query start ***************
create or replace query subQuery(vertex v) for graph ldbc_snb returns(tuple<uint, datetime, vertex, edge>){
  typedef tuple<uint ui, datetime dtime, vertex v, edge e> subTuple;
  //typedef tuple<uint ui, datetime dtime, vertex<Person>, edge<LIKES>> subTuple;
  subTuple tmp;
  datetime dtime = to_datetime("2017-01-01 00:00:00");

  vSet = { Person.* };
  Result1 = select p from vSet:p -(LIKES>:e)- :tgt
            where p.id == 21990232555801
            accum tmp = subTuple(1, dtime, v, e);
  print tmp;
  return tmp;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace ldbc_snb{ 
class UDF_subQuery :public gpelib4::BaseUDF {
struct subTuple {
  uint64_t ui;
  DATETIME dtime;
  VERTEX v;
  EDGE e;

  subTuple() {
    ui = 0;
    dtime = DATETIME(0);
  }

  subTuple(uint64_t ui_, DATETIME dtime_, VERTEX v_, EDGE e_){
    ui = ui_;
    dtime = dtime_;
    v = v_;
    e = e_;
  }
  operator std::tuple<uint64_t, DATETIME, VERTEX, EDGE>() const {
    return std::make_tuple(ui,dtime,v,e);
  }

  subTuple(const std::tuple<uint64_t, DATETIME, VERTEX, EDGE>& __GQUERY__other__576037) {
    ui = std::get<0>(__GQUERY__other__576037);
    dtime = std::get<1>(__GQUERY__other__576037);
    v = std::get<2>(__GQUERY__other__576037);
    e = std::get<3>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const subTuple& m) {
    os<<"[";
    os<<"ui "<<m.ui<<"|";
    os<<"dtime "<<m.dtime<<"|";
    os<<"v "<<m.v<<"|";
    os<<"e "<<m.e<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const subTuple& m) {
    os<<"[";
    os<<"ui "<<m.ui<<"|";
    os<<"dtime "<<m.dtime<<"|";
    os<<"v "<<m.v<<"|";
    os<<"e "<<m.e<<"]";
      return os ;
  }


  bool operator==(subTuple const &__GQUERY__other__576037) const {
    return
      ui == __GQUERY__other__576037.ui &&
      dtime == __GQUERY__other__576037.dtime &&
      v == __GQUERY__other__576037.v &&
      e == __GQUERY__other__576037.e;
  }


  subTuple& operator+=( const subTuple& __GQUERY__other__576037) {
      ui += __GQUERY__other__576037.ui;
      dtime += __GQUERY__other__576037.dtime;
      v= __GQUERY__other__576037.v;
      e += __GQUERY__other__576037.e;
    return *this;
  }

  friend std::size_t hash_value(const subTuple& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.ui);
    boost::hash_combine(seed, other.dtime);
    boost::hash_combine(seed, other.v);
    boost::hash_combine(seed, other.e);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("ui");
      writer.WriteUnsignedInt(ui);
      writer.WriteNameString("dtime");
      writer.WriteString((std::string)DATETIME(dtime));
      writer.WriteNameString("v");
      (v).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("e");
      (e).json_printer(writer, _request, graphAPI, true);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(subTuple const &__GQUERY__other__576037) const {
      if (ui < __GQUERY__other__576037.ui) return true;
      if (ui > __GQUERY__other__576037.ui) return false;
      if (dtime < __GQUERY__other__576037.dtime) return true;
      if (dtime > __GQUERY__other__576037.dtime) return false;
      if (v < __GQUERY__other__576037.v) return true;
      if (v > __GQUERY__other__576037.v) return false;
      if (e < __GQUERY__other__576037.e) return true;
      if (e > __GQUERY__other__576037.e) return false;
      return false;
  }


  bool operator>(subTuple const &__GQUERY__other__576037) const {
      if (ui > __GQUERY__other__576037.ui) return true;
      if (ui < __GQUERY__other__576037.ui) return false;
      if (dtime > __GQUERY__other__576037.dtime) return true;
      if (dtime < __GQUERY__other__576037.dtime) return false;
      if (v > __GQUERY__other__576037.v) return true;
      if (v < __GQUERY__other__576037.v) return false;
      if (e > __GQUERY__other__576037.e) return true;
      if (e < __GQUERY__other__576037.e) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (ui, dtime, v, e);
   }

};

struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_subQuery (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
    if (request.jsoptions_.isMember("v")) {
      VertexLocalId_t localId;
      if (request.jsoptions_.isMember("no_translation_eid_to_iid") && request.jsoptions_["no_translation_eid_to_iid"][0].asString() == "true") {
        _v = VERTEX(std::atoll(request.jsoptions_["v"][0]["id"].asString().c_str()));
      } else {
        std::stringstream ss;
        ss << serviceapi.GetTopologyMeta()->GetVertexTypeId(request.jsoptions_["v"][0]["type"].asString(), _request.graph_id_);
        ss << "_" << request.jsoptions_["v"][0]["id"].asString();
        if (serviceapi.UIdtoVId (request, ss.str(), localId, false)) {
          _v = VERTEX(localId);
        } else {
          std::string msg("Failed to convert user vertex id for parameter v");
          HF_set_error(request, msg, true);
          return;
        }
      }
      v_flag = true;
    } else {
      _v = VERTEX(-1);
      v_flag = false;
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }

}




   UDF_subQuery (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , VERTEX v, gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
topology4::AttributesMeta& VTY_Person_attrMeta = meta->GetVertexType(8).attributes_;
_schema_VATT_Person_576037_id = VTY_Person_attrMeta.GetAttributePosition("id", true);
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
_v = v;
v_flag = true;
__GQUERY__all_vetex_mode = false;

}

   ~UDF_subQuery () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_PARAM_v, GV_SYS_v_flag, GV_GV_i_1, GV_GV_tmp_1, GV_GV_dtime_1, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_Result1_SIZE, GV_SYS_Result1_ORDERBY, GV_SYS_Result1_LASTSET, GV_SYS_vSet_SIZE, GV_SYS_vSet_ORDERBY, GV_SYS_vSet_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_Result1_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_Result1_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_Result1_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vSet_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vSet_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vSet_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params
   gvs->Register (GV_PARAM_v, new  gpelib4::BroadcastVariable<VERTEX> (_v));
   gvs->Register (GV_SYS_v_flag, new  gpelib4::BroadcastVariable<bool> (v_flag));

   // global variables
   gvs->Register (GV_GV_i_1, new gpelib4::StateVariable<int64_t> (int64_t()));
   gvs->Register (GV_GV_tmp_1, new gpelib4::StateVariable<subTuple> (subTuple()));
   gvs->Register (GV_GV_dtime_1, new gpelib4::StateVariable<DATETIME> (DATETIME()));

   // loop indices

   //limit k gv heap

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_subQuery::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void EdgeMap_4 (const VERTEX& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                const VERTEX& tgt,
                V_ATTR*                tgt_attr,
                const V_VALUE&         tgt_val,
                E_ATTR*                e_attr,
                gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Enter function EdgeMap_4 src: " << src << " tgt: " << tgt << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(true)) return;

   //attributes' local var declaration
   uint64_t p_id_uint64_t = 0;
   bool p_id_uint64_t_flag = false;

   //get src's attribute
   int src_typeIDVar = context->GraphAPI()->GetVertexType(src);
     if (src_typeIDVar == _schema_VTY_Person) {
       p_id_uint64_t = src_attr->GetUInt(_schema_VATT_Person_576037_id, 0);
     p_id_uint64_t_flag = true;
     }

   // WHERE
   if (!(p_id_uint64_t_flag && (p_id_uint64_t) == (21990232555801l))) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // ACCUM
   

   context->GlobalVariable_Reduce(GV_GV_tmp_1, static_cast<subTuple>(( subTuple(( 1l), ( context->GlobalVariable_GetValue<DATETIME> (GV_GV_dtime_1)), ( HF_retrieve_param_856409387<VERTEX>(context, GV_PARAM_v, GV_SYS_v_flag, "v")), ( EDGE (src, tgt, e_attr->type(), e_attr->GetDiscriminatorBinary()))))));



   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Exit function EdgeMap_4 src: " << src << " tgt " << tgt << std::endl;
}

void Reduce_4 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Enter function Reduce_4 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):


   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Exit function Reduce_4 v: " << v << std::endl;
}


void (UDF_subQuery::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_subQuery::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("subQuery");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_subQuery DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 PC = 1;
                 break;
           PC = 1; break;
         }

         case 1:
         {

                 PC = 2;
                 break;
           PC = 2; break;
         }

         case 2:
         {

                 context->GlobalVariable_GetValue<DATETIME> (GV_GV_dtime_1)=( (gutil::GtimeConverter::datetime_constructor(( string("2017-01-01 00:00:00")), context, _request)));
                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {
                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);

                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_Person,true);

                 PC = 4;
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vSet";
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vSet_ORDERBY) = -1;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(122L, "vSet", 13);
                     timer_.saveVSetCode(122L, "vSet = { Person.* };");
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_LASTSET) = 122L;
                   }
                 break;

           PC = 4; break;
         }

         case 4:
         {

                 context->set_UDFMapRun (gpelib4::UDFMapRun_EdgeMap);

                 context->GetTypeFilterController ()->DisableAllEdgeTypes ();
                 context->GetTypeFilterController ()->EnableEdgeType (_schema_ETY_LIKES);

                 edgemap   = &UDF_subQuery::EdgeMap_4;
                 context->set_udfedgemapsetting(1);

                 reduce    = &UDF_subQuery::Reduce_4;
                 context->set_udfreducesetting(0);

{
gvector<gvector<gindex::IndexPredicate>> hints_src_4;
{
 gvector<gindex::IndexPredicate> hint_src_4;
if (_schema_VTY_Person != -1 && _schema_VATT_Person_576037_id != -1) {
hint_src_4.emplace_back(gindex::IndexHint::CreateIndexPredicate_uint64_t(gindex::EQUAL, _schema_VTY_Person, _schema_VATT_Person_576037_id, {21990232555801l}
));
}
hints_src_4.push_back(std::move(hint_src_4));
}
gindex::IndexHint indexHint_src_4(std::move(hints_src_4));
context->SetSrcIndexHint(std::move(indexHint_src_4));
}
                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "Result1";
                 PC = 5;
                 if (context->CalcActiveVertexCount() > 0 || __GQUERY__all_vetex_mode) {
                   // save PC of next statement:
                   context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                   GCOUT(Verbose_FrameworkHigh) << "[UDF_subQuery INFO] " << "Exit function BeforeIteraion new pc: " << PC << std::endl;
                   if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                     timer_.registerVSet(120L, "Result1", 15);
                     timer_.saveVSetCode(120L, "Result1 =\n\tselect p\n\tfrom vSet:p -(LIKES>:e)- _:tgt\n\twhere  p.id == 21990232555801\n\taccum       tmp = subTuple(1, dtime, v, e)\n\t\t;");
                     timer_.addDependency(120L, context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vSet_LASTSET));
                     timer_.start("Result1", 15, context->CalcActiveVertexCount(), 120L);
                     context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result1_LASTSET) = 120L;
                   }
                   return;
                 } else {
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result1_SIZE) = 0;
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_Result1_ORDERBY) = -1;
                   break;
                 }

           PC = 5; break;
         }

         case 5:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("tmp");
(( context->GlobalVariable_GetValue<subTuple> (GV_GV_tmp_1))).json_printer(writer, _request, context->GraphAPI(), true);

                 } //END
                 writer.WriteEndObject();
                 PC = 6;
                 break;

           PC = 6; break;
         }

         case 6:
         {

      __GQUERY__rtnVal_0 = ( context->GlobalVariable_GetValue<subTuple> (GV_GV_tmp_1));
      context->Stop();
      return;
           PC = 7; break;
         }

         case 7:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 4:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_Result1_ORDERBY) = -1;
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_Result1_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

template <typename R>
inline R& HF_retrieve_param_856409387(gpelib4::MasterContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R>
inline const R& HF_retrieve_param_856409387(gpelib4::GlobalVariableContext* ctx, uint32_t pos, uint32_t flag, const char* name) {
  if (!ctx->GlobalVariable_GetValue<bool> (flag)) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return ctx->GlobalVariable_GetValue<R> (pos);
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::MasterContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

template <typename R, typename T>
inline const R& HF_retrieve_param_856409387_file(gpelib4::GlobalVariableContext* ctx, const T& param, bool flag, const char* name) {
  if (!flag) {
    std::string msg("Runtime Error: Parameter " + string(name) + " is NULL.");
    HF_set_error(_request, msg, true);
    ctx->Abort();
    static R tmp_var = R();
    return tmp_var;
  }
  return param;
}

private:
   VERTEX _v;
   bool v_flag;

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_Person = 8;
const int _schema_ETY_LIKES = 21;
int _schema_VATT_Person_576037_id = -1;
   V_VALUE* vvalptr = nullptr;
public:
subTuple __GQUERY__rtnVal_0;
};

  bool call_subQuery(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query subQuery in all vetext active mode." << std::endl;
    }
    UDIMPL::ldbc_snb::UDF_subQuery udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "subQuery") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "subQuery") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "subQuery") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "subQuery") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  std::tuple<uint64_t,DATETIME,VERTEX,EDGE> call_q_subQuery(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,VERTEX v) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::ldbc_snb::UDF_subQuery udf(request, serviceapi, _graphupdates_ , v, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
return udf.__GQUERY__rtnVal_0;
}
  bool call_subQuery_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  std::tuple<uint64_t,DATETIME,VERTEX,EDGE> call_q_subQuery(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ,VERTEX v){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
return call_q_subQuery(graphAPI, request, serviceapi,_graphupdates_ , v);
}
  bool call_subQuery(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query subQuery in all vetext active mode." << std::endl;
  }

if (values.size() != 1) {
    HF_set_error(request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }
VERTEX v = values[0]->GetVidInternal();


  UDIMPL::ldbc_snb::UDF_subQuery udf(request, serviceapi, graphupdates, v, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

interpret::ValuePtr __GQUERY__rtnVal_0 = interpret::ValuePtr(new interpret::TupleValue({ "ui","dtime","v","e"}));
{
interpret::ValuePtr tuple_0 = interpret::ValuePtr(new interpret::UIntValue(udf.__GQUERY__rtnVal_0.ui));
__GQUERY__rtnVal_0->Update(0, tuple_0);
}
{
interpret::ValuePtr tuple_0 = interpret::ValuePtr(new interpret::DatetimeValue((int64_t)udf.__GQUERY__rtnVal_0.dtime));
__GQUERY__rtnVal_0->Update(1, tuple_0);
}
{
interpret::ValuePtr tuple_0 = interpret::ValuePtr(new interpret::VertexValue(udf.__GQUERY__rtnVal_0.v));
__GQUERY__rtnVal_0->Update(2, tuple_0);
}
{
interpret::ValuePtr tuple_0 = interpret::ValuePtr(new interpret::EdgeValue(udf.__GQUERY__rtnVal_0.e));
__GQUERY__rtnVal_0->Update(3, tuple_0);
}

returns.push_back(__GQUERY__rtnVal_0);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
