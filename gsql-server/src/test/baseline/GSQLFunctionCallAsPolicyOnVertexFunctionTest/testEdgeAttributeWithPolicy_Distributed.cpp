/********** query start ***************
CREATE QUERY testEdgeAttributeWithPolicy(string etName) FOR GRAPH graphlet {
  SetAccum<STRING> @@edgeAttrsSet1;
  SetAccum<DATETIME> @@edgeAttrsSet2;
  vSet = {person.*};
  Result = SELECT s
      FROM vSet:s
      POST-ACCUM
        FOREACH tt IN s.edgeattribute("work_at", "title") DO
          @@edgeAttrsSet1 += tt
        END,
        @@edgeAttrsSet2 += s.edgeattribute("attend", "start_time")
      ;
  PRINT Result;
  PRINT @@edgeAttrsSet1;
  PRINT @@edgeAttrsSet2;
}
********** query end ***************/
#include "graphlet-schemaIndex.hpp"
#include "gle/engine/cpplib/querydispatcher.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "utility/gutil/gsqlcontainer.hpp"
#include "utility/gutil/glogging.hpp"
#include "utility/gutil/gtimelib.hpp"
#include "utility/gutil/gtimer.hpp"
#include "utility/gutil/gcleanup.hpp"
#include "utility/gutil/gsql_exception.hpp"
#include <stdarg.h>
#include <string>
#include <pthread.h>
#include <fstream>
#include <sstream>
#include <tuple>
#include "thirdparty/murmurhash/MurmurHash2.h"
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"
#include "gle/engine/cpplib/string_like/cpp_libs.hpp"

#include "core/gpe/gpr/gpr.hpp"
#include "core/gpe/gpr/remote_topology/filter.hpp"
#include "olgp/gpe/workermanager.hpp"
#include "olgp/gpe/workerinstance.hpp"
#include "olgp/gpe/packagemanager.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"

using namespace gperun;

using std::abs;
namespace UDIMPL {
  using namespace GSQL_UTIL;
  typedef std::string string;
  namespace graphlet {
    class GPR_testEdgeAttributeWithPolicy {
      // These type aliases are for gsql package function
      using __GQUERY__pkg1_pkg2_func1_184462_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*, const string&, const int64_t&);
      using __GQUERY__pkg1_pkg2_func2_184463_fptr_t = bool (*)(const ghash_set<std::string>*, const gvector<bool>*);
      
      struct DefaultDelta {
        // accumulators
        // does recipient of this message play role of src/tgt/both?
        // set in EdgeMap, needed in reduce
        bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;
        DefaultDelta () {
          __GQUERY__isSrc__576037 = __GQUERY__isTgt__576037 = __GQUERY__isOther__576037 = false;
        }
        // message combinator
        DefaultDelta& operator+= (const DefaultDelta& __GQUERY__other_) {
          __GQUERY__isSrc__576037 |= __GQUERY__other_.__GQUERY__isSrc__576037;
          __GQUERY__isTgt__576037 |= __GQUERY__other_.__GQUERY__isTgt__576037;
          __GQUERY__isOther__576037 |= __GQUERY__other_.__GQUERY__isOther__576037;
          return *this;
        }
      };
      struct __GQUERY__VertexVal_ptr__576037 {
        // accumulators:
      };
      struct __GQUERY__VertexVal__576037 {
        // accumulators:
        
        
        // default constructor
        __GQUERY__VertexVal__576037 () {};
      };
      public:
        typedef __GQUERY__VertexVal__576037                  V_VALUE;
      typedef topology4::VertexAttribute V_ATTR;
      typedef topology4::EdgeAttribute   E_ATTR;
      
      ///class constructor
      GPR_testEdgeAttributeWithPolicy (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, bool is_worker = false): _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = false;
        _request.SetJSONAPIVersion("v2");
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_person_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_person_576037_name = VTY_person_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_person_576037_height = VTY_person_attrMeta.GetAttributePosition("height", true);
        _schema_VATT_person_576037_gender = VTY_person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_person_576037_married = VTY_person_attrMeta.GetAttributePosition("married", true);
        _schema_VATT_person_576037_birthYear = VTY_person_attrMeta.GetAttributePosition("birthYear", true);
        topology4::AttributesMeta& ETY_work_at_attrMeta = meta->GetEdgeType(3).attributes_;
        _schema_EATT_work_at_576037_title = ETY_work_at_attrMeta.GetAttributePosition("title", true);
        topology4::AttributesMeta& ETY_attend_attrMeta = meta->GetEdgeType(13).attributes_;
        _schema_EATT_attend_576037_start_time = ETY_attend_attrMeta.GetAttributePosition("start_time", true);
        if (request.jsoptions_.isMember("etName")) {
          _etName = request.jsoptions_["etName"][0].asString();
          etName_flag = true;
        } else {
          // parameter is not given (null case)
          _etName = string();
          etName_flag = false;
        }
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer = _request.outputwriter_;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_pkg2_func1_184462_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_func1_184462_fptr_t>(std::string("callfunc_func1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_func1_184462_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.func1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_func2_184463_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_func2_184463_fptr_t>(std::string("callfunc_func2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_func2_184463_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.func2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      //query calling query constructor
      GPR_testEdgeAttributeWithPolicy (gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_, string etName, bool is_worker, bool _isQueryCalled_): _graphAPI(graphAPI), _request(request), _serviceapi(&serviceapi) {
        isQueryCalled = _isQueryCalled_;
        topology4::TopologyMeta* meta = _serviceapi->GetTopologyMeta();
        topology4::AttributesMeta& VTY_person_attrMeta = meta->GetVertexType(0).attributes_;
        _schema_VATT_person_576037_name = VTY_person_attrMeta.GetAttributePosition("name", true);
        _schema_VATT_person_576037_height = VTY_person_attrMeta.GetAttributePosition("height", true);
        _schema_VATT_person_576037_gender = VTY_person_attrMeta.GetAttributePosition("gender", true);
        _schema_VATT_person_576037_married = VTY_person_attrMeta.GetAttributePosition("married", true);
        _schema_VATT_person_576037_birthYear = VTY_person_attrMeta.GetAttributePosition("birthYear", true);
        topology4::AttributesMeta& ETY_work_at_attrMeta = meta->GetEdgeType(3).attributes_;
        _schema_EATT_work_at_576037_title = ETY_work_at_attrMeta.GetAttributePosition("title", true);
        topology4::AttributesMeta& ETY_attend_attrMeta = meta->GetEdgeType(13).attributes_;
        _schema_EATT_attend_576037_start_time = ETY_attend_attrMeta.GetAttributePosition("start_time", true);
        _etName = etName;
        etName_flag = true;
        if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
          file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
        }
        
        __GQUERY__local_writer= &__GQUERY__local_writer_instance;
        enumMapper_ = serviceapi.GetEnumMappers();
        if (request.jsoptions_.isMember("__GQUERY_LOG_LEVEL")) {
          std::string __level = request.jsoptions_["__GQUERY_LOG_LEVEL"][0].asString();
          if (__level == "default") {
            __GQUERY_LOG_LEVEL = 0;
          } else if (__level == "brief") {
            __GQUERY_LOG_LEVEL = 1;
          } else if (__level == "debug") {
            __GQUERY_LOG_LEVEL = 2;
          } else if (__level == "detail") {
            __GQUERY_LOG_LEVEL = 3;
          }
        } else {
          __GQUERY_LOG_LEVEL = 0;
        }
        __disable_filter_ = request.jsoptions_.isMember("__disable_filter__");
        if (request.jsoptions_.isMember ("__GQUERY__")
          && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
            for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
              __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
        } else {
          addAllVertexTypesToSet(__GQUERY__vts_);
        }
        // initialize package name to pointer map
        if (request.pkg_names_.size() != request.pkg_libs_.size()) {
          std::string msg("package name and pointer list size differs!\n");
          msg.append("Size of package name list is ")
            .append(std::to_string(request.pkg_names_.size()))
            .append(".\n");
          msg.append("Size of package pointer list is ")
            .append(std::to_string(request.pkg_libs_.size()))
            .append(".\n");
          msg.append("The package name list: ");
          for (const auto& pkg_name : request.pkg_names_) {
            msg.append(pkg_name).append(",");
          }
          HF_set_error(request, msg, true);
          return;
        }
        for (auto i = 0; i < request.pkg_names_.size(); ++i) {
          _pkglibs_map[request.pkg_names_[i]] = request.pkg_libs_[i];
        }
        // initialize the function pointer and is_granted_cache for all GSQL functions used as policy
        pkg1_pkg2_func1_184462_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_func1_184462_fptr_t>(std::string("callfunc_func1"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_func1_184462_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.func1");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
        
        pkg1_pkg2_func2_184463_fptr = _pkglibs_map[std::string("libpkgudf_pkg1-pkg2")]->GetGSQLFunction<__GQUERY__pkg1_pkg2_func2_184463_fptr_t>(std::string("callfunc_func2"), gperun::PackageManager::USAGE_TYPE::POLICY);
        if (pkg1_pkg2_func2_184463_fptr == nullptr) {
          std::string msg("Un-initialized gsql package function pkg1.pkg2.func2");
          throw gutil::GsqlException(msg, gutil::error_t::E_GV_NOT_EXIST);
        }
      }
      
      ///class destructor
      ~GPR_testEdgeAttributeWithPolicy () {}
      
      ///vertex actions for write
      void Write_4(gpr::VertexEntity& vVertex, gpr::Context& context) {
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        bool hasOrder = context.GlobalVariableGet<bool>(2);
        V_VALUE v_val;
        
        // json writer
        gutil::JSONWriter writer;
        int Result_typeIDVar = graphAPI->GetVertexType(v);
        if(_schema_VTY_person != -1 && _schema_VTY_person == Result_typeIDVar) {
          writer.WriteStartObject();
          writer.WriteNameString("v_id");
          (v).json_printer(writer, _request, graphAPI, true);
          writer.WriteNameString("v_type");
          writer.WriteString(graphAPI->GetVertexTypeName(v));
          writer.WriteName("attributes");
          writer.WriteStartObject();
          if (_schema_VATT_person_576037_name != -1) {
            writer.WriteNameString("name");
            writer.WriteString(vVertex.GetAttr().GetString(_schema_VATT_person_576037_name));
          }
          if (_schema_VATT_person_576037_height != -1) {
            writer.WriteNameString("height");
            writer.WriteDouble(vVertex.GetAttr().GetDouble(_schema_VATT_person_576037_height, 0.0));
          }
          if (_schema_VATT_person_576037_gender != -1) {
            writer.WriteNameString("gender");
            (string_compress(&vVertex.GetAttr(), _schema_VATT_person_576037_gender)).json_printer(writer, _request, graphAPI, true);
          }
          if (_schema_VATT_person_576037_married != -1) {
            writer.WriteNameString("married");
            writer.WriteBool(vVertex.GetAttr().GetBool(_schema_VATT_person_576037_married, 0));
          }
          if (_schema_VATT_person_576037_birthYear != -1) {
            writer.WriteNameString("birthYear");
            writer.WriteInt(vVertex.GetAttr().GetInt(_schema_VATT_person_576037_birthYear, 0));
          }
          writer.WriteEndObject();
          writer.WriteEndObject();
        }
        if (hasOrder) {
          // print json to map for retrieve in oder later
          context.GlobalVariableAdd(0, MapAccum<VERTEX, JsonWriterGV>(v, JsonWriterGV(writer)));
        } else {
          // json writer
          context.GlobalVariableAdd(0, JsonWriterGV(writer));
        }
      }
      
      ///vertex/edge actions
      void VertexMap_2(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        string src_name_string = string();
        bool src_name_string_flag = false;
        int64_t src_birthYear_int64_t = 0;
        bool src_birthYear_int64_t_flag = false;
        
        //get src's attribute
        int src_typeIDVar = graphAPI->GetVertexType(src);
        if (src_typeIDVar == _schema_VTY_person) {
          if (srcVertex.GetAttr().IsValid()) {
            src_name_string = srcVertex.GetAttr().GetString(_schema_VATT_person_576037_name);
            src_name_string_flag = true;
          }
          if (srcVertex.GetAttr().IsValid()) {
            src_birthYear_int64_t = srcVertex.GetAttr().GetInt(_schema_VATT_person_576037_birthYear, 0);
            src_birthYear_int64_t_flag = true;
          }
        }
        V_VALUE src_val;
        if (!((src_name_string_flag && src_birthYear_int64_t_flag && pkg1_pkg2_func1_184462_fptr(_request.current_role_infos_.get(), pkg1_pkg2_func1_184462_is_granted_cache, src_name_string, src_birthYear_int64_t)))) return;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        V_VALUE v_val;
        
        if (src_delta.__GQUERY__isSrc__576037) {
          context.Activate(src, 0);
        }
      }
      
      void VertexMap_3(gpr::VertexEntity& srcVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        
        typedef DefaultDelta MESSAGE;
        
        VERTEX src = srcVertex.vid();
        auto graphAPI = context.GraphAPI();
        
        V_VALUE src_val;
        // prepare message
        DefaultDelta src_delta = DefaultDelta();
        src_delta.__GQUERY__isSrc__576037 = true;
        context.Write(src, src_delta, 0);
        context.Activate(src, 0);
      }
      void Reduce_3(gpr::VertexEntity& vVertex,
        gpr::Context& context) {
          if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
            std::string msg("Aborted due to timeout or system memory in critical state.");
          HF_set_error(_request, msg, true);
          return;
        }
        typedef DefaultDelta MESSAGE;
        
        VERTEX v = vVertex.vid();
        auto graphAPI = context.GraphAPI();
        auto delta = *vVertex.GetValuePtr(0).GetRawPtr<DefaultDelta>();
        
        V_VALUE v_val;
        
        // foreach tt in s.edgeattribute ( "work_at", "title" ) do @@edgeAttrsSet1 += tt end
        if (delta.__GQUERY__isSrc__576037) {
          uint64_t __GQUERY__iter_2 = 0;
          const auto&  __GQUERY__accum_2 = HF_get___GQUERY__set___576037work_at_title_3_0(graphAPI, v, [&](EdgesCollection& ec, topology4::VertexAttribute& vattr) { return HF_RowPolicyFilter_AnyVertex(context, ec.GetCurrentToVId()); });
          for (auto it = ( __GQUERY__accum_2).begin(); it != ( __GQUERY__accum_2).end(); it++) {
            auto& lvar_tt = *it;
            
            // @@edgeAttrsSet1 += tt
            context.GlobalVariableAdd(0, SetAccum<string > (lvar_tt));
            if (UNLIKELY((++__GQUERY__iter_2 & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
              std::string msg("Aborted due to timeout or system memory in critical state.");
              HF_set_error(_request, msg, true);
              return;
            }
          }
          
        }
        
        // @@edgeAttrsSet2 += s.edgeattribute ( "attend", "start_time" )
        if (delta.__GQUERY__isSrc__576037) {
          context.GlobalVariableAdd(1, SetAccum<DATETIME > (HF_get___GQUERY__set___576037attend_start_time_3_0(graphAPI, v, [&](EdgesCollection& ec, topology4::VertexAttribute& vattr) { return HF_RowPolicyFilter_AnyVertex(context, ec.GetCurrentToVId()); })));
        }
        if (delta.__GQUERY__isSrc__576037) {
          context.Activate(v, 0);
        }
        
      }
      
      ///gpr driver
      void run () {
        try {
          __GQUERY__local_writer->WriteStartArray();
          singleGprMainFlow();
          __GQUERY__local_writer->WriteEndArray();
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end run
      void singleGprMainFlow () {
        try {
          gshared_ptr<gpr::GPR> gpr = _serviceapi->CreateGPR(&_request, true);
          // for subquery, use the graphAPI from main query
          auto graphAPI = _graphAPI;
          if (!isQueryCalled) {
            // for normal query, create graph api
            graphAPI = _serviceapi->CreateUDFGraphAPI(&_request).get();
          }
          
          ///Create active sets
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_vSet = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_Result = gpr->CreateActiveSet();
          gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_target = gpr->CreateActiveSet();
          gpelib4::SumVariable<string> __GQUERY_GV_Global_Variable_etName_(_etName);
          gpelib4::SumVariable<bool> __GQUERY_GV_Global_Variable_etName_flag(etName_flag);
          
          gpelib4::SumVariable<SetAccum<string > > __GQUERY_GV_edgeAttrsSet1_1_;
          gpelib4::SumVariable<SetAccum<DATETIME > > __GQUERY_GV_edgeAttrsSet2_1_;
          gpelib4::SumVariable<JsonWriterGV> __GQUERY_GV_SYS_JSON;
          gpelib4::SumVariable<MapAccum<VERTEX, JsonWriterGV>> __GQUERY_GV_SYS_Map;
          gpelib4::StateVariable<bool> __GQUERY_GV_SYS_commit_update;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_vSet_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_vSet_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_vSet_vector_;
          gpelib4::SumVariable<int64_t> __GQUERY_GV_Global_Variable__vset_Result_SIZE__;
          gpelib4::StateVariable<bool> __GQUERY_GV_Global_Variable__vset_Result_hasOrder_;
          gpelib4::StateVariable<gvector<VERTEX>> __GQUERY_GV_Global_Variable__vset_Result_vector_;
          
          gpr::GPR_Container* __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg2(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request));
          gpr::GPR_Container* __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
          gutil::ScopedCleanUp sc_msg3(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request));
          {
            /*
            @@edgeAttrsSet1_1 reinitiate
            */
            
            // @@edgeAttrsSet1_1 reinitiate
            __GQUERY_GV_edgeAttrsSet1_1_.Value().clear();
            
          }
          {
            /*
            @@edgeAttrsSet2_1 reinitiate
            */
            
            // @@edgeAttrsSet2_1 reinitiate
            __GQUERY_GV_edgeAttrsSet2_1_.Value().clear();
            
          }
          {
            /*
            vSet = { person .* };
            */
            // vset
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "starts action_1_";
            //declare output gvs
            //run action
            __GQUERY__vSet_vSet->SetAllActiveFlag(false);
            __GQUERY__vSet_vSet->SetActiveFlagByType(_schema_VTY_person, true);
            __GQUERY_GV_Global_Variable__vset_vSet_SIZE__.Value() = __GQUERY__vSet_vSet->count();
            
          }
          {
            /*
            vSet = select src from vSet : src where ( pkg1.pkg2.func1 ( src.name, src.birthYear ) );
            */
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "starts action_2_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container2 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg2 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container2, &_request)));
            auto vertexAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy::VertexMap_2, this),//action function
              __GQUERY__vSet_vSet.get(),//input bitset
              {},//input container(v_value)
              {},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {&__GQUERY_GV_Global_Variable_etName_, &__GQUERY_GV_Global_Variable_etName_flag},//input gv
              {&__GQUERY_GV_Global_Variable__vset_vSet_SIZE__}//output gv
            );
            vertexAction->AddInputObject(this);
            
            //run vertex action
            gpr->Run({vertexAction.get()});
            //assign the temp vset to the output vset directly
            std::swap(__GQUERY__vSet_vSet, __GQUERY__vSet_target);
            __GQUERY_GV_Global_Variable__vset_vSet_SIZE__.Value() = __GQUERY__vSet_vSet->count();
            //update vset size
            
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "executor finish action_2_map";
            __GQUERY_GV_Global_Variable__vset_vSet_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_vSet_hasOrder_.Value() = false;
            
          }
          {
            /*
            Result = select s from vSet : s post-accum foreach tt in s.edgeattribute ( "work_at", "title" ) do @@edgeAttrsSet1 += tt end, @@edgeAttrsSet2 += s.edgeattribute ( "attend", "start_time" );
            */
            // map
            GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "starts action_3_map";
            //declare output gvs
            //run map action
            __GQUERY__vSet_target->Reset();
            __GQUERY__delta_container3 = gpr->CreateMsgContainer_RawPtr<DefaultDelta>();
            sc_msg3 = std::move(gutil::ScopedCleanUp(new gutil::CleanUpWrapper<gpr::GPR_Container>(__GQUERY__delta_container3, &_request)));
            auto vertexAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy::VertexMap_3, this),//action function
              __GQUERY__vSet_vSet.get(),//input bitset
              {},//input container(v_value)
              {__GQUERY__delta_container3},//output container(delta)
              {__GQUERY__vSet_target.get()},//result bitset
              {&__GQUERY_GV_Global_Variable_etName_, &__GQUERY_GV_Global_Variable_etName_flag},//input gv
              {&__GQUERY_GV_edgeAttrsSet1_1_, &__GQUERY_GV_edgeAttrsSet2_1_, &__GQUERY_GV_Global_Variable__vset_Result_SIZE__}//output gv
            );
            vertexAction->AddInputObject(this);
            
            //run vertex action
            
            gshared_ptr<gpr::GPR_ActiveSet> __GQUERY__vSet_Result_tmp = gpr->CreateActiveSet();
            auto reduceAction = gpr->CreateVertexAction(
              VertexActionFunc(&UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy::Reduce_3, this),//action function
              __GQUERY__vSet_target.get(),//input bitset
              {__GQUERY__delta_container3},//input container(old v_value and delta)
              {},//output container(new v_value)
              {__GQUERY__vSet_Result_tmp.get()},//result bitset
              {&__GQUERY_GV_Global_Variable_etName_, &__GQUERY_GV_Global_Variable_etName_flag},//input gv
              {&__GQUERY_GV_edgeAttrsSet1_1_, &__GQUERY_GV_edgeAttrsSet2_1_, &__GQUERY_GV_Global_Variable__vset_Result_SIZE__}//output gv
            );
            reduceAction->AddInputObject(this);
            
            //run actions together, where it prepares topology once
            gpr->SuccessivelyRun({vertexAction.get(), reduceAction.get()}, false, false);
            std::swap(__GQUERY__vSet_Result, __GQUERY__vSet_Result_tmp);
            __GQUERY_GV_Global_Variable__vset_Result_SIZE__.Value() = __GQUERY__vSet_Result->count();
            GUDFInfo((2<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "executor finish action_3_map";
            __GQUERY_GV_Global_Variable__vset_Result_hasOrder_.Value() = false;
            __GQUERY_GV_Global_Variable__vset_Result_hasOrder_.Value() = false;
            
          }
          {
            /*
            print Result;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              GUDFInfo((1<=__GQUERY_LOG_LEVEL ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|" << "starts action_4_";
              //run print action
              std::cout << "HERE print worker" << std::endl;
              gpelib4::BaseVariableObject* outputWriterGv;
              if (__GQUERY_GV_Global_Variable__vset_Result_hasOrder_.Value()) {
                __GQUERY_GV_SYS_Map.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_Map;
              } else {
                __GQUERY_GV_SYS_JSON.Value().clear();
                outputWriterGv = &__GQUERY_GV_SYS_JSON;
              }
              auto printAction = gpr->CreateVertexAction(
                VertexActionFunc(&UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy::Write_4, this),//action function
                __GQUERY__vSet_Result.get(),//input bitset
                {},//input container(v_value)
                {},//output container(delta)
                {},//result bitset
                {&__GQUERY_GV_Global_Variable_etName_, &__GQUERY_GV_Global_Variable_etName_flag, &__GQUERY_GV_Global_Variable__vset_Result_hasOrder_},//input gv
                {outputWriterGv}//output gv
              );
              printAction->AddInputObject(this);
              gpr->Run({printAction.get()});
              if (__GQUERY_GV_Global_Variable__vset_Result_hasOrder_.Value()) {
                __GQUERY__local_writer->WriteName("Result");
                __GQUERY__local_writer->WriteStartArray();
                for (auto it = __GQUERY_GV_Global_Variable__vset_Result_vector_.Value().begin(); it != __GQUERY_GV_Global_Variable__vset_Result_vector_.Value().end(); ++it) {
                  JsonWriterGV jsonWriterGV = __GQUERY_GV_SYS_Map.Value().get(it->vid);
                  __GQUERY__local_writer->WriteJSONContent(jsonWriterGV.buffer(), jsonWriterGV.size(), true);
                  // add the mark vids inside json writer from each worker for translate vid use
                  __GQUERY__local_writer->mark_vids().insert(jsonWriterGV.mark_vids().begin(), jsonWriterGV.mark_vids().end());
                }
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY_GV_SYS_Map.Value().clear();
              } else {
                __GQUERY__local_writer->WriteName("Result");
                __GQUERY__local_writer->WriteStartArray();
                __GQUERY__local_writer->WriteJSONContent(__GQUERY_GV_SYS_JSON.Value().buffer(), __GQUERY_GV_SYS_JSON.Value().size(), true);
                __GQUERY__local_writer->WriteEndArray();
                __GQUERY__local_writer->mark_vids().insert(__GQUERY_GV_SYS_JSON.Value().mark_vids().begin(), __GQUERY_GV_SYS_JSON.Value().mark_vids().end());
                __GQUERY_GV_SYS_JSON.Value().clear();
              }
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            print @@edgeAttrsSet1;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              gutil::JSONWriter& writer = *__GQUERY__local_writer;
              std::cout << "HERE print global" << std::endl;
              writer.WriteNameString("@@edgeAttrsSet1");
              (__HF_GPR_retrieveGV<SetAccum<string >  >(__GQUERY_GV_edgeAttrsSet1_1_, true, "edgeAttrsSet1_1")).json_printer(writer, _request, graphAPI, true);
              
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
          {
            /*
            print @@edgeAttrsSet2;
            */
            __GQUERY__local_writer->WriteStartObject();
            {
              gutil::JSONWriter& writer = *__GQUERY__local_writer;
              std::cout << "HERE print global" << std::endl;
              writer.WriteNameString("@@edgeAttrsSet2");
              (__HF_GPR_retrieveGV<SetAccum<DATETIME >  >(__GQUERY_GV_edgeAttrsSet2_1_, true, "edgeAttrsSet2_1")).json_printer(writer, _request, graphAPI, true);
              
            }
            __GQUERY__local_writer->WriteEndObject();
            
          }
        } catch(gutil::GsqlException& e) {
          _request.SetErrorCode(e.code());
          _request.SetErrorMessage(e.msg());
          _request.error_ = true;
        }
        
      }//end singleGprMainFlow
      
      ///helper function
      template<class R, class T>
      inline R& __HF_GPR_retrieveGV(T& p, bool flag, const char* name) {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "+ string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return p.Value();
      }
      template<class R>
      inline const R& __HF_GPR_retrieveGV(gpr::Context& context, uint32_t pos, bool flag, const char* name) const {
        if (!flag) {
          std::string msg("Runtime Error: Parameter "
            + string(name) + " is NULL.");
          throw gutil::GsqlException(msg, gutil::error_t::E_PARAM_NULL);
        }
        return context.GlobalVariableGet<R>(pos);
      }
      
      BagAccum<string> HF_get___GQUERY__set___576037work_at_title_3_0(gapi4::UDFGraphAPI* graphAPI, const VERTEX& src, std::function<bool(EdgesCollection&, topology4::VertexAttribute&)> condition) {
        BagAccum<string> __GQUERY__set___576037work_at_title_3_0;
        EdgesCollection ec;
        gapi4::EdgesFilter_ByOneType filter(_schema_ETY_work_at);
        graphAPI->GetEdges(src, &filter, ec);
        while(ec.NextEdge()){
          topology4::VertexAttribute vattr;
          if (condition(ec, vattr)) {
            __GQUERY__set___576037work_at_title_3_0 += ec.GetCurrentEdgeAttribute()->GetString(_schema_EATT_work_at_576037_title);
          }
        }
        return __GQUERY__set___576037work_at_title_3_0;
      }
      
      inline bool HF_RowPolicyFilter_AnyVertex(gpr::Context& context, const VERTEX& toVid) {
        gapi4::UDFGraphAPI* graphAPI = context.GraphAPI();
        uint32_t vtypeid = graphAPI->GetVertexType(toVid);
        if (vtypeid == _schema_VTY_person) {
          topology4::VertexAttribute toVAttr;
          if (!graphAPI->GetVertex(toVid, toVAttr)) {
            std::string msg("Failed to get attribute values for vid "
              + std::to_string(toVid) + " of vertex type 'person', typeid " + std::to_string(_schema_VTY_person));
            throw gutil::GsqlException(msg, gutil::error_t::E_VERTEX_ATT);
          }
          string person_name_string = toVAttr.GetString(_schema_VATT_person_576037_name);
          int64_t person_birthYear_int = toVAttr.GetInt(_schema_VATT_person_576037_birthYear, 0);
          return pkg1_pkg2_func1_184462_fptr(_request.current_role_infos_.get(), pkg1_pkg2_func1_184462_is_granted_cache, person_name_string, person_birthYear_int);
        } else if (vtypeid == _schema_VTY_company) {
          return pkg1_pkg2_func2_184463_fptr(_request.current_role_infos_.get(), pkg1_pkg2_func2_184463_is_granted_cache);
        } else {
          return true;
        }
      }
      BagAccum<DATETIME> HF_get___GQUERY__set___576037attend_start_time_3_0(gapi4::UDFGraphAPI* graphAPI, const VERTEX& src, std::function<bool(EdgesCollection&, topology4::VertexAttribute&)> condition) {
        BagAccum<DATETIME> __GQUERY__set___576037attend_start_time_3_0;
        EdgesCollection ec;
        gapi4::EdgesFilter_ByOneType filter(_schema_ETY_attend);
        graphAPI->GetEdges(src, &filter, ec);
        while(ec.NextEdge()){
          topology4::VertexAttribute vattr;
          if (condition(ec, vattr)) {
            __GQUERY__set___576037attend_start_time_3_0 += ec.GetCurrentEdgeAttribute()->GetInt(_schema_EATT_attend_576037_start_time, 0);
          }
        }
        return __GQUERY__set___576037attend_start_time_3_0;
      }
      
      private:
        
        ///class members
        gapi4::UDFGraphAPI* _graphAPI;
      EngineServiceRequest& _request;
      ServiceAPI* _serviceapi;
      gse2::EnumMappers* enumMapper_;
      bool monitor_;
      bool isQueryCalled;// true if this is a sub query
      bool monitor_all_;
      char file_sep_ = ',';
      __GQUERY__Timer timer_;
      bool __GQUERY__all_vetex_mode;
      gutil::JSONWriter* __GQUERY__local_writer;
      gutil::JSONWriter __GQUERY__local_writer_instance;
      SetAccum<uint32_t> __GQUERY__vts_;
      string _etName;
      bool etName_flag = true;
      const int _schema_VTY_person = 0;
      const int _schema_VTY_company = 1;
      const int _schema_ETY_work_at = 3;
      const int _schema_ETY_attend = 13;
      int _schema_VATT_person_576037_name = -1;
      int _schema_VATT_person_576037_height = -1;
      int _schema_VATT_person_576037_gender = -1;
      int _schema_VATT_person_576037_married = -1;
      int _schema_VATT_person_576037_birthYear = -1;
      int _schema_EATT_work_at_576037_title = -1;
      int _schema_EATT_attend_576037_start_time = -1;
      uint64_t __GQUERY_LOG_LEVEL;
      bool __disable_filter_;
      const std::string action_1_ = "action_1_";
      const std::string action_2_map = "action_2_map";
      const std::string action_2_reduce = "action_2_reduce";
      const std::string action_2_post = "action_2_post";
      const std::string action_3_map = "action_3_map";
      const std::string action_3_reduce = "action_3_reduce";
      const std::string action_3_post = "action_3_post";
      const std::string action_4_ = "action_4_";
      const std::string action_5_ = "action_5_";
      const std::string action_6_ = "action_6_";
      ghash_map<std::string, PKGLib*> _pkglibs_map;
      __GQUERY__pkg1_pkg2_func1_184462_fptr_t pkg1_pkg2_func1_184462_fptr = nullptr;
      gvector<bool>* pkg1_pkg2_func1_184462_is_granted_cache = nullptr;
      __GQUERY__pkg1_pkg2_func2_184463_fptr_t pkg1_pkg2_func2_184463_fptr = nullptr;
      gvector<bool>* pkg1_pkg2_func2_184463_is_granted_cache = nullptr;
      public:
        
        ///return vars
      };//end class GPR_testEdgeAttributeWithPolicy
    bool call_testEdgeAttributeWithPolicy(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      
      try {
        UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy gpr(_request, serviceapi);
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_testEdgeAttributeWithPolicy_worker(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi) {
      //single gpr doing nothing
    }
    bool call_testEdgeAttributeWithPolicy(gpelib4::EngineServiceRequest& _request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns, UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
      
      try {
        if (values.size() != 1) {
          HF_set_error(_request, "Invalid parameter size: 1|" + boost::lexical_cast<std::string>(values.size()), true, true);
          return false;
        }
        string etName = values[0]->GetStringInternal();
        
        UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy gpr(graphAPI.get(), _request, serviceapi, graphupdates, etName, false, true);
        
        if (_request.error_) return false;
        gpr.run();
        
        return !_request.error_;
      } catch (gutil::GsqlException& e) {
        _request.SetErrorMessage(e.msg());
        _request.SetErrorCode(e.code());
        _request.error_ = true;
      } catch (const boost::exception& bex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " +
          boost::diagnostic_information(bex));
        _request.SetErrorCode(8001);
        _request.error_ = true;
      } catch (const std::runtime_error& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8002);
        _request.error_ = true;
      } catch (const std::exception& ex) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error " + std::string(ex.what()));
        _request.SetErrorCode(8003);
        _request.error_ = true;
      } catch (...) {
        GUDFInfo((true ? 0 : 0xffff), "testEdgeAttributeWithPolicy") << _request.requestid_ << "|"  << "get an exception";
        _request.SetErrorMessage("unexpected error ");
        _request.SetErrorCode(8999);
        _request.error_ = true;
      }
      return false;
    }
    void call_q_testEdgeAttributeWithPolicy(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , string etName) {
      UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy gpr(graphAPI, request, serviceapi, _graphupdates_, etName, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
    void call_q_testEdgeAttributeWithPolicy(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum, string etName) {
      UDIMPL::graphlet::GPR_testEdgeAttributeWithPolicy gpr(graphAPI, request, serviceapi, _graphupdates_, etName, false, true);
      if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
      gpr.run();
      
    }
  }//end namespace graphlet
}//end namespace UDIMPL
