/********** query start ***************
create query caller_vset_dist(){
    vset = select c
           from company:c
           limit 5;
    subquery_vset(vset, 5);
    int val = subquery_vset(vset, 5);
    print val;
    vset2 = select m
            from members:m
            limit 4;
    vset3 = select m
            from company:m
            limit 4;
    val = subquery_vset_2(vset, vset2, 5) + subquery_vset(vset3, 5);
    print val;
    int val2 = subquery_vset_2(vset, vset2, 5) + subquery_vset(vset3, 5);
    print val2;

    while subquery_vset_3(vset, 10) do
        val += 1;
    end;

    foreach i in subquery_vset_4(vset) do
        val += i;
        val += 1;
        val += i;
        val += subquery_vset(vset, 0);
    end;

    print val;

    if subquery_vset_3(vset, 0) then
        val += 1;
    else if subquery_vset_3(vset, 10) then
        val += 2;
    else if subquery_vset_3(vset3, 20) then
        val += 3;
    END;

    case
        when subquery_vset_3(vset, 0) then val += 1;
        when subquery_vset_3(vset, 10) then val += 2;
    end;

    print val;
}
********** query end ***************/
#include <sstream>
#include "thirdparty/boost/range/algorithm/set_algorithm.hpp"

#include "gle/engine/cpplib/headers.hpp"
#include "blue/features/interpreted_query_function/value/value.hpp"
#include "blue/features/interpreted_query_function/value/headers.hpp"
#include "utility/gutil/gpelibidtypes.hpp"
#include "gle/engine/cpplib/gquery_libs/gquery_libs.hpp"

using namespace gperun;

using std::abs;

namespace UDIMPL {

typedef std::string string;
namespace poc_graph{ 
extern   bool call_q_subquery_vset_3(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,SetAccum<VERTEX >  vset,int64_t inc);
extern   int64_t call_q_subquery_vset_2(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,SetAccum<VERTEX >  vset1,SetAccum<VERTEX >  vset2,int64_t inc);
extern   int64_t call_q_subquery_vset(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,SetAccum<VERTEX >  vset,int64_t inc);
extern   ListAccum<int64_t >  call_q_subquery_vset_4(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ,SetAccum<VERTEX >  vset);
class UDF_caller_vset_dist :public gpelib4::BaseUDF {
struct tupleLimitK_1603506567_0 {
  VERTEX vid;
  float randomValue;

  tupleLimitK_1603506567_0() {
    randomValue = 0;
  }

  tupleLimitK_1603506567_0(VERTEX vid_, float randomValue_){
    vid = vid_;
    randomValue = randomValue_;
  }
  operator std::tuple<VERTEX, float>() const {
    return std::make_tuple(vid,randomValue);
  }

  tupleLimitK_1603506567_0(const std::tuple<VERTEX, float>& __GQUERY__other__576037) {
    vid = std::get<0>(__GQUERY__other__576037);
    randomValue = std::get<1>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const tupleLimitK_1603506567_0& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const tupleLimitK_1603506567_0& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  bool operator==(tupleLimitK_1603506567_0 const &__GQUERY__other__576037) const {
    return
      vid == __GQUERY__other__576037.vid &&
      randomValue == __GQUERY__other__576037.randomValue;
  }


  tupleLimitK_1603506567_0& operator+=( const tupleLimitK_1603506567_0& __GQUERY__other__576037) {
      vid= __GQUERY__other__576037.vid;
      randomValue += __GQUERY__other__576037.randomValue;
    return *this;
  }

  friend std::size_t hash_value(const tupleLimitK_1603506567_0& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.vid);
    boost::hash_combine(seed, other.randomValue);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("vid");
      (vid).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("randomValue");
      writer.WriteFloat(randomValue);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(tupleLimitK_1603506567_0 const &__GQUERY__other__576037) const {
      if (vid < __GQUERY__other__576037.vid) return true;
      if (vid > __GQUERY__other__576037.vid) return false;
      if (randomValue < __GQUERY__other__576037.randomValue) return true;
      if (randomValue > __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  bool operator>(tupleLimitK_1603506567_0 const &__GQUERY__other__576037) const {
      if (vid > __GQUERY__other__576037.vid) return true;
      if (vid < __GQUERY__other__576037.vid) return false;
      if (randomValue > __GQUERY__other__576037.randomValue) return true;
      if (randomValue < __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (vid, randomValue);
   }

};


template <typename TUPLE_t>
class tupleLimitK_1603506567_0Compare {
  public: 
    tupleLimitK_1603506567_0Compare(bool = false) {} 
    bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
      if (lhs.randomValue < rhs.randomValue) {
        return true; 
      }
      if (lhs.randomValue > rhs.randomValue) {
        return false; 
      }
     return false;
    }
};

struct tupleLimitK_1603506567_4 {
  VERTEX vid;
  float randomValue;

  tupleLimitK_1603506567_4() {
    randomValue = 0;
  }

  tupleLimitK_1603506567_4(VERTEX vid_, float randomValue_){
    vid = vid_;
    randomValue = randomValue_;
  }
  operator std::tuple<VERTEX, float>() const {
    return std::make_tuple(vid,randomValue);
  }

  tupleLimitK_1603506567_4(const std::tuple<VERTEX, float>& __GQUERY__other__576037) {
    vid = std::get<0>(__GQUERY__other__576037);
    randomValue = std::get<1>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const tupleLimitK_1603506567_4& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const tupleLimitK_1603506567_4& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  bool operator==(tupleLimitK_1603506567_4 const &__GQUERY__other__576037) const {
    return
      vid == __GQUERY__other__576037.vid &&
      randomValue == __GQUERY__other__576037.randomValue;
  }


  tupleLimitK_1603506567_4& operator+=( const tupleLimitK_1603506567_4& __GQUERY__other__576037) {
      vid= __GQUERY__other__576037.vid;
      randomValue += __GQUERY__other__576037.randomValue;
    return *this;
  }

  friend std::size_t hash_value(const tupleLimitK_1603506567_4& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.vid);
    boost::hash_combine(seed, other.randomValue);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("vid");
      (vid).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("randomValue");
      writer.WriteFloat(randomValue);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(tupleLimitK_1603506567_4 const &__GQUERY__other__576037) const {
      if (vid < __GQUERY__other__576037.vid) return true;
      if (vid > __GQUERY__other__576037.vid) return false;
      if (randomValue < __GQUERY__other__576037.randomValue) return true;
      if (randomValue > __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  bool operator>(tupleLimitK_1603506567_4 const &__GQUERY__other__576037) const {
      if (vid > __GQUERY__other__576037.vid) return true;
      if (vid < __GQUERY__other__576037.vid) return false;
      if (randomValue > __GQUERY__other__576037.randomValue) return true;
      if (randomValue < __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (vid, randomValue);
   }

};


template <typename TUPLE_t>
class tupleLimitK_1603506567_4Compare {
  public: 
    tupleLimitK_1603506567_4Compare(bool = false) {} 
    bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
      if (lhs.randomValue < rhs.randomValue) {
        return true; 
      }
      if (lhs.randomValue > rhs.randomValue) {
        return false; 
      }
     return false;
    }
};

struct tupleLimitK_1603506567_5 {
  VERTEX vid;
  float randomValue;

  tupleLimitK_1603506567_5() {
    randomValue = 0;
  }

  tupleLimitK_1603506567_5(VERTEX vid_, float randomValue_){
    vid = vid_;
    randomValue = randomValue_;
  }
  operator std::tuple<VERTEX, float>() const {
    return std::make_tuple(vid,randomValue);
  }

  tupleLimitK_1603506567_5(const std::tuple<VERTEX, float>& __GQUERY__other__576037) {
    vid = std::get<0>(__GQUERY__other__576037);
    randomValue = std::get<1>(__GQUERY__other__576037);
  }

  friend gutil::GOutputStream& operator<<(gutil::GOutputStream& os, const tupleLimitK_1603506567_5& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  friend std::ostream& operator<<(std::ostream& os, const tupleLimitK_1603506567_5& m) {
    os<<"[";
    os<<"vid "<<m.vid<<"|";
    os<<"randomValue "<<m.randomValue<<"]";
      return os ;
  }


  bool operator==(tupleLimitK_1603506567_5 const &__GQUERY__other__576037) const {
    return
      vid == __GQUERY__other__576037.vid &&
      randomValue == __GQUERY__other__576037.randomValue;
  }


  tupleLimitK_1603506567_5& operator+=( const tupleLimitK_1603506567_5& __GQUERY__other__576037) {
      vid= __GQUERY__other__576037.vid;
      randomValue += __GQUERY__other__576037.randomValue;
    return *this;
  }

  friend std::size_t hash_value(const tupleLimitK_1603506567_5& other) {
    std::size_t seed = 0;
    boost::hash_combine(seed, other.vid);
    boost::hash_combine(seed, other.randomValue);
    return seed;
  }

  void json_printer (gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {

    writer.WriteStartObject();
writer.WriteNameString("vid");
      (vid).json_printer(writer, _request, graphAPI, true);
      writer.WriteNameString("randomValue");
      writer.WriteFloat(randomValue);
      
    writer.WriteEndObject();
  }

  gutil::JSONWriter& json_write_name (
      gutil::JSONWriter& writer, gpelib4::EngineServiceRequest& _request,
      gapi4::UDFGraphAPI* graphAPI, bool verbose = false) const {
    std::string ss = boost::lexical_cast<std::string>(*this);
    return writer.WriteNameString(ss.c_str());
  }

  bool operator<(tupleLimitK_1603506567_5 const &__GQUERY__other__576037) const {
      if (vid < __GQUERY__other__576037.vid) return true;
      if (vid > __GQUERY__other__576037.vid) return false;
      if (randomValue < __GQUERY__other__576037.randomValue) return true;
      if (randomValue > __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  bool operator>(tupleLimitK_1603506567_5 const &__GQUERY__other__576037) const {
      if (vid > __GQUERY__other__576037.vid) return true;
      if (vid < __GQUERY__other__576037.vid) return false;
      if (randomValue > __GQUERY__other__576037.randomValue) return true;
      if (randomValue < __GQUERY__other__576037.randomValue) return false;
      return false;
  }


  template <class ARCHIVE>
   void serialize(ARCHIVE& __GQUERY__ar__576037) {
     __GQUERY__ar__576037 (vid, randomValue);
   }

};


template <typename TUPLE_t>
class tupleLimitK_1603506567_5Compare {
  public: 
    tupleLimitK_1603506567_5Compare(bool = false) {} 
    bool operator() (const TUPLE_t& lhs, const TUPLE_t& rhs) const {
      if (lhs.randomValue < rhs.randomValue) {
        return true; 
      }
      if (lhs.randomValue > rhs.randomValue) {
        return false; 
      }
     return false;
    }
};

struct __GQUERY__Delta__576037 {
   // accumulators:

   // activation flag (computed by select clause in EdgeMap):
   bool __GQUERY__activate__576037;

   // does recipient of this message play role of src/tgt?
   // (set IN EdgeMap, needed for post-accum code in Reduce)
   bool __GQUERY__isSrc__576037, __GQUERY__isTgt__576037, __GQUERY__isOther__576037;

   // default constructor required by engine
   __GQUERY__Delta__576037 () {
      __GQUERY__activate__576037 = false;
      __GQUERY__isSrc__576037    = false;
      __GQUERY__isTgt__576037    = false;
       __GQUERY__isOther__576037  = false;

   }

   // message combinator
   __GQUERY__Delta__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__other__576037) {
      __GQUERY__activate__576037 = __GQUERY__activate__576037 || __GQUERY__other__576037.__GQUERY__activate__576037;
      __GQUERY__isSrc__576037 = __GQUERY__isSrc__576037 || __GQUERY__other__576037.__GQUERY__isSrc__576037;
      __GQUERY__isTgt__576037 = __GQUERY__isTgt__576037 || __GQUERY__other__576037.__GQUERY__isTgt__576037;
       __GQUERY__isOther__576037 =  __GQUERY__isOther__576037 || __GQUERY__other__576037.__GQUERY__isOther__576037;

      return *this;
   }
};

struct __GQUERY__VertexVal__576037 {
   // accumulators:

   // dafault constructor
   __GQUERY__VertexVal__576037 () {}


   // copy constructor
   __GQUERY__VertexVal__576037 (const __GQUERY__VertexVal__576037& __GQUERY__other__576037) {
   }

   // apply (combined) deltas
   __GQUERY__VertexVal__576037& operator+= (const __GQUERY__Delta__576037& __GQUERY__d__576037) {
      return *this;
   }
};


public:

   // These consts are required by the engine.
   static const gpelib4::ValueTypeFlag ValueTypeMode_ =
      gpelib4::Mode_SingleValue;
   static const gpelib4::UDFDefineInitializeFlag InitializeMode_ =
      gpelib4::Mode_Initialize_Globally;
   static const gpelib4::UDFDefineFlag AggregateReduceMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefineFlag CombineReduceMode_ =
      gpelib4::Mode_NotDefined;
   static const gpelib4::UDFDefineFlag VertexMapMode_ =
      gpelib4::Mode_Defined;
   static const gpelib4::UDFDefinePrintFlag PrintMode_ =
      gpelib4::Mode_MultiplePrint_ByVertex;
   static const gpelib4::EngineProcessingMode ProcessingMode_ =
      gpelib4::EngineProcessMode_ActiveVertices;

   // These typedefs are required by the engine.
   typedef __GQUERY__Delta__576037                      MESSAGE;
   typedef __GQUERY__VertexVal__576037                  V_VALUE;
   typedef topology4::VertexAttribute V_ATTR;
   typedef topology4::EdgeAttribute   E_ATTR;

// enums for all user-defined exceptions:
enum __EXCEPT__enum { __EXCEPT__NoneException = 0};



   UDF_caller_vset_dist (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, gpelib4::EngineProcessingMode activateMode) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = false;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = _request.outputwriter_;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    __GQUERY__all_vetex_mode = true;
  } else {
    __GQUERY__all_vetex_mode = false;
  }
      graphupdates = serviceapi.CreateGraphUpdates(&_request);

}




   UDF_caller_vset_dist (gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , gpelib4::EngineProcessingMode activateMode, bool isQueryCalled_) :
gpelib4::BaseUDF(activateMode, INT_MAX), _request(request), _serviceapi(&serviceapi) {
isQueryCalled = isQueryCalled_;
  _request.SetJSONAPIVersion("v2");
__GQUERY__local_writer = &__GQUERY__local_writer_instance;
      if (request.jsoptions_.isMember ("__GQUERY__monitor")) {
         char m = request.jsoptions_["__GQUERY__monitor"][0].asString ()[0];
         if (m == 'A') {
           monitor_all_ = true;
           monitor_ = true;
         } else if (m == 't') {
           monitor_all_ = false;
           monitor_ = true;
         } else {
           monitor_all_ = false;
           monitor_ = false;
         }
      } else {
         monitor_all_ = false;
         monitor_ = false;
      }
       enumMapper_ = serviceapi.GetEnumMappers();
       EnableSharedVertexBucket();
if (request.jsoptions_.isMember ("__FILE_OBJ_SEP__")) {
  file_sep_ = request.jsoptions_["__FILE_OBJ_SEP__"][0].asString ()[0];
}
    if (request.jsoptions_.isMember ("__GQUERY__")
    && request.jsoptions_["__GQUERY__"].isMember ("vTypeIdList")) {
          for (uint32_t i=0; i < request.jsoptions_["__GQUERY__"]["vTypeIdList"].size(); i++) {
                __GQUERY__vts_ += (uint32_t)request.jsoptions_["__GQUERY__"]["vTypeIdList"][i].asUInt();
          }
    } else {
      addAllVertexTypesToSet(__GQUERY__vts_);
    }
__GQUERY__all_vetex_mode = false;
      graphupdates = _graphupdates_;

}

   ~UDF_caller_vset_dist () { if (vvalptr != nullptr) delete vvalptr; vvalptr = nullptr; }



// enum for global var access:
enum GVs {GV_SYS_PC, GV_SYS_tupleLimitK_1603506567_0, GV_GV_val_1, GV_SYS_tupleLimitK_1603506567_4, GV_SYS_tupleLimitK_1603506567_5, GV_GV_val2_1, FE_GV_12_SRC, FE_GV_12, FE_GV_12_NULL, MONITOR, MONITOR_ALL, GV_SYS_OLD_PC, GV_SYS_EXCEPTION, GV_SYS_TO_BE_COMMITTED, GV_SYS_LAST_ACTIVE, GV_SYS_EMPTY_INITIALIZED, GV_SYS_VTs, GV_SYS_vset2_SIZE, GV_SYS_vset2_ORDERBY, GV_SYS_vset2_LASTSET, GV_SYS_vset3_SIZE, GV_SYS_vset3_ORDERBY, GV_SYS_vset3_LASTSET, GV_SYS_vset_SIZE, GV_SYS_vset_ORDERBY, GV_SYS_vset_LASTSET};

void Initialize_GlobalVariables (gpelib4::GlobalVariables* gvs) {
  // program counter
  gvs->Register(GV_SYS_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_OLD_PC, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_EXCEPTION, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_TO_BE_COMMITTED, new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_LAST_ACTIVE,new gpelib4::StateVariable<std::string>(""));
  gvs->Register(GV_SYS_EMPTY_INITIALIZED,new gpelib4::StateVariable<bool>(false));
  gvs->Register(GV_SYS_vset2_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vset2_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vset2_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vset3_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vset3_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vset3_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vset_SIZE, new gpelib4::StateVariable<uint32_t>(0));
  gvs->Register(GV_SYS_vset_ORDERBY,new gpelib4::StateVariable<int32_t>(-1));
  gvs->Register(GV_SYS_vset_LASTSET,new gpelib4::StateVariable<uint32_t>(0));
   ListAccum<int64_t >  i_12;
   gvs->Register ( FE_GV_12, new gpelib4::StateVariable<decltype(i_12.begin())> (i_12.end()) );
   gvs->Register ( FE_GV_12_NULL, new gpelib4::StateVariable<decltype(i_12.begin())> (i_12.end()) );
   gvs->Register ( FE_GV_12_SRC, new gpelib4::StateVariable<decltype(i_12)> (i_12) );
  gvs->Register(GV_SYS_VTs,new gpelib4::StateVariable<SetAccum<uint32_t> >(__GQUERY__vts_));

   // job params

   // global variables
   gvs->Register (GV_GV_val_1, new gpelib4::StateVariable<int64_t> (int64_t()));
   gvs->Register (GV_GV_val2_1, new gpelib4::StateVariable<int64_t> (int64_t()));

   // loop indices

   //limit k gv heap
   gvs->Register (GV_SYS_tupleLimitK_1603506567_0, new gpelib4::SumVariable<SetAccum<VertexLocalId_t> >(SetAccum<VertexLocalId_t>()));
   gvs->Register (GV_SYS_tupleLimitK_1603506567_4, new gpelib4::SumVariable<SetAccum<VertexLocalId_t> >(SetAccum<VertexLocalId_t>()));
   gvs->Register (GV_SYS_tupleLimitK_1603506567_5, new gpelib4::SumVariable<SetAccum<VertexLocalId_t> >(SetAccum<VertexLocalId_t>()));

   // global accs

   //monitoring
   gvs->Register (MONITOR, new gpelib4::StateVariable<bool>(monitor_));
   gvs->Register (MONITOR_ALL, new gpelib4::StateVariable<bool>(monitor_all_));
}


void (UDF_caller_vset_dist::*write) (gvector<gutil::GOutputStream*>&   ostream,
                      const VERTEX& v,
                      V_ATTR*           v_attr,
                      const V_VALUE&     v_val,
                      gpelib4::GlobalVariableContext* context);


ALWAYS_INLINE
void Write(gvector<gutil::GOutputStream*>&   ostream,
            const VertexLocalId_t& v,
            V_ATTR*           v_attr,
            const V_VALUE&     v_val,
            gpelib4::GlobalVariableContext* context) {
    // ignore all PRINTs in the monitor mode
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      return;
    }
    (this->*(write))(ostream, VERTEX(v), v_attr, v_val, context);
}
void WriteSummaryVis () {
  pthread_mutex_lock(&jsonWriterLock);
  auto final_code = finally([&] { pthread_mutex_unlock(&jsonWriterLock); });
  timer_.PrintVisualizeSummary(*__GQUERY__local_writer);
}
void VertexMap_0 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function VertexMap_0 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(src) == _schema_VTY_company)) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function VertexMap_0 src: " << src << std::endl;
}

void Reduce_0 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function Reduce_0 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):

   //attributes' local var declaration




   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   //push limit k to global set
   if (__GQUERY__activate__576037 && delta.__GQUERY__isSrc__576037) {
     context->GlobalVariable_Reduce<SetAccum<VertexLocalId_t>>(GV_SYS_tupleLimitK_1603506567_0, SetAccum<VertexLocalId_t>(v));
   }

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function Reduce_0 v: " << v << std::endl;
}
void VertexMap_4 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function VertexMap_4 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(src) == _schema_VTY_members)) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function VertexMap_4 src: " << src << std::endl;
}

void Reduce_4 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function Reduce_4 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):

   //attributes' local var declaration




   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   //push limit k to global set
   if (__GQUERY__activate__576037 && delta.__GQUERY__isSrc__576037) {
     context->GlobalVariable_Reduce<SetAccum<VertexLocalId_t>>(GV_SYS_tupleLimitK_1603506567_4, SetAccum<VertexLocalId_t>(v));
   }

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function Reduce_4 v: " << v << std::endl;
}
void VertexMap_5 (const VERTEX& src,
                  V_ATTR*                src_attr,
                  const V_VALUE&         src_val,
                  gpelib4::SingleValueMapContext<MESSAGE>* context) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function VertexMap_5 src: " << src << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // FROM
   if (!(context->GraphAPI()->GetVertexType(src) == _schema_VTY_company)) return;


   // prepare messages
   __GQUERY__Delta__576037 src_delta = __GQUERY__Delta__576037 ();
   src_delta.__GQUERY__isSrc__576037 = true;

   // SELECT
   src_delta.__GQUERY__activate__576037 = true;


   // send messages
   context->Write (src, src_delta);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function VertexMap_5 src: " << src << std::endl;
}

void Reduce_5 (const VERTEX&                 v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request) {
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function Reduce_5 v: " << v << std::endl;
   if (UNLIKELY(!_request.TimeOutCheck() || _request.IsAbort())) {
     std::string msg("Aborted due to timeout or system memory in critical state.");
     HF_set_error(_request, msg, true);
     return;
   }

   // Note that v_val is a const reference so it cannot be modified.
   // Make local non-const copy of v_val (using copy constructor) first
   // then apply delta to it (using += operator):

   //attributes' local var declaration




   // Will this vertex be active in next iteration?
   // This depends on both whether select clause said so (communicated in delta)
   // and whether the new vertex value passes the having clause filter (if any):
   bool __GQUERY__activate__576037 = delta.__GQUERY__activate__576037;

   //push limit k to global set
   if (__GQUERY__activate__576037 && delta.__GQUERY__isSrc__576037) {
     context->GlobalVariable_Reduce<SetAccum<VertexLocalId_t>>(GV_SYS_tupleLimitK_1603506567_5, SetAccum<VertexLocalId_t>(v));
   }

   if (__GQUERY__activate__576037&&!__GQUERY__all_vetex_mode) context->SetActiveFlag(v);
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Exit function Reduce_5 v: " << v << std::endl;
}


void (UDF_caller_vset_dist::*edgemap) (const VERTEX& src,
                 V_ATTR*                src_attr,
                 const V_VALUE&         src_val,
                 const VERTEX& tgt,
                 V_ATTR*                tgt_attr,
                 const V_VALUE&         tgt_val,
                 E_ATTR*                e_attr,
                 gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void EdgeMap (const VertexLocalId_t& src,
              V_ATTR*                src_attr,
              const V_VALUE&         src_val,
              const VertexLocalId_t& tgt,
              V_ATTR*                tgt_attr,
              const V_VALUE&         tgt_val,
              E_ATTR*                e_attr,
              gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(edgemap))(VERTEX(src), src_attr, src_val, VERTEX(tgt), tgt_attr, tgt_val, e_attr, ctx);
}



void (UDF_caller_vset_dist::*vertexmap) (const VERTEX& src,
                   V_ATTR*                src_attr,
                   const V_VALUE&         src_val,
                   gpelib4::SingleValueMapContext<MESSAGE>* context);

ALWAYS_INLINE void VertexMap (const VertexLocalId_t& src,
                V_ATTR*                src_attr,
                const V_VALUE&         src_val,
                gpelib4::SingleValueMapContext<MESSAGE>* ctx) {

   (this->*(vertexmap)) (VERTEX(src), src_attr, src_val, ctx);
}



void (UDF_caller_vset_dist::*reduce) (const VERTEX&                v,
                V_ATTR*                               v_attr,
                const V_VALUE&                        v_val,
                MESSAGE&                              delta,
                gpelib4::SingleValueContext<V_VALUE>* context,
                gpelib4::EngineServiceRequest&                 request);

ALWAYS_INLINE void Reduce (const VertexLocalId_t&       v,
                           V_ATTR*                      v_attr,
                           const V_VALUE&               v_val,
                           MESSAGE&                     delta,
                           gpelib4::SingleValueContext<V_VALUE>* context) {

    (this->*(reduce))(VERTEX(v), v_attr, v_val, delta, context, _request);
}

void BeforeIteration (gpelib4::MasterContext* context) {
   uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC);
uint64_t __loop_count__= 0; // for infinite loop timeout check
   GCOUT(Verbose_FrameworkHigh) << "[UDF_caller_vset_dist INFO] " << "Enter function BeforeIteraion pc: " << PC << std::endl;
  if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
    if (PC == 0) timer_.begin("caller_vset_dist");
  }
   while (true) {
      if (_request.error_) {
        context->Abort();
        return;
      }
      gindex::IndexHint emptyIndex;
      context->SetSrcIndexHint(std::move(emptyIndex));
      context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC) = PC;
      GCOUT(Verbose_FrameworkLow) << "[UDF_caller_vset_dist DEBUG] " << "In BeforeIteraion switch PC " << PC << std::endl;
      switch (PC) {
         case 0:
         {
                 if (context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) == false) {
                     context->StoreBitSets("1EMPTY", *context->GetBitSets());
                     context->GlobalVariable_GetValue<bool> (GV_SYS_EMPTY_INITIALIZED) = true;
                 }

                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_company,true);

                 context->GetBitSets()->Limit(5l);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vset";
                 PC = 1;
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset_ORDERBY) = -1;
           PC = 1; break;
         }

         case 1:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) != "vset") {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } else {
                     __GQUERY__bs_vset = *context->GetBitSets();
                 }
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                     call_q_subquery_vset(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 5l));
                     PC = 2;
                     break;

           PC = 2; break;
         }

         case 2:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) != "vset") {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } else {
                     __GQUERY__bs_vset = *context->GetBitSets();
                 }
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)=( call_q_subquery_vset(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 5l)));
                 PC = 3;
                 break;
           PC = 3; break;
         }

         case 3:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("val");
writer.WriteInt(( context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)));

                 } //END
                 writer.WriteEndObject();
                 PC = 4;
                 break;

           PC = 4; break;
         }

         case 4:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") {
                     context->StoreBitSets("vset", *context->GetBitSets());
                 }

                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_members,true);

                 context->GetBitSets()->Limit(4l);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vset2";
                 PC = 5;
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset2_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset2_ORDERBY) = -1;
           PC = 5; break;
         }

         case 5:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset2") {
                     context->StoreBitSets("vset2", *context->GetBitSets());
                 }

                 if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
                 if(!__GQUERY__all_vetex_mode)context->SetActiveFlagByType(_schema_VTY_company,true);

                 context->GetBitSets()->Limit(4l);

                 context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) = "vset3";
                 PC = 6;
                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset3_SIZE) = context->CalcActiveVertexCount();
                 context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset3_ORDERBY) = -1;
           PC = 6; break;
         }

         case 6:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset3") {
                     context->StoreBitSets("vset3", *context->GetBitSets());
                 }
                 gutil::BitSets __GQUERY__bs_vset2;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset2") { 
                     __GQUERY__bs_vset2 = *context->GetBitSets();
                 } else if (context->HasBitSets("vset2")) {
                     context->LoadBitSets("vset2", __GQUERY__bs_vset2);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset2);
                 } 

                 gutil::BitSets __GQUERY__bs_vset3;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset3") { 
                     __GQUERY__bs_vset3 = *context->GetBitSets();
                 } else if (context->HasBitSets("vset3")) {
                     context->LoadBitSets("vset3", __GQUERY__bs_vset3);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset3);
                 } 

                 gutil::BitSets __GQUERY__bs_vset;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 SetAccum<VERTEX> __GQUERY__uset_vset2 = __GQUERY__bs_vset2.ToSet(*_serviceapi->GetTopologyMeta());
                 SetAccum<VERTEX> __GQUERY__uset_vset3 = __GQUERY__bs_vset3.ToSet(*_serviceapi->GetTopologyMeta());
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)=( (call_q_subquery_vset_2(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( __GQUERY__uset_vset2), ( 5l))+call_q_subquery_vset(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset3), ( 5l))));
                 PC = 7;
                 break;
           PC = 7; break;
         }

         case 7:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("val");
writer.WriteInt(( context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)));

                 } //END
                 writer.WriteEndObject();
                 PC = 8;
                 break;

           PC = 8; break;
         }

         case 8:
         {
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset3") {
                     context->StoreBitSets("vset3", *context->GetBitSets());
                 }
                 gutil::BitSets __GQUERY__bs_vset2;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset2") { 
                     __GQUERY__bs_vset2 = *context->GetBitSets();
                 } else if (context->HasBitSets("vset2")) {
                     context->LoadBitSets("vset2", __GQUERY__bs_vset2);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset2);
                 } 

                 gutil::BitSets __GQUERY__bs_vset3;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset3") { 
                     __GQUERY__bs_vset3 = *context->GetBitSets();
                 } else if (context->HasBitSets("vset3")) {
                     context->LoadBitSets("vset3", __GQUERY__bs_vset3);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset3);
                 } 

                 gutil::BitSets __GQUERY__bs_vset;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 SetAccum<VERTEX> __GQUERY__uset_vset2 = __GQUERY__bs_vset2.ToSet(*_serviceapi->GetTopologyMeta());
                 SetAccum<VERTEX> __GQUERY__uset_vset3 = __GQUERY__bs_vset3.ToSet(*_serviceapi->GetTopologyMeta());
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val2_1)=( (call_q_subquery_vset_2(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( __GQUERY__uset_vset2), ( 5l))+call_q_subquery_vset(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset3), ( 5l))));
                 PC = 9;
                 break;
           PC = 9; break;
         }

         case 9:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("val2");
writer.WriteInt(( context->GlobalVariable_GetValue<int64_t> (GV_GV_val2_1)));

                 } //END
                 writer.WriteEndObject();
                 PC = 10;
                 break;

           PC = 10; break;
         }

         case 10:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 { // loop start
                   if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 10l))))) {
                     PC = 12;
                       break;
                   }
if (UNLIKELY((++__loop_count__ & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  context->Abort();
  return;
}
                 }

           PC = 11; break;
         }

         case 11:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 1l);
                 PC = 10;
                 break;
           PC = 12; break;
         }

         case 12:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 { // foreach start
                   if (context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12) == context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12_NULL)) {
context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))))> (FE_GV_12_SRC) = call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset));
                     context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12) = (context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))))> (FE_GV_12_SRC)).begin();
                   } else {
                     context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12)++;
                   }
                   if (!(context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12)!=(context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))))> (FE_GV_12_SRC)).end())) {
                     context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12) = context->GlobalVariable_GetValue<decltype((call_q_subquery_vset_4(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset))).begin())> (FE_GV_12_NULL);
                     PC = 17;
                     break;
                   }
if (UNLIKELY((++__loop_count__ & GSQL_UTIL::memory_checkcount) == 0) && (!_request.TimeOutCheck() || _request.IsAbort())) {
  std::string msg("Aborted due to timeout or system memory in critical state.");
  HF_set_error(_request, msg, true);
  context->Abort();
  return;
}
                 }

           PC = 13; break;
         }

         case 13:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( (*context->GlobalVariable_GetValue<decltype(( __GSQL_INNER_VAR_12).begin())> (FE_GV_12)));
                 PC = 14;
                 break;
           PC = 14; break;
         }

         case 14:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 1l);
                 PC = 15;
                 break;
           PC = 15; break;
         }

         case 15:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( (*context->GlobalVariable_GetValue<decltype(( __GSQL_INNER_VAR_12).begin())> (FE_GV_12)));
                 PC = 16;
                 break;
           PC = 16; break;
         }

         case 16:
         {
                 gutil::BitSets __GQUERY__bs_vset;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( call_q_subquery_vset(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 0l)));
                 PC = 12;
                 break;
           PC = 17; break;
         }

         case 17:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("val");
writer.WriteInt(( context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)));

                 } //END
                 writer.WriteEndObject();
                 PC = 18;
                 break;

           PC = 18; break;
         }

         case 18:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 0l))))) {
                   PC = 20;
                   break;
                 }
           PC = 19; break;
         }

         case 19:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 1l);
                 PC = 24;
                 break;
           PC = 20; break;
         }

         case 20:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 10l))))) {
                   PC = 22;
                   break;
                 }
           PC = 21; break;
         }

         case 21:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 2l);
                 PC = 24;
                 break;
           PC = 22; break;
         }

         case 22:
         {
                 gutil::BitSets __GQUERY__bs_vset3;
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) != "vset3") {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset3);
                 } else {
                     __GQUERY__bs_vset3 = *context->GetBitSets();
                 }
                 SetAccum<VERTEX> __GQUERY__uset_vset3 = __GQUERY__bs_vset3.ToSet(*_serviceapi->GetTopologyMeta());

                 if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset3), ( 20l))))) {
                   PC = 24;
                   break;
                 }
           PC = 23; break;
         }

         case 23:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 3l);
                 PC = 24;
                 break;
           PC = 24; break;
         }

         case 24:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 0l))))) {
                   PC = 26;
                   break;
                 }
           PC = 25; break;
         }

         case 25:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 1l);
                 PC = 28;
                 break;
           PC = 26; break;
         }

         case 26:
         {
                 gutil::BitSets& __GQUERY__bs_vset = *context->GetBitSets();
                 if (context->GlobalVariable_GetValue<std::string> (GV_SYS_LAST_ACTIVE) == "vset") { 
                     __GQUERY__bs_vset = *context->GetBitSets();
                 } else if (context->HasBitSets("vset")) {
                     context->LoadBitSets("vset", __GQUERY__bs_vset);
                 } else {
                     context->LoadBitSets("1EMPTY", __GQUERY__bs_vset);
                 } 

                 *context->GetBitSets() = __GQUERY__bs_vset;
                 SetAccum<VERTEX> __GQUERY__uset_vset = __GQUERY__bs_vset.ToSet(*_serviceapi->GetTopologyMeta());

                 if (!(( call_q_subquery_vset_3(context->GraphAPI(), _request, *_serviceapi, graphupdates , ( __GQUERY__uset_vset), ( 10l))))) {
                   PC = 28;
                   break;
                 }
           PC = 27; break;
         }

         case 27:
         {

                 context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)+=( 2l);
                 PC = 28;
                 break;
           PC = 28; break;
         }

         case 28:
         {

                 context->GlobalVariable_GetValue<uint32_t> (GV_SYS_PC) = PC;
                 gutil::JSONWriter& writer = *__GQUERY__local_writer;
                 writer.WriteStartObject();
                 { //START
writer.WriteNameString("val");
writer.WriteInt(( context->GlobalVariable_GetValue<int64_t> (GV_GV_val_1)));

                 } //END
                 writer.WriteEndObject();
                 PC = 29;
                 break;

           PC = 29; break;
         }

         case 29:
         {
                 uint32_t exceptCode = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_EXCEPTION);
                 if (exceptCode != __EXCEPT__NoneException) {
                     _request.error_ = true;
                     _request.SetErrorMessage(raisedErrorMsg_);
                     _request.SetErrorCode(boost::lexical_cast<string>(exceptCode));
                     context->Abort();
                     return;
                 } else {
                     context->Stop ();
                     return;
                 }
         }
      }
   }
}

void AfterIteration (gpelib4::MasterContext* context) {
    uint32_t PC = context->GlobalVariable_GetValue<uint32_t> (GV_SYS_OLD_PC);
    switch (PC) {
     case 0:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset_ORDERBY) = -1;
      // random activate k vertices from global limit set
      if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
      int64_t start = 0;
      int64_t end = 5l;
      int64_t size = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_0).size();
      if (size > 5l) {
        start = (int64_t)std::rand() % size;
        end += start;
      }
      int64_t i = 0;
      for(auto it = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_0).begin(); it != context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_0).end(); ++it){
        bool pick = (start <= i && i < end)
                    || (start <= i + size && i + size < end);
        if(pick && !__GQUERY__all_vetex_mode) context->SetActiveFlag(*it);
        i += 1;
      }
context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_0).clear();
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 4:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset2_ORDERBY) = -1;
      // random activate k vertices from global limit set
      if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
      int64_t start = 0;
      int64_t end = 4l;
      int64_t size = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_4).size();
      if (size > 4l) {
        start = (int64_t)std::rand() % size;
        end += start;
      }
      int64_t i = 0;
      for(auto it = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_4).begin(); it != context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_4).end(); ++it){
        bool pick = (start <= i && i < end)
                    || (start <= i + size && i + size < end);
        if(pick && !__GQUERY__all_vetex_mode) context->SetActiveFlag(*it);
        i += 1;
      }
context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_4).clear();
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset2_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     case 5:
     {
context->GlobalVariable_GetValue<int32_t> (GV_SYS_vset3_ORDERBY) = -1;
      // random activate k vertices from global limit set
      if(!__GQUERY__all_vetex_mode)context->SetAllActiveFlag(false);
      int64_t start = 0;
      int64_t end = 4l;
      int64_t size = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_5).size();
      if (size > 4l) {
        start = (int64_t)std::rand() % size;
        end += start;
      }
      int64_t i = 0;
      for(auto it = context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_5).begin(); it != context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_5).end(); ++it){
        bool pick = (start <= i && i < end)
                    || (start <= i + size && i + size < end);
        if(pick && !__GQUERY__all_vetex_mode) context->SetActiveFlag(*it);
        i += 1;
      }
context->GlobalVariable_GetValue<SetAccum<VertexLocalId_t> >(GV_SYS_tupleLimitK_1603506567_5).clear();
         context->GlobalVariable_GetValue<uint32_t> (GV_SYS_vset3_SIZE) = context->CalcActiveVertexCount();
         break;
     }

     default:
       break;
    }
    if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
      uint64_t edgeCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ProcessedEdgeCount()->Value();
      uint64_t vertexCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->VertexMapCount()->Value();
      uint64_t reduceCnt = gpelib4::BasicContext_FriendHelper::GetGlobalVariables(context)->ReduceVertexCount()->Value();
      timer_.finish(edgeCnt, vertexCnt, reduceCnt, context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
    }
}

ALWAYS_INLINE void Initialize (gpelib4::GlobalSingleValueContext<V_VALUE>* context) {

  if (_request.error_) {
    context->Abort();
    return;
  }
  _request.SetJSONAPIVersion("v2");

   // vertex acc initialization:
   V_VALUE vval = V_VALUE ();
   context->WriteAll (vval, false);

   vvalptr = new V_VALUE(vval);

   this->writeAllValue_ = (void*)vvalptr;

   pthread_mutex_init(&jsonWriterLock, NULL);

   // writing
   __GQUERY__local_writer->WriteStartArray();
}


void EndRun(gpelib4::BasicContext* context) {
if (!isQueryCalled) {
if (context->IsAborted() || _request.error_) {
  graphupdates->Rollback(true);
} else {
  _request.ResetQueryState();
  _serviceapi->QueryResponse_VIdtoUId(&_request);
  if (!graphupdates->Commit(true)) {
    throw gutil::GsqlException("Commit may not succeed.", gutil::error_t::E_OTHER_EXCP);
  }
}
}
                 if (context->GlobalVariable_GetValue<bool> (MONITOR)) {
                   timer_.end();
                   timer_.PrintSummary(context->GlobalVariable_GetValue<bool> (MONITOR_ALL));
                   WriteSummaryVis();
                 }
    pthread_mutex_destroy(&jsonWriterLock);
    __GQUERY__local_writer->WriteEndArray();
}

private:

   gpelib4::EngineServiceRequest& _request;

   gperun::ServiceAPI* _serviceapi;

   topology4::GraphUpdatesPointer graphupdates;
   bool isQueryCalled;// true if this is a sub query
   pthread_mutex_t jsonWriterLock;
   string raisedErrorMsg_;
   gse2::EnumMappers* enumMapper_;
   bool monitor_;
   bool monitor_all_;
   char file_sep_ = ',';
   __GQUERY__Timer timer_;
   bool __GQUERY__all_vetex_mode;
   gutil::JSONWriter* __GQUERY__local_writer;
   gutil::JSONWriter __GQUERY__local_writer_instance;
   SetAccum<uint32_t> __GQUERY__vts_;
const int _schema_VTY_company = 0;
const int _schema_VTY_members = 1;
   V_VALUE* vvalptr = nullptr;
 // use these for simplifying foreach item definition
ListAccum<int64_t >  __GSQL_INNER_VAR_12;
};

  bool call_caller_vset_dist(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) {
  try {
    gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
    if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
      activateMode = gpelib4::EngineProcessMode_AllVertices;
      GCOUT(0) << "launching query caller_vset_dist in all vetext active mode." << std::endl;
    }
    UDIMPL::poc_graph::UDF_caller_vset_dist udf(request, serviceapi, activateMode);
    if (request.error_) return false;

    serviceapi.RunUDF(&request, &udf);
    if (udf.abortmsg_.size() > 0) {
      HF_set_error(request, udf.abortmsg_, true, true);
    }
  } catch (gutil::GsqlException& e) {
    request.SetErrorMessage(e.msg());
    request.SetErrorCode(e.code());
    request.error_ = true;
  } catch (const boost::exception& bex) {
    GUDFInfo((true ? 0 : 0xffff), "caller_vset_dist") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " +
                             boost::diagnostic_information(bex));
    request.SetErrorCode(8001);
    request.error_ = true;
  } catch (const std::runtime_error& ex) {
    GUDFInfo((true ? 0 : 0xffff), "caller_vset_dist") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8002);
    request.error_ = true;
  } catch (const std::exception& ex) {
    GUDFInfo((true ? 0 : 0xffff), "caller_vset_dist") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error " + std::string(ex.what()));
    request.SetErrorCode(8003);
    request.error_ = true;
  } catch (...) {
    GUDFInfo((true ? 0 : 0xffff), "caller_vset_dist") << request.requestid_ << "|"  << "get an exception";
    request.SetErrorMessage("unexpected error ");
    request.SetErrorCode(8999);
    request.error_ = true;
  }
  return !request.error_;
}
  void call_q_caller_vset_dist(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ ) {
gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  UDIMPL::poc_graph::UDF_caller_vset_dist udf(request, serviceapi, _graphupdates_ , activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);

  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
}
  bool call_caller_vset_dist_worker(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi) { return false; }

  void call_q_caller_vset_dist(gapi4::UDFGraphAPI* graphAPI, gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, topology4::GraphUpdatesPointer _graphupdates_ , MapAccum<std::tuple<size_t,uint32_t,uint32_t>, uint32_t>* _mapaccum ){
// This query is called as a subquery, where parameter _mapaccum is used to collect universal edge insertion failure info;
call_q_caller_vset_dist(graphAPI, request, serviceapi,_graphupdates_ );
}
  bool call_caller_vset_dist(gpelib4::EngineServiceRequest& request, gperun::ServiceAPI& serviceapi, const gvector<interpret::ValuePtr>& values,gvector<interpret::ValuePtr>& returns,UDFGraphAPIPointer graphAPI, GraphUpdatesPointer graphupdates) {
  gpelib4::EngineProcessingMode activateMode = gpelib4::EngineProcessMode_ActiveVertices;
  if (request.jsoptions_.isMember("__GQUERY__USING_ALL_ACTIVE_MODE") && request.jsoptions_["__GQUERY__USING_ALL_ACTIVE_MODE"][0].asString()[0] == 't') {
    activateMode = gpelib4::EngineProcessMode_AllVertices;
    GCOUT(0) << "launching query caller_vset_dist in all vetext active mode." << std::endl;
  }

if (values.size() != 0) {
    HF_set_error(request, "Invalid parameter size: 0|" + boost::lexical_cast<std::string>(values.size()), true, true);
return false;
 }

  UDIMPL::poc_graph::UDF_caller_vset_dist udf(request, serviceapi, graphupdates, activateMode, true);

  if (request.error_) throw gutil::GsqlException(request.GetErrorMessage());
  serviceapi.RunUDF(&request, &udf);


  if (udf.abortmsg_.size() > 0) {
    HF_set_error(request, udf.abortmsg_, true, true);
  }
  return !request.error_;
}

}

}
