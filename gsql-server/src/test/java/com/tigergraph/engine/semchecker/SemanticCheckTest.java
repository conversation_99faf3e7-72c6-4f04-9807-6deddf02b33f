package com.tigergraph.engine.semchecker;

import com.tigergraph.engine.TestCase;
import com.tigergraph.engine.codegen.compileFlags;
import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.plan.policy.Policy;
import com.tigergraph.schema.plan.query.QuerySignature;

import java.util.Arrays;
import java.util.List;

import org.junit.Test;

import static org.junit.Assert.*;
import static org.hamcrest.CoreMatchers.startsWith;

public class SemanticCheckTest extends TestCase {
  /**
   * Test case for the following ticket
   * {@link https://graphsql.atlassian.net/browse/GLE-1145}
   *
   * This test case checks whether += operator is disallowed for
   * global variable.
   */
  @Test
  public void testGlobalVariableCombineOperator() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test_gle1145() for graph empty_graph {",
      "  int gvar = 0;",
      "  foreach i in range[0,4] do",
      "    gvar += 1; // support in GLE-2461",
      "  end;",
      "  print gvar;",
      "}"
    };

    // compile the query - should succeed
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNotNull(qs);
    assertNull(errMsg);
    assertEquals("test_gle1145", qs.Name);
    assertEquals("empty_graph", qs.GraphName);
  }

  @Test
  public void testUserDefinedExceptionErrorCodeCheck() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testRFC163n0() for graph empty_graph {",
      "  exception A(1);",
      "  print \"Good\";",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String[] errLines = {
      "\nSemantic Check Error in query testRFC163n0 (SEM-1407): line 2, col 12",
      "For user-defined exception, error code must be more than 40000!"
    };
    String err = String.join("\n", errLines);
    assertEquals(err, errMsg);
  }

  @Test
  public void testDuplicateExceptionCode() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testRFC163n1() for graph empty_graph {",
      "  exception A(40001);",
      "  exception B(40001);",
      "  print \"Good\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testRFC163n1 (SEM-1408): line 3, col 12"
               + "\nDuplicate error code 40001 in user-defined exceptions!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testRaiseUndefinedException() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testRFC163n2() for graph empty_graph {",
      "  try",
      "    raise A(\"Raise A\");",
      "  exception",
      "    when A then print \"Handle A\";",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testRFC163n2 (SEM-1409): line 3, col 10"
               + "\nException name A hasn't been defined!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testCatchUndefinedException() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testRFC163n3() for graph empty_graph {",
      "  exception A(40001);",
      "  try",
      "    raise A(\"Raise A\");",
      "  exception",
      "    when B then print \"Handle B\";",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testRFC163n3 (SEM-1409): line 6, col 9"
               + "\nException name B hasn't been defined!";
    assertEquals(err, errMsg);
  }

  /**
   * Test case for the following ticket:
   * {@link https://graphsql.atlassian.net/browse/GLE-3998}
   *
   * This test case ensures that exception code should be unique.
   * Currently exceptions are still global and unique, this could be removed if we make them scoped
   */
  @Test
  public void testDuplicateExceptionCodeWithScopeCode() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testGLE3998n1(int a) for graph empty_graph {",
      "  exception A(40001);",
      "  if a > 1 then",
      "    exception B(40001);",
      "  end;",
      "  print \"Good\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE3998n1 (SEM-1408): line 4, col 14"
               + "\nDuplicate error code 40001 in user-defined exceptions!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testDuplicateScopeExceptionCodeWithExternalCode() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testGLE3998n2(int a) for graph empty_graph {",
      "  if a > 1 then",
      "    exception A(40001);",
      "  end;",
      "  exception B(40001);",
      "  print \"Good\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE3998n2 (SEM-1408): line 5, col 12"
               + "\nDuplicate error code 40001 in user-defined exceptions!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testDuplicateExceptionCodeTwoScopeCode() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query testGLE3998n3(int a) for graph empty_graph {",
      "  if a > 1 then",
      "    exception A(40001);",
      "  else",
      "    exception B(40001);",
      "  end;",
      "  print \"Good\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE3998n3 (SEM-1408): line 5, col 14"
               + "\nDuplicate error code 40001 in user-defined exceptions!";
    assertEquals(err, errMsg);
  }

  /**
   * Test case for the following ticket:
   * {@link https://graphsql.atlassian.net/browse/GF-619}
   *
   * This test case ensures that when assigning to an undefined global variable,
   * the variable is not mis-recognized as a vertex set variable.
   */
  @Test
  public void testAssignmentToUndefinedGlobalVar() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query n1() for graph poc_graph {",
      "  int maxIteration = 5;",
      "  T2 = { ANY };",
      "  T2 = select v from T2:v-(:e)-:t;",
      "  while true limit maxIteration do",
      "    T2 = select s from T2:s-(:e)-:t;",
      "    ILU_result = to_string(12.0);  // undefined var",
      "    T2 = select s from T2:s;",
      "    while true limit maxIteration do",
      "      T2 = select t from T2:t-(:e)-:s;",
      "      T2 = select s from T2:s;",
      "    end;",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n1 (SEM-234): line 7, col 4"
        + "\nAn undefined identifier 'ILU_result' in the current scope.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testWriteVertexAttrInAccum() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query n1() for graph poc_graph syntax v1 {",
      "  SetAccum<vertex<members>> @@vs;",
      "  Start = { members.* };",
      "  S = select u from Start-(:e)-members:u",
      "        accum",
      "          @@vs += u,",
      "          foreach i in @@vs",
      "          do",
      "            i.id = \"hello\"",
      "          end",
      "          ;",
      "  print @@vs;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n1 (SEM-140): line 9, col 12"
        + "\n'i.id = \"hello\"' is not allowed because vertex attribute cannot be updated in"
        + "\nthe ACCUM clause. Please use the POST-ACCUM clause instead. To use edge"
        + "\nattributes for a vertex attribute update, first put them in an accumulator in"
        + "\nthe ACCUM clause, and then use the accumulator in the POST-ACCUM clause.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testInvalidVertexTypeNameInParam() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query n2 (vertex<unknown> seed) for graph poc_graph {",
      "  print \"Yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    // TODO: same error as TYP-152
    String err = "\nSemantic Check Error in query n2 (SEM-7): line 1, col 24"
        + "\nThe vertex type \"unknown\" does not exist in the graph schema!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testInvalidEdgeTypeNameInAccumDecl() {
    Setup("poc_graph");
    String query = String.join("\n",
        "create query test27() for graph poc_graph {",
        "  SetAccum<edge<company>> @@something;",
        "  print @@something;",
        "}"
    );
    String err = "\nThe edge type \"company\" does not exist in the graph schema!";
    assertNull(compile(query));
    assertError(errMsg, "SEM-7", err);
  }

  @Test
  public void testLoopVariableModifiedInDMLBlock() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query n6() for graph poc_graph {",
      "  SetAccum<int> @@acc;",
      "  Seed = { members.* };",
      "  Seed =",
      "    select s",
      "    from Seed:s",
      "    accum foreach i in @@acc do",
      "            i = 0",
      "          end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n6 (SEM-1202): line 8, col 12"
        + "\nModifying the loop variable 'i' inside a DML block is not allowed.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testLoopVariableOfSetAccumModifiedStatementLevel() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n9() for graph empty_graph syntax v1 {",
      "  SetAccum<int> @@acc;",
      "  foreach i in @@acc do",
      "    i = 10;",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n9 (SEM-1203): line 4, col 4"
        + "\nThe content of SetAccum cannot be modified in a FOREACH statement.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testLoopKeyOfMapAccumModifiedStatementLevel() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n10() for graph empty_graph syntax v1 {",
      "  MapAccum<int, string> @@acc;",
      "  foreach (k, v) in @@acc do",
      "    k = 10;",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n10 (SEM-1203): line 4, col 4"
        + "\nThe key of MapAccum cannot be modified in a FOREACH statement.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testInsertVertex() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // test 1 - key value count mismatch
    {
      query = ""
          + "create query test12 (string pid, int val) for graph poc_graph {\n"
          + "  insert into company (PRIMARY_ID) values (pid, val);\n"
          + "}";
      err = "\nSemantic Check Error in query test12 (SEM-227): line 2, col 22"
          + "\n'(PRIMARY_ID)' has 1 entries, but '(pid, val)' has 2 entries. They are not"
          + "\nconsistent.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - attribute value count mismatch (statement level)
    {
      query = ""
          + "create query test21 (string pid, int val) for graph poc_graph {\n"
          + "  insert into company values (pid, \"com\", val);\n"
          + "}";
      err = "\nSemantic Check Error in query test21 (SEM-230): line 2, col 2"
          + "\nVertex 'company' has 3 attributes, but 2 values are provided.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - attribute value count mismatch (block level)
    {
      query = ""
          + "create query test21_2 (string pid, int val) for graph poc_graph {\n"
          + "  L0 = { members.* };\n"
          + "  L1 = select tgt from L0:src -(member_work_company)-> :tgt\n"
          + "       accum insert into company values (pid, \"com\", val);\n"
          + "}";
      err = "\nSemantic Check Error in query test21_2 (SEM-230): line 4, col 13"
          + "\nVertex 'company' has 3 attributes, but 2 values are provided.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 4 - insert non-existing attribute
    {
      query = ""
          + "create query test29 (string s, string t) for graph poc_graph {\n"
          + "  insert into members (primary_id, gender) values (s, t);\n"
          + "}";
      err = "\nSemantic Check Error in query test29 (SEM-231): line 2, col 35"
          + "\n'gender' is not a valid attribute of 'members'";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 5 - use insert edge syntax
    {
      query = ""
          + "create query test17 (string s, string t) for graph poc_graph {\n"
          + "  insert into company (FROM, TO) values (s, t);\n"
          + "}";
      err = "\nSemantic Check Error in query test17 (SEM-229): line 2, col 2"
          + "\nWhen inserting a vertex, you must specify PRIMARY_ID. FROM vertex and TO vertex"
          + "\nare for inserting edges.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testInsertVertexWithPrimaryKey() {
    Setup("primary_keys");
    String query = "", err = "";

    // 1. Primary key must be specified in the attribute list for insert
    {
      query = ""
          + "CREATE distributed QUERY insertErrorTest() FOR GRAPH primary_keys {\n"
          + "  Insert into person (name) values (\"3\");\n"
          + "}";
      err = "\nSemantic Check Error in query insertErrorTest (SEM-257): line 2, col 21"
          + "\nThe primary key 'id' was not provided in the specified attributes for vertex"
          + "\n'person'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 2. Primary key must be specified in the attribute list for insert
    {
      query = ""
          + "CREATE distributed QUERY insertErrorTest() FOR GRAPH primary_keys {\n"
          + "  Insert into movie2 (title, country, year) values (\"movie_name\", \"USA\", 1990);\n"
          + "}";
      err = "\nSemantic Check Error in query insertErrorTest (SEM-257): line 2, col 21"
          + "\nThe primary key 'id' was not provided in the specified attributes for vertex"
          + "\n'movie2'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 3. Primary_Id must be specified when inserting if no primary keys
    {
      query = ""
          + "CREATE distributed QUERY insertErrorTest() FOR GRAPH primary_keys {\n"
          + "  Insert into person1 (id, name) values (3, \"3\");\n"
          + "}";
      err = "\nSemantic Check Error in query insertErrorTest (SEM-257): line 2, col 22"
          + "\nVertex \"person1\" does not have a primary key, so PRIMARY_ID must be specified in"
          + "\nthe attributes when inserting.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 4. Primary_Id must be specified when inserting if no primary keys
    {
      query = ""
          + "CREATE distributed QUERY insertErrorTest() FOR GRAPH primary_keys {\n"
          + "  Insert into person2 (name) values (\"3\");\n"
          + "}";
      err = "\nSemantic Check Error in query insertErrorTest (SEM-257): line 2, col 22"
          + "\nVertex \"person2\" does not have a primary key, so PRIMARY_ID must be specified in"
          + "\nthe attributes when inserting.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 5. If Primary_Id being persisted as attribute, extra value does not need to be provided
    {
      query = ""
          + "CREATE distributed QUERY insertErrorTest() FOR GRAPH primary_keys {\n"
          + "  Insert into person2 values (4, 4, \"4\");\n"
          + "}";
      err = "\nSemantic Check Error in query insertErrorTest (SEM-230): line 2, col 2"
          + "\nVertex 'person2' has 2 attributes, but 3 values are provided.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 6. Cannot use primary_id when inserting into vertex that contains primary key
    {
      query = ""
          + "create query negative_composite() for graph primary_keys {\n"
          + "  insert into compositeMovie (PRIMARY_ID, id,title,year) values"
          + " (1,1,\"TigerGraph\", 777);\n"
          + "}";
      err = "\nSemantic Check Error in query negative_composite (SEM-257): line 2, col 29"
          + "\nPRIMARY_ID may not be used when inserting vertex \"compositeMovie\" because it"
          + "\ncontains a primary key";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 7. Not all primary keys specified
    {
      query = ""
          + "create query negative_composite() for graph primary_keys {\n"
          + "  insert into compositeMovie (title, year) values (\"TigerGraph\", 1777);\n"
          + "}";
      err = "\nSemantic Check Error in query negative_composite (SEM-257): line 2, col 29"
          + "\nThe primary key 'id' was not provided in the specified attributes for vertex"
          + "\n'compositeMovie'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 8. Invalid number of values provided for FROM vertex
    {
      query = ""
          + "create query negative_composite() for graph primary_keys {\n"
          + "  insert into compositeRoles VALUES ((\"testInsertPerson\",\"888\",\"123\"),"
          + " (\"testInsert\",\"TigerGraph\",\"777\"), \"CompositeEdgeInsert\");\n"
          + "}";
      err = "\nType Check Error in query negative_composite (TYP-6202): line 2, col 2"
          + "\nThe FROM vertex compositePerson requires 2 value(s) but 3 were provided instead.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // 9. Invalid number of values provided for TO vertex
    {
      query = ""
          + "create query negative_composite() for graph primary_keys {\n"
          + "  insert into compositeRoles VALUES ((\"testInsertPerson\",\"888\"),"
          + " (\"testInsert\",\"TigerGraph\"),"
          + " \"CompositeEdgeInsert\");\n"
          + "}";
      err = "\nType Check Error in query negative_composite (TYP-6202): line 2, col 2"
          + "\nThe TO vertex compositeMovie requires 3 value(s) but 2 were provided instead.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testInsertEdge() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // test 1 - key value count mismatch
    {
      query = ""
          + "create query test14 (string s, string t, int val) for graph poc_graph {\n"
          + "  insert into member_work_company (FROM, TO) values (s member, t company, val);\n"
          + "}";
      err = "\nSemantic Check Error in query test14 (SEM-227): line 2, col 34"
          + "\n'(FROM, TO)' has 2 entries, but '(s member, t company, val)' has 3 entries. They"
          + "\nare not consistent.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - attribute value count mismatch (statement level)
    {
      query = ""
          + "create query test23 (string s, string t, int val) for graph poc_graph {\n"
          + "  insert into member_member values (s member, t member);\n"
          + "}";
      err = "\nSemantic Check Error in query test23 (SEM-230): line 2, col 2"
          + "\nEdge 'member_member' has 1 attributes, but 0 values are provided.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - attribute value count mismatch (block level)
    {
      query = ""
          + "create query test23_2 (string s, string t, int val) for graph poc_graph {\n"
          + "  L0 = { members.* };\n"
          + "  L1 = select tgt from L0:src -(member_member:e)-> :tgt\n"
          + "       accum insert into member_member values (s member, t member);\n"
          + "}";
      err = "\nSemantic Check Error in query test23_2 (SEM-230): line 4, col 13"
          + "\nEdge 'member_member' has 1 attributes, but 0 values are provided.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 4 - use insert vertex syntax
    {
      query = ""
          + "create query test19 (string s) for graph poc_graph {\n"
          + "  insert into member_work_company (primary_id) values (s);\n"
          + "}";
      err = "\nSemantic Check Error in query test19 (SEM-229): line 2, col 2"
          + "\nWhen inserting an edge, you must specify FROM vertex and TO vertex. PRIMARY_ID"
          + "\nis for inserting vertices.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 5 - insert invalid attribute
    {
      query = ""
          + "create query test30 (string s, string t) for graph poc_graph {\n"
          + "  insert into member_work_company (FROM, to, startTime2) values (s, t, 3);\n"
          + "}";
      err = "\nSemantic Check Error in query test30 (SEM-231): line 2, col 45"
          + "\n'startTime2' is not a valid attribute of 'member_work_company'";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 6 - use _ for FROM vertex
    {
      query = ""
          + "create query test31 (string s, string t) for graph poc_graph {\n"
          + "  insert into member_work_company (FROM, TO) values (_, t);\n"
          + "}";
      err = "\nSemantic Check Error in query test31 (SEM-232): line 2, col 53"
          + "\nFROM and TO vertex cannot be _";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 7 - use _ for TO vertex
    {
      query = ""
          + "create query test32 (string s, string t) for graph poc_graph {\n"
          + "  insert into member_work_company (FROM, TO) values (s, _);\n"
          + "}";
      err = "\nSemantic Check Error in query test32 (SEM-232): line 2, col 53"
          + "\nFROM and TO vertex cannot be _";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testVertexSetPlusAssign() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query test100 (vertex<company> v1) for graph poc_graph {",
      "  x (ANY) = { members.* };",
      "  x += v1;",
      "}"
    };

    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test100 (SEM-234): line 3, col 2"
        + "\nAn undefined identifier 'x' in the current scope.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testEdgeAndTargetVertexTypeInBlock() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // test 1 - mix static and dynamic target vertex type
    {
      query = ""
          + "create query test46 (string s) for graph poc_graph {\n"
          + "  X = { members.* };\n"
          + "  X = select tgt from X -(member_to_all)-> (members|s):tgt;\n"
          + "}";
      err = "\nSemantic Check Error in query test46 (SEM-233): line 3, col 45"
          + "\nTarget vertex type check items must all be vertex types in the schema, which"
          + "\nmeans target vertex types are determined at compile time, or they must all be"
          + "\nparameters or global accumulators, which means target vertex types are"
          + "\ndetermined at runtime. Two cases cannot be mixed together.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - mix static and dynamic edge type
    {
      query = ""
          + "create query test45 (string s) for graph poc_graph {\n"
          + "  X = { members.* };\n"
          + "  X = select t from X -(member_member|s)-> :t;\n"
          + "}";
      err = "\nSemantic Check Error in query test45 (SEM-233): line 3, col 26"
          + "\nEdge type check items must all be edge types in schema, which means edge types"
          + "\nare compile time determined. Or they must all be parameters or global"
          + "\naccumulators, which means edge types are runtime determined. Two cases cannot be"
          + "\nmixed together.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testControlFlowStatement() {
    Setup("empty_graph");
    String query = "", err = "";

    // test 1 - break statement not within loop
    {
      query = ""
          + "create query n100() for graph empty_graph {\n"
          + "  break;\n"
          + "}";
      err = "\nSemantic Check Error in query n100 (SEM-100): line 2, col 2"
          + "\n'break;' is not in WHILE statement nor in FOREACH statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - continue statement not within loop
    {
      query = ""
          + "create query n101() for graph empty_graph {\n"
          + "  if true then\n"
          + "    if not false then\n"
          + "      continue;\n"
          + "    end;\n"
          + "  end;"
          + "}";
      err = "\nSemantic Check Error in query n101 (SEM-100): line 4, col 6"
          + "\n'continue;' is not in WHILE statement nor in FOREACH statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - FOREACH loop cardinality mismatch
    // TODO: improve the error message
    {
      query = ""
          + "create query n141() for graph empty_graph {\n"
          + "  MapAccum<int, int> @@a;\n"
          + "  foreach b in @@a do\n"
          + "    print \"ok\";\n"
          + "  end;\n"
          + "}";
      err = "\nSemantic Check Error in query n141 (SEM-126): line 3, col 10"
          + "\nThe cardinality of the FOREACH variable @@a must be the same as the container"
          + "\nelement cardinality: 2.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 4 - FOREACH loop use primitive accum as container
    {
      query = ""
          + "create query n139() for graph empty_graph {\n"
          + "  SumAccum<float> @@a;\n"
          + "  foreach b in @@a do\n"
          + "    print \"ok\";\n"
          + "  end;\n"
          + "}";
      err = "\nType Check Error in query n139 (TYP-510): line 3, col 15"
          + "\nA container type is required, while '@@a' is SumAccum<float>.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 5 - FOREACH loop use primitive param as container
    {
      query = ""
          + "create query n138(int a) for graph empty_graph {\n"
          + "  foreach b in a do\n"
          + "    print \"ok\";\n"
          + "  end;\n"
          + "}";
      err = "\nType Check Error in query n138 (TYP-510): line 2, col 15"
          + "\nA container type is required, while 'a' is int.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testAttributeAccess() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // Test 1 - read attribute of vertex set outside a DML block
    {
      query = ""
          + "create query n84(vertex<members> seed) for graph poc_graph {\n"
          + "  Start = { seed };\n"
          + "  while Start.registrationDate > 0 DO\n"
          + "    Start = select v from Start:v;\n"
          + "  end;\n"
          + "}";
      err = "\nSemantic Check Error in query n84 (SEM-105): line 3, col 8"
          + "\nThe vertex set or edge attribute outside a DML block cannot be referred.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testLoadAccum() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // Test 1 - same file, different separator
    {
      query = ""
          + "create query n137() for graph poc_graph {\n"
          + "  typedef tuple<string a, float b, int c, bool e> myTuple;\n"
          + "  SetAccum<myTuple> @@testSet;\n"
          + "  @@testSet = {\n"
          + "    loadAccum(\"/tmp/gacc1.csv\",$1,$2,$3,$4,\"\\t\",false),\n"
          + "    loadAccum(\"/tmp/gacc2.csv\",$1,$2,$3,$4,\",\",false),\n"
          + "    loadAccum(\"/tmp/gacc1.csv\",$1,$2,$3,$4,\",\",false)\n"
          + "  };\n"
          + "}";
      err = "\nSemantic Check Error in query n137 (SEM-118): line 7, col 43"
          + "\nIn the same statement, LoadAccum from the same file must use same separator."
          + "\nPrevious separator is '\\t'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // Test 2 - same file, different use_header flag
    {
      query = ""
          + "create query n136() for graph poc_graph {\n"
          + "  typedef tuple<string a, float b, int c, bool e> myTuple;\n"
          + "  SetAccum<myTuple> @@testSet;\n"
          + "  @@testSet = {\n"
          + "    loadAccum(\"/tmp/gacc1.csv\",$1,$2,$3,$4,\",\",true),\n"
          + "    loadAccum(\"/tmp/gacc2.csv\",$1,$2,$3,$4,\",\",false),\n"
          + "    loadAccum(\"/tmp/gacc1.csv\",$1,$2,$3,$4,\",\",false)\n"
          + "  };\n"
          + "}";
      err = "\nSemantic Check Error in query n136 (SEM-118): line 7, col 47"
          + "\nIn the same statement, the option of using header for LoadAccum from the same"
          + "\nfile must be specified by the same parameter. Previously, it is 'true'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // Test 3 - refer to column name while setting use_header flag to false
    {
      query = ""
          + "create query n135() for graph poc_graph {\n"
          + "  typedef tuple<string a, float b, int c, bool e> myTuple;\n"
          + "  SetAccum<myTuple> @@testSet;\n"
          + "  @@testSet = {loadAccum(\"/tmp/gacc1.csv\",\n"
          + "               $\"s\",$\"f\",$\"i\",$\"v\",\",\",false)};\n"
          + "}";
      err = "\nSemantic Check Error in query n135 (SEM-116): line 5, col 15"
          + "\nThe vertex id parameter '$\"s\"' is invalid because using header option is FALSE."
          + "\nPlease either change vertex id parameter to $number, or change using header to"
          + "\nTRUE.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // Test 4 - incorrect number of loading items
    {
      query = ""
          + "create query n134() for graph poc_graph {\n"
          + "  typedef tuple<string a, float b, int c, bool e> myTuple;\n"
          + "  SetAccum<myTuple> @@testSet;\n"
          + "  @@testSet = {loadAccum(\"/tmp/gacc1.csv\",$\"s\",$\"f\",$\"i\",\",\",true)};\n"
          + "}";
      err = "\nSemantic Check Error in query n134 (SEM-117): line 4, col 15"
          + "\nThe number of loading items in the loading statement does not match the global"
          + "\naccumulator. The correct number should be:4";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    {
      // SEM-116: vertex id cannot be string if usingHeader not literal "true" in loadAccum
      String[] queryLines = {
          "create query n6 (string inputPath, string separator,"
              + " bool usingHeader) for graph poc_graph {",
          "  TypeDef TUPLE<String aaa, float bbb, int ccc,vertex<company> ddd,bool eee> yourTuple;",
          "  SetAccum<yourTuple> @@testSet;",
          "  @@testSet = {loadAccum (inputPath,$\"s\",$\"f\",$\"i\",$\"v\","
              + "$\"b\",separator,usingHeader)};",
          "  print @@testSet;",
          "}"
      };

      // compile the query - should fail
      query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      err = "\nSemantic Check Error in query n6 (SEM-116): line 4, col 36\n"
          + "The vertex id parameter '$\"s\"' is invalid because using header option\n"
          + "'usingHeader' may be FALSE. Please either change vertex id parameter to $number,\n"
          + "or change using header to TRUE.";
      assertEquals(err, errMsg);
    }

    {
      // SEM-116: vertex id cannot be string if usingHeader not literal "true" in loadAccum
      String[] queryLines = {
          "create query n7 (string inputPath, string separator, string separator2, "
              + "bool usingHeader) for graph poc_graph {",
          "  TypeDef TUPLE<String aaa, float bbb, int ccc,vertex<company> ddd,bool eee> yourTuple;",
          "  SetAccum<yourTuple> @@testSet;",
          "  @@testSet = {loadAccum (inputPath,$0,$1,$2,$3,$4,separator,usingHeader),",
          "               loadAccum (inputPath,$0,$1,$2,$3,$4,separator2,usingHeader)};",
          "  print @@testSet;",
          "}"
      };

      // compile the query - should fail
      query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      err = "\nSemantic Check Error in query n7 (SEM-118): line 5, col 51\n"
          + "In the same statement, LoadAccum from the same file must use same separator.\n"
          + "Previous separator is 'separator'.";
      assertEquals(err, errMsg);
    }

    // test 7 - use empty string as separator
    {
      query = String.join("\n",
          "create query test18() for graph poc_graph {",
          "  SetAccum<int> @@set;",
          "  @@set += { loadAccum(\"/tmp/gacc1.csv\", $1, \"\", true) };",
          "}"
      );
      err = "\nAn empty string cannot be used as separator.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-809", err);
    }

    // test 8, SEM-2505 - constant file path is not absolute path
    {
      query = String.join("\n",
          "create query test19() for graph poc_graph {",
          "  SetAccum<int> @@set;",
          "  @@set += { loadAccum(\"gacc1.csv\", $1, \",\", true) };",
          "}"
      );
      err = "\nSemantic Check Error in query test19 (SEM-2505): line 3, col 23"
          + "\nThe path 'gacc1.csv' is not an absolute path. Please use absolute paths for file"
          + "\ninput constant path.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testIsNull() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // Test 1 - non-parameter expr using IS NULL operation
    {
      query = ""
          + "create query n133() for graph poc_graph {\n"
          + "  SumAccum<int> @@s;\n"
          + "  if @@s is null then\n"
          + "    print \"ok\";\n"
          + "  end;\n"
          + "}";
      err = "\nSemantic Check Error in query n133 (SEM-122): line 3, col 5"
          + "\nvariable @@s is not nullable.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testSelectVertex() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // test 1 - use id column header with use_header flag set to false
    {
      query = ""
          + "create query n117() for graph poc_graph {\n"
          + "  L0 = SelectVertex(\"/tmp/index.csv\", $\"id\", $1, \",\", false);\n"
          + "}";
      err = "\nSemantic Check Error in query n117 (SEM-116): line 2, col 38"
          + "\nThe vertex id parameter '$\"id\"' is invalid because using header option is FALSE."
          + "\nPlease either change vertex id parameter to $number, or change using header to"
          + "\nTRUE.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - use type column with user_header flag set to false
    {
      query = ""
          + "create query n118() for graph poc_graph {\n"
          + "  L0 = SelectVertex(\"/tmp/index.csv\", $0, $\"type\", \",\", false);\n"
          + "}";
      err = "\nSemantic Check Error in query n118 (SEM-116): line 2, col 38"
          + "\nThe vertex type parameter '$\"type\"' is invalid because using header option is"
          + "\nFALSE. Please either change vertex type parameter to $number, or change using"
          + "\nheader to TRUE.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - use negative id column (parse error)
    // TODO: need to find a way to compare parse error mesage
    {
      query = ""
          + "create query n119() for graph poc_graph {\n"
          + "  L0 = SelectVertex(\"/tmp/index.csv\", $-1, $1, \",\", false);\n"
          + "}";
      assertNull(compile(query));
    }

    // test 3 - use negative type column (parse error)
    // TODO: need to find a way to compare parse error mesage
    {
      query = ""
          + "create query n119() for graph poc_graph {\n"
          + "  L0 = SelectVertex(\"/tmp/index.csv\", $0, $-1, \",\", false);\n"
          + "}";
      assertNull(compile(query));
    }

    // test 4 - inconsistent separator for the same file
    {
      query = ""
          + "create query n121() for graph poc_graph {\n"
          + "  L0 = {\n"
          + "    SelectVertex(\"/tmp/index.csv\", $0, $1, \",\", false),\n"
          + "    SelectVertex(\"/tmp/index.csv\", $2, $3, \"|\", false)\n"
          + "  };\n"
          + "}";
      err = "\nSemantic Check Error in query n121 (SEM-118): line 4, col 43"
          + "\nIn the same statement, SelectVertex from the same file must use same separator."
          + "\nPrevious separator is ','.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 5 - inconsistent user_header option for the same file
    {
      query = ""
          + "create query n123() for graph poc_graph {\n"
          + "  L0 = {\n"
          + "    SelectVertex(\"/tmp/index.csv\", $0, $1, \",\", false),\n"
          + "    SelectVertex(\"/tmp/index.csv\", $2, $3, \",\", true)\n"
          + "  };\n"
          + "}";
      err = "\nSemantic Check Error in query n123 (SEM-118): line 4, col 48"
          + "\nIn the same statement, the option of using header for SelectVertex from the same"
          + "\nfile must be specified by the same parameter. Previously, it is 'false'.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 6 - $sys.data_root not set
    {
      query = ""
          + "create query n124() for graph poc_graph {\n"
          + "  L0 = {SelectVertex(\"$sys.data_root/index.csv\", $0, $1, \",\", false)};\n"
          + "}";
      err = "\nSemantic Check Error in query n124 (SEM-119): line 2, col 21"
          + "\nparameter sys.data_root hasn't been set yet."
          + "\n[Hint] Each time launch gsql, redo the command set sys.data_root = \"xxx\".";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 7 - invalid separator \\
    {
      query = ""
          + "create query n125() for graph poc_graph {\n"
          + "  L0 = {SelectVertex(\"/tmp/index.csv\", $2, $3, \"\\\\\", true)};\n"
          + "}";
      err = "\nSemantic Check Error in query n125 (SEM-120): line 2, col 21"
          + "\n'\\\\' is an invalid separator.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 8 - invalid separator \t\t
    {
      query = ""
          + "create query n126() for graph poc_graph {\n"
          + "  L0 = {SelectVertex(\"/tmp/index.csv\", $2, $3, \"\\t\\t\", true)};\n"
          + "}";
      err = "\nSemantic Check Error in query n126 (SEM-120): line 2, col 21"
          + "\n'\\t\\t' is an invalid separator.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 9 - invalid separator ,,
    {
      query = ""
          + "create query n126() for graph poc_graph {\n"
          + "  L0 = {SelectVertex(\"/tmp/index.csv\", $2, $3, \",,\", true)};\n"
          + "}";
      err = "\nSemantic Check Error in query n126 (SEM-120): line 2, col 21"
          + "\n',,' is an invalid separator.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // SEM-116: vertex id cannot be string if usingHeader not literal "true" in SelectVertex
      String[] queryLines = {
          "create query n4 (string filename1, string separator1, bool usingHeader1, "
              + "string filename2, string separator2, bool usingHeader2) for graph poc_graph {",
          "  L1 (_) = {",
          "    SelectVertex(filename1, $\"company\", company, separator1, usingHeader1)",
          "  };",
          "  print L1;",
          "}"
      };

      // compile the query - should fail
      query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      err = "\nSemantic Check Error in query n4 (SEM-116): line 3, col 28\n"
          + "The vertex id parameter '$\"company\"' is invalid because using header option\n"
          + "'usingHeader1' may be FALSE. Please either change vertex id parameter to\n"
          + "$number, or change using header to TRUE.";
      assertEquals(err, errMsg);
    }
    {
      // SEM-118: usingHeader option must be specified by same varaible for same file
      String[] queryLines = {
          "create query n5 (string filename1, string separator1, bool usingHeader1, "
              + "string filename2, string separator2, bool usingHeader2) for graph poc_graph {",
          "  L1 (_) = {",
          "    SelectVertex(filename1, $1, company, separator1, usingHeader1),",
          "    SelectVertex(filename1, $1, company, separator1, true)",
          "  };",
          "  print L1;",
          "}"
      };

      // compile the query - should fail
      query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      err = "\nSemantic Check Error in query n5 (SEM-118): line 4, col 53\n"
          + "In the same statement, the option of using header for SelectVertex from the same\n"
          + "file must be specified by the same parameter. Previously, it is 'usingHeader1'.";
      assertEquals(err, errMsg);
    }
    // test SEM-2505 - constant file path is not absolute path
    {
      query = ""
          + "create query n2505() for graph poc_graph {\n"
          + "  L0 = {\n"
          + "    SelectVertex(\"./index.csv\", $0, $1, \",\", false)\n"
          + "  };\n"
          + "}";
      err = "\nSemantic Check Error in query n2505 (SEM-2505): line 3, col 17"
          + "\nThe path './index.csv' is not an absolute path. Please use absolute paths for"
          + "\nfile input constant path.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // Positive test
    {
      String[] queryLines = {
          "create query testPos (string filename1, string separator1, bool usingHeader1, "
              + "string filename2, string separator2, bool usingHeader2) for graph poc_graph {",
          "  L1 (_) = {",
          "    SelectVertex(filename1, $2, $3, \",\", true)",
          "  };",
          "  L2 (_) = {",
          "    SelectVertex(\"/tmp/index.csv\", $2, $3, \",\", true)",
          "  };",
          "  print L1;",
          "  print L2;",
          "}"
      };

      // compile the query - should succeed
      query = String.join("\n", queryLines);
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testQueryParameter() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // test 1 - vertex param with default value
    {
      query = ""
          + "create query n30 (vertex<company> seed=\"aa\") for graph poc_graph {\n"
          + "  Start = { seed };\n"
          + "}";
      err = "\nSemantic Check Error in query n30 (SEM-1): line 1, col 18"
          + "\nVertex or set type argument seed cannot have a default value";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - set param with default value
    {
      query = ""
          + "create query n39 (set<int> aaa = 1) for graph poc_graph {\n"
          + "  Start = { company.* };\n"
          + "}";
      err = "\nSemantic Check Error in query n39 (SEM-1): line 1, col 18"
          + "\nVertex or set type argument aaa cannot have a default value";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - use parameter as a stand-alone assignment
    {
      query = String.join("\n",
          "create query test35 (int a) for graph poc_graph {",
          "  Start = { _ };",
          "  Start = select s from Start:s accum a;",
          "}"
      );
      err = "\nExpression 'a' cannot be used as a stand alone assignment";
      assertNull(compile(query));
      assertError(errMsg, "SEM-819", err);
    }
  }

  @Test
  public void testFloatPointWarn() {
    Setup("poc_graph_2");
    // SEM-Warn-5: Equality test between floating-point numbers is imprecise.
    String[] queryLines = {
        "create query n18() for graph poc_graph {",
        "  MaxAccum<double> @@max;",
        "  float k = 1.0;",
        "  @@max += k;",
        "  if @@max <= 1.0 then",
        "    if 1.0 >= 1.0 then",
        "      if k == 1 then",
        "        if k != @@max then",
        "          print \"n13\";",
        "        end;",
        "      end;",
        "    end;",
        "  end;",
        "  L0 = {_};",
        "  L1 = SELECT tgt",
        "       FROM L0-()->company:tgt",
        "       WHERE tgt.nCount BETWEEN 18.0 AND 60",
        "       ;",
        "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    flags_.add(compileFlags.WARNING);
    QuerySignature qs = compile(query);
    assertNotNull(qs);

    // check error message
    String err = "Warning in query n18 (WARN-5): line 5, col 5\n"
        + "The comparison '@@max<=1.0' may lead to unexpected behavior because it involves\n"
        + "equality test between float/double numeric values. We suggest to do such\n"
        + "comparison with an error margin, e.g. '@@max<=1.0 + epsilon', where epsilon is a\n"
        + "very small positive value of your choice, such as 0.0001.\n"
        + "Warning in query n18 (WARN-5): line 6, col 7\n"
        + "The comparison '1.0>=1.0' may lead to unexpected behavior because it involves\n"
        + "equality test between float/double numeric values. We suggest to do such\n"
        + "comparison with an error margin, e.g. '1.0>=1.0 - epsilon', where epsilon is a\n"
        + "very small positive value of your choice, such as 0.0001.\n"
        + "Warning in query n18 (WARN-5): line 7, col 9\n"
        + "The comparison 'k==1' may lead to unexpected behavior because it involves\n"
        + "equality test between float/double numeric values. We suggest to do such\n"
        + "comparison with an error margin, e.g. 'abs((k) - (1)) < epsilon', where epsilon\n"
        + "is a very small positive value of your choice, such as 0.0001.\n"
        + "Warning in query n18 (WARN-5): line 8, col 11\n"
        + "The comparison 'k!=@@max' may lead to unexpected behavior because it involves\n"
        + "equality test between float/double numeric values. We suggest to do such\n"
        + "comparison with an error margin, e.g. 'abs((k) - (@@max)) > epsilon', where\n"
        + "epsilon is a very small positive value of your choice, such as 0.0001.\n"
        + "Warning in query n18 (WARN-5): line 17, col 13\n"
        + "The expression 'tgt.nCount BETWEEN 18.0 AND 60' may lead to unexpected behavior\n"
        + "because it involves equality test between float/double numeric values. We\n"
        + "suggest to do such comparison with an error margin, e.g. 'abs((tgt.nCount) -\n"
        + "(18.0)) <= epsilon OR abs((tgt.nCount) - (60)) <= epsilon OR ((tgt.nCount >\n"
        + "18.0) AND (tgt.nCount < 60))', where epsilon is a very small positive value of\n"
        + "your choice, such as 0.0001.\n";
    assertEquals(err, outMsg);
  }

  @Test
  public void testBreakContinueInStatement() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // testGLE510_n1 and testGLE510_n2 are negative test cases for break
    // and continue in the statement level
    {
      query = ""
          + "create query testGLE510_n1 () for graph poc_graph {\n"
          + "  ListAccum<int> @@ListA;\n"
          + "  int j = 0;\n"
          + "  @@ListA += 1;\n"
          + "  @@ListA += 2;\n"
          + "  @@ListA += 3;\n"
          + "  @@ListA += 4;\n"
          + "  \n"
          + "  FOREACH i IN @@ListA\n"
          + "  DO\n"
          + "    CASE WHEN i == 2\n"
          //valid continue in the statement level FOREACH
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    CASE WHEN i == 3\n"
          //valid break in the statement level FOREACH
          + "      THEN BREAK;\n"
          + "    END;\n"
          + "  END;\n"
          + "  WHILE true\n"
          + "  DO\n"
          + "  j = j + 1;\n"
          + "    CASE WHEN i == 2\n"
          //valid continue in the statement level WHILE
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    CASE WHEN j == 3\n"
          //valid break in the statement level WHILE
          + "      THEN BREAK;\n"
          + "    END;\n"
          + "  END;\n"
          //invalid break in statement level
          + "  BREAK;\n"
          + "}";
      err = "\nSemantic Check Error in query testGLE510_n1 (SEM-100): line 28, col 2"
          + "\n'break;' is not in WHILE statement nor in FOREACH statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    {
      query = ""
          + "create query testGLE510_n2 () for graph poc_graph syntax v1 {\n"
          + "  ListAccum<int> @@ListA;\n"
          + "  @@ListA += 1;\n"
          + "  @@ListA += 2;\n"
          + "  @@ListA += 3;\n"
          + "  @@ListA += 4;\n"
          + "  FOREACH i IN @@ListA\n"
          + "  DO\n"
          + "    CASE WHEN i == 2\n"
          //valid continue in the statement level FOREACH
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    CASE WHEN i == 3\n"
          //valid continue in the statement level FOREACH
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "  END;\n"
          + "  WHILE (j < 3)\n"
          + "  DO\n"
          + "  j = j + 1;\n"
          + "    CASE WHEN i == 2\n"
          //valid continue in the statement level WHILE
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    CASE WHEN j == 2\n"
          //valid continue in the statement level WHILE
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "  END;\n"
          //invalid continue in statement level
          + "  CONTINUE;\n"
          + "}";
      err = "\nSemantic Check Error in query testGLE510_n2 (SEM-100): line 26, col 2"
          + "\n'continue;' is not in WHILE statement nor in FOREACH statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // testGLE510_n3 is the negative case for BREAK in the assignment level
    {
      query = ""
          + "create query testGLE510_n3 () for graph poc_graph {\n"
          + "  ListAccum<int> @@ListA;\n"
          + "  @@ListA += 1;\n"
          + "  @@ListA += 2;\n"
          + "  @@ListA += 3;\n"
          + "  @@ListA += 4;\n"
          + "  Seed = {members.*};\n"
          + "  \n"
          + "  FOREACH i IN @@ListA\n"
          + "  DO\n"
          + "    CASE WHEN i == 3\n"
          //valid continue in the statement level FOREACH
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    X = SELECT tgt\n"
          + "      FROM  Seed:src - (:e) -> company: tgt\n"
          + "      ACCUM\n"
          + "        int j = 0,\n"
          + "        WHILE j < 4\n"
          + "        DO\n"
          + "          j = j+1,"
          + "        CASE WHEN j == 2\n"
          + "          THEN CONTINUE\n"
          + "        END,\n"
          + "        CASE WHEN j == 3\n"
          + "          THEN BREAK\n"
          + "        END\n"
          + "      END,\n"
          + "      BREAK"
          + "     ;"
          + "  END;\n"
          + "}";
      err = "\nSemantic Check Error in query testGLE510_n3 (SEM-124): line 27, col 6"
          + "\nDML-level 'BREAK' must appear in a FOREACH/WHILE block statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // testGLE510_n4 is the negative case for continue in the assignment level
    {
      query = ""
          + "create query testGLE510_n4 () for graph poc_graph {\n"
          + "  ListAccum<int> @@ListA;\n"
          + "  @@ListA += 1;\n"
          + "  @@ListA += 2;\n"
          + "  @@ListA += 3;\n"
          + "  @@ListA += 4;\n"
          + "  Seed = {members.*};\n"
          + "  \n"
          + "  FOREACH i IN @@ListA\n"
          + "  DO\n"
          + "    CASE WHEN i == 3\n"
          //valid continue in the statement level FOREACH
          + "      THEN CONTINUE;\n"
          + "    END;\n"
          + "    X = SELECT tgt\n"
          + "      FROM  Seed:src - (:e) -> company: tgt\n"
          + "      ACCUM\n"
          + "        int j = 0,\n"
          + "        WHILE j < 4\n"
          + "        DO\n"
          + "          j = j+1,"
          + "        CASE WHEN j == 2\n"
          + "          THEN CONTINUE\n"
          + "        END,\n"
          + "        CASE WHEN j == 3\n"
          + "          THEN BREAK\n"
          + "        END\n"
          + "      END,\n"
          + "      CONTINUE"
          + "     ;"
          + "  END;\n"
          + "}";
      err = "\nSemantic Check Error in query testGLE510_n4 (SEM-124): line 27, col 6"
          + "\nDML-level 'CONTINUE' must appear in a FOREACH/WHILE block statement.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testLikeExpr() {
    Setup("recommend");
    String query = "", err = "";

    // test 1 - disallow var.type LIKE regex
    {
      query = ""
          + "create query n92() for graph recommend {\n"
          + "  Seed = { _ };\n"
          + "  Seed = select Seed from Seed\n"
          + "    where Seed.type LIKE \"%abc\";\n"
          + "}";
      err = "\nSemantic Check Error in query n92 (SEM-430): line 4, col 10"
          + "\n'var.Type LIKE regex' is not supported. Please spell out the types in"
          + "\ndisjunction.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testReadFieldOfNonTupleType() {
    Setup("recommend");
    String query = "", err = "";

    // test 1 - read field via MapAccum.get() function
    {
      query = ""
          + "create query n55() for graph recommend {\n"
          + "  typedef tuple<string aaa, string bbb> strTuple;\n"
          + "  MapAccum<string, string> @@mapmap;\n"
          + "  print @@mapmap.get(\"a\").aaa;\n"
          + "}";
      err = "\nSemantic Check Error in query n55 (SEM-530): line 4, col 8"
          + "\nNo field can be accessed by '@@mapmap.get(\"a\")'";
      assertNull(compile(query));
      assertEquals(err, errMsg);

    }
  }

  @Test
  public void testSetAsCondition() {
    Setup("poc_graph");
    String q = String.join("\n",
        "CREATE QUERY test607_1(VERTEX<members> seed) {",
        "  SetAccum<INT> @@A;",
        "  Start = { members.* };",
        "  L0 = SELECT v",
        "    FROM Start:s-(member_skill:e)->:v",
        "    WHERE s == seed;",
        // SetAccum cannot be a condition
        "  if @@A then",
        "    PRINT L0;",
        "  end;",
        "}"
    );
    assertNull(compile(q));
    String errCode = "SEM-121";
    String errDesc = "Set expression cannot be a condition!";
    assertError(errMsg, errCode, errDesc);
  }

  @Test
  public void testListAsCondition() {
    Setup("poc_graph");
    String q = String.join("\n",
        "CREATE QUERY test607_1(VERTEX<members> seed) {",
        "  ListAccum<INT> @@A;",
        "  Start = { members.* };",
        "  L0 = SELECT v",
        "    FROM Start:s-(member_skill:e)->:v",
        "    WHERE s == seed;",
        // ListAccum cannot be a condition
        "  if @@A then",
        "    PRINT L0;",
        "  end;",
        "}"
    );
    assertNull(compile(q));
    String errCode = "SEM-121";
    String errDesc = "Set expression cannot be a condition!";
    assertError(errMsg, errCode, errDesc);
  }

  @Test
  public void testMapAsCondition() {
    Setup("poc_graph");
    String q = String.join("\n",
        "CREATE QUERY test607_1(VERTEX<members> seed) {",
        "  MapAccum<INT, STRING> @@A;",
        "  Start = { members.* };",
        "  L0 = SELECT v",
        "    FROM Start:s-(member_skill:e)->:v",
        "    WHERE s == seed;",
        // MapAccum cannot be a condition
        "  if @@A then",
        "    PRINT L0;",
        "  end;",
        "}"
    );
    assertNull(compile(q));
    String errCode = "SEM-121";
    String errDesc = "Set expression cannot be a condition!";
    assertError(errMsg, errCode, errDesc);
  }

  @Test
  public void testVertexAttribute() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - global attr access on universal vertex type
    {
      query = ""
          + "create query n13 (vertex v) for graph poc_graph {\n"
          + "  string vv;\n"
          + "  vv = v.id;\n"
          + "}";
      err = "\nThe attribute of an universal vertex type variable cannot be accessed.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-606", err);
    }

    // test 2 - global attr access on universal vertex type (in loop)
    {
      query = ""
          + "create query n14() for graph poc_graph {\n"
          + "  SetAccum<vertex> @@op;\n"
          + "  string vv;\n"
          + "  foreach v in @@op do\n"
          + "    vv = v.id;\n"
          + "  end;\n"
          + "}";
      err = "\nThe attribute of an universal vertex type variable cannot be accessed.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-606", err);
    }
  }

  @Test
  public void testEngineParameter() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - use outside expression function
    {
      query = String.join("\n",
        "create query test2() for graph poc_graph syntax v1 {",
        "  SetAccum<int> @@set;",
        "  Start = { members.* };",
        "  Start = select t from Start-(member_member:e)-:t",
        "    accum @@set += __ENGINE__SRC_ATTR;",
        "}"
      );
      err = "\nEngine parameters cannot be used outside the expression function.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-802", err);
    }

    // test 2 - use in a wrong place
    // TODO: The error message needs to be more specific
    {
      query = String.join("\n",
        "create query test3() for graph poc_graph syntax v1 {",
        "  SetAccum<string> @@set;",
        "  Start = { members.* };",
        "  Start = select t from Start-(member_member:e)-:t",
        "    accum @@set += to_string(__ENGINE__V_ATTR);",
        "}"
      );
      err = "\nThe engine parameter '__ENGINE__V_ATTR' has been used in a wrong place.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-803", err);
    }
  }

  @Test
  public void testReturnStatement() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - query has no return type, but return statement is present
    // TODO: This error can be reported at an earlier stage (ParserOutputProcessor)
    {
      query = String.join("\n",
          "create query test14() for graph poc_graph {",
          "  return 0;",
          "}"
      );
      err = "\nThe query 'test14' with no return type cannot return a value.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-806", err);
    }

    // test 2 - query has return type, but no return statement
    {
      query = String.join("\n",
          "create query test15() for graph poc_graph returns (set<vertex>) {",
          "  Start = { company.* };",
          "}"
      );
      err = "\nQuery 'test15' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    // test 3 - has unreachable statement after return
    {
      query = String.join("\n",
          "create query test16() for graph poc_graph returns (set<vertex>) {",
          "  Start (ANY) = { company.* };",
          "  return Start;",
          "  Start = {};",
          "}"
      );
      err = "\nStatements after the RETURN statement are unreachable!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-807", err);
    }

    // test 4 - not all runtime paths have a return statement
    {
      query = String.join("\n",
          "create query test17() for graph poc_graph returns (set<vertex>) {",
          "  Start = { company.* };",
          "  while true do",
          "    return Start;",
          "  end;",
          "}"
      );
      err = "\nQuery 'test17' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    // test 5 - positive test case for multiple return statement in different runtime path
    {
      query = String.join("\n",
          "create query test22 (int x) for graph poc_graph returns(int) {",
          "  if x > 100 then",
          "    return x;",
          "  end;",
          "  return x + 100;",
          "}"
      );
      assertNotNull(compile(query));
    }

    // test 6 - a query with return type and try...exception clause doesn't have return
    // statement in all paths (print doesn't have a subsequent return path)
    {
      query = String.join("\n",
              "create or replace query test23 (String s) returns (bool) {\n",
                      "    EXCEPTION zero (40001);\n",
                      "    int i = 0;\n",
                      "    TRY\n",
                      "        IF i == 0 then\n",
                      "          raise zero (\"Error: i is zero\");\n",
                      "        END;\n",
                      "        return true;\n",
                      "    EXCEPTION\n",
                      "        WHEN zero THEN i = 10;\n",
                      "    END;\n",
                      "    print(i);\n",
                      "}"
      );
      err = "\nQuery 'test23' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    // test 7 - a distributed query with return type and try...exception clause doesn't have
    // return statement in all paths (print doesn't have a subsequent return path)
    {
      query = String.join("\n",
              "create or replace distributed query test24 (String s) returns (bool) {\n",
              "    EXCEPTION zero (40001);\n",
              "    int i = 0;\n",
              "    TRY\n",
              "        IF i == 0 then\n",
              "          raise zero (\"Error: i is zero\");\n",
              "        END;\n",
              "        return true;\n",
              "    EXCEPTION\n",
              "        WHEN zero THEN i = 10;\n",
              "    END;\n",
              "    print(i);\n",
              "}"
      );
      err = "\nQuery 'test24' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    /**
     * test 8 - negative case. an udf sub-query with StatementRaise doesn't have return statement
     */
    {
      query = String.join("\n",
              "create query raiseInSubWithoutReturn_udf(int i) returns (bool) {\n",
                      "     EXCEPTION wrongRange (40001);\n",
                      "     IF i > 1 then\n",
                      "         raise wrongRange (\"Used a wrong range.\");\n",
                      "     END;\n",
                      "}"
      );
      err = "\nQuery 'raiseInSubWithoutReturn_udf' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    /**
     * test 9 - positive case. an udf sub-query with StatementRaise has return statement
     */
    {
      query = String.join("\n",
              "create query raiseInSubWithReturn_udf(int i) returns (bool) {\n",
              "     EXCEPTION wrongRange (40001);\n",
              "     IF i > 1 then\n",
              "         raise wrongRange (\"Used a wrong range.\");\n",
              "     END;\n",
              "     return true;",
              "}"
      );
      assertNotNull(compile(query));
    }

    /**
     * test 10 - negative case. an gpr sub-query with StatementRaise doesn't have return statement
     */
    {
      query = String.join("\n",
              "create distributed query raiseInSubWithoutReturn_gpr(int i) returns (bool) {\n",
              "     EXCEPTION wrongRange (40001);\n",
              "     IF i > 1 then\n",
              "         raise wrongRange (\"Used a wrong range.\");\n",
              "     END;\n",
              "}"
      );
      err = "\nQuery 'raiseInSubWithoutReturn_gpr' must return a value!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-808", err);
    }

    /**
     * test 11 - positive case. an gpr sub-query with StatementRaise has return statement
     */
    {
      query = String.join("\n",
              "create distributed query raiseInSubWithReturn_gpr(int i) returns (bool) {\n",
              "     EXCEPTION wrongRange (40001);\n",
              "     IF i > 1 then\n",
              "         raise wrongRange (\"Used a wrong range.\");\n",
              "     END;\n",
              "     return true;",
              "}"
      );
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testListExpr() {
    Setup("poc_graph");
    String query = String.join("\n",
        "create query test23() for graph poc_graph {",
        "  foreach x in (1,2,3) do",
        "    print x;",
        "  end;",
        "}"
    );
    String err = "\nIterating over an inlist at statement level is not supprted for now.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-810", err);
  }

  /**
   * Tag access function cannot be used in query for a tag-based graph.
   */
  @Test
  public void testTagAccessInTagGraph() {
    Setup("tagGraph_simple");
    String query = "", err = "";
    // 1. isTaggable
    {
      query = String.join("\n",
          "create query test1() for graph tagGraph {",
          "  Start = { ANY };",
          "  Result = select s from Start:s",
          "           where s.isTaggable();",
          "  PRINT Result;",
          "}"
      );
      err = "The tag-access function 'istaggable' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 2. getTags
    {
      query = String.join("\n",
          "create query test2() for graph tagGraph {",
          "  SetAccum<string> @@gAcc;",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      @@gAcc += tgt.getTags();",
          "  PRINT @@gAcc;",
          "}"
      );
      err = "The tag-access function 'gettags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 3. intersecttags
    {
      query = String.join("\n",
          "create query test3() for graph tagGraph {",
          "  SetAccum<string> @@gAcc;",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      @@gAcc += tgt.intersectTAGS(src);",
          "  PRINT @@gAcc;",
          "}"
      );
      err = "The tag-access function 'intersecttags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 4. differencetags
    {
      query = String.join("\n",
          "create query test4() for graph tagGraph {",
          "  SetAccum<string> @@gAcc;",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      @@gAcc += tgt.DIFFERENCETAGS(src);",
          "  PRINT @@gAcc;",
          "}"
      );
      err = "The tag-access function 'differencetags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 5. addtags
    {
      query = String.join("\n",
          "create query test5() for graph tagGraph {",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      tgt.ADDTAGs(\"tag2\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag-access function 'addtags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 6. removetags
    {
      query = String.join("\n",
          "create query test6() for graph tagGraph {",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      tgt.removetags(\"tag3\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag-access function 'removetags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 7. removealltags
    {
      query = String.join("\n",
          "create query test7() for graph tagGraph {",
          "  Start = { ANY };",
          "  Result =",
          "    select tgt",
          "    from Start:src -(:e)-> :tgt",
          "    accum",
          "      tgt.removealltags();",
          "  PRINT Result;",
          "}"
      );
      err = "The tag-access function 'removealltags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 8. hastags
    {
      query = String.join("\n",
          "create query test8() for graph tagGraph {",
          "  Start = { ANY };",
          "  Result = select s from Start:s",
          "           where s.HAStags(\"tag1\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag-access function 'hastags' cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }

    // 9. "WITH TAGS" in printExpr
    {
      query = String.join("\n",
          "create query test9() for graph tagGraph {",
          "  Start = { ANY };",
          "  PRINT Start WITH Tags;",
          "}"
      );
      err = "The print of tags cannot be used in query "
          + "for a tag-based graph: 'tagGraph'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2420", err);
    }
  }

  /**
   * Invalid constant tag name referred in tag functions
   */
  @Test
  public void testInvalidTagNameInTagFunc() {
    Setup("poc_graph_tag");
    String query = "", err = "";
    // 1. hastags
    {
      query = String.join("\n",
          "create query test1() for graph poc_graph_tag {",
          "  Start = { ANY };",
          "  Result = select s from Start:s",
          "           where s.HASTAGS(\"tag1\", \"tag_qqq\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag name 'tag_qqq' in function 'hastags' is not valid "
          + "in current graph: 'poc_graph_tag'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2424", err);
    }

    // 2. addtags
    {
      query = String.join("\n",
          "create query test2() for graph poc_graph_tag {",
          "  Start = { ANY };",
          "  Result = select s from Start:s",
          "           accum s.addTAGS(\"tag2\", \"tag_qqq\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag name 'tag_qqq' in function 'addtags' is not valid "
          + "in current graph: 'poc_graph_tag'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2424", err);
    }

    // 3. removetags
    {
      query = String.join("\n",
          "create query test3() for graph poc_graph_tag {",
          "  Start = { ANY };",
          "  Result = select s from Start:s",
          "           accum s.Removetags(\"tag_qqq\", \"tag3\");",
          "  PRINT Result;",
          "}"
      );
      err = "The tag name 'tag_qqq' in function 'removetags' is not valid "
          + "in current graph: 'poc_graph_tag'.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2424", err);
    }
  }

  @Test
  public void testSetPrimaryKeyAttribute() {
    Setup("primary_keys");
    String query = "", err = "";

    // 1. Attribute cannot be updated if it is a primary key
    {
      query = ""
          + "CREATE QUERY test(/* Parameters here */) FOR GRAPH primary_keys {\n"
          + "  S = {person.*};\n"
          + "  x = SELECT s from S:s\n"
          + "  POST-ACCUM s.id = 1\n"
          + "  ;\n"
          + "}";
      err = "\nSemantic Check Error in query test (SEM-1501): line 4, col 13"
          + "\nAttribute id cannot be updated if it is a primary key or being persisted as an"
          + "\nattribute from primary id.";
      assertNull(compile(query));
      assertEquals(errMsg, err);
    }

    // 2. Attribute cannot be updated if it is a primary key
    {
      query = ""
          + "CREATE QUERY test2(/* Parameters here */) FOR GRAPH primary_keys {\n"
          + "  S = {person.*};\n"
          + "  UPDATE s from S:s\n"
          + "  SET s.id = 1;\n"
          + "}";
      err = "\nSemantic Check Error in query test2 (SEM-1501): line 4, col 6"
          + "\nAttribute id cannot be updated if it is a primary key or being persisted as an"
          + "\nattribute from primary id.";
      assertNull(compile(query));
      assertEquals(errMsg, err);
    }

    // 3. Attribute cannot be updated if is persisted from primary_id
    {
      query = ""
          + "CREATE QUERY test3(/* Parameters here */) FOR GRAPH primary_keys {\n"
          + "  S = {person2.*};\n"
          + "  x = SELECT s from S:s\n"
          + "  POST-ACCUM s.id = 1\n"
          + "  ;\n"
          + "}";
      err = "\nSemantic Check Error in query test3 (SEM-1501): line 4, col 13"
          + "\nAttribute id cannot be updated if it is a primary key or being persisted as an"
          + "\nattribute from primary id.";
      assertNull(compile(query));
      assertEquals(errMsg, err);
    }

    // 4. Attribute cannot be updated if it is a primary key
    {
      query = ""
          + "CREATE QUERY test4(/* Parameters here */) FOR GRAPH primary_keys syntax v1 {\n"
          + "  S = {compositePerson.*};\n"
          + "  UPDATE s from S:s\n"
          + "  SET s.name = 1;\n"
          + "}\n";
      err = "\nSemantic Check Error in query test4 (SEM-1501): line 4, col 6"
          + "\nAttribute name cannot be updated if it is a primary key or being persisted as an"
          + "\nattribute from primary id.";
      assertNull(compile(query));
      assertEquals(errMsg, err);
    }

    // 5. Attribute cannot be updated if it is a primary key
    {
      query = ""
          + "CREATE QUERY test5(/* Parameters here */) FOR GRAPH primary_keys {\n"
          + " S = {compositePerson.*};\n"
          + "  x = SELECT s from S:s\n"
          + "  POST-ACCUM s.id = 1\n"
          + "  ; \n"
          + "}";
      err = "\nSemantic Check Error in query test5 (SEM-1501): line 4, col 13"
          + "\nAttribute id cannot be updated if it is a primary key or being persisted as an"
          + "\nattribute from primary id.";
      assertNull(compile(query));
      assertEquals(errMsg, err);
    }
  }

  @Test
  public void testTableSelectInSyntaxV1() {
    Setup("ldbc_snb_v2");
    String query = "", err = "";
    {
      String[] queryLines = {
        "create query test_table() for graph ldbc_snb syntax v1 {",
        "  Seed = { any };",
        "  select v.id, v.name INTO T",
        "    from Seed:v;",
        "  print T;",
        "}"
      };

      // compile the query - should fail
      query = String.join("\n", queryLines);
      err = "The query specifies V1 syntax but uses V2 here.";
      assertNull(compile(query));
      assertError(errMsg, "Syntax Error", err);
    }
  }

  @Test
  public void testMultiplePostAccumClauses() {
    Setup("testGraph");
    String query, err;

    // GLE-2138: NPE when using multiple POST-ACCUM clauses.
    {
      query = ""
          + "CREATE QUERY test(VERTEX<person2> v) FOR GRAPH testGraph {\n"
          + "  SetAccum<VERTEX<person2>> @@vs;"
          + "  start = {v};\n"
          + "  start =\n"
          + "    SELECT s FROM start:s\n"
          + "      POST-ACCUM @@vs += s\n"
          + "      POST-ACCUM @@vs += s;\n"
          + "}\n";
      assertNotNull(compile(query));
    }

    // SEM-2501: Multiple POST-ACCUM clauses are also supported in v1 syntax now.
    {
      query = ""
          + "CREATE QUERY test(VERTEX<person2> v) FOR GRAPH testGraph {\n"
          + "  SetAccum<VERTEX<person2>> @@vs;"
          + "  start = {v};\n"
          + "  start =\n"
          + "    SELECT s FROM start:s\n"
          + "      POST-ACCUM @@vs += s\n"
          + "      POST-ACCUM @@vs += s;\n"
          + "}\n";
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testMissingLeftVSetVar() {
    Setup("testGraph");
    String query, err;

    // GLE-2263: Positive Test
    {
      query = ""
          + "CREATE QUERY test() FOR GRAPH testGraph{\n"
          + "  start = {person2.*};\n"
          + "  tmp =\n"
          + "    SELECT s FROM start:s - (star) - :t\n"
          + "      where s.name == \"Tom\";\n"
          + "  print tmp;\n"
          + "}\n";
      assertNotNull(compile(query));
    }

    // GLE-2263: Negative Test: missing left-hand side vSet var
    {
      query = ""
          + "CREATE QUERY test1() FOR GRAPH testGraph {\n"
          + "  start = {person2.*};\n"
          + "    SELECT s FROM start:s - (star) - :t\n"
          + "      where s.name == \"Tom\";\n"
          + "}\n";
      err = "Need a vSet var on the left-hand side. Eg: tmp = SELECT ... FROM ...";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2601", err);
    }

    // GLE-2263: Positive Test
    {
      query = ""
          + "CREATE QUERY test2() FOR GRAPH testGraph{\n"
          + "  start = {person2.*};\n"
          + "  tmp =\n"
          + "    SELECT s FROM start:s - (star) - :t\n"
          + "      where s.name == \"Tom\";\n"
          + "  print tmp;\n"
          + "}\n";
      assertNotNull(compile(query));
    }

    // GLE-2263: Positive Test, INTO table
    {
      query = ""
          + "CREATE QUERY test3() FOR GRAPH testGraph{\n"
          + "SELECT COUNT(  v.name) AS cnt INTO T\n"
          + "FROM  person2:v;\n"
          + "PRINT T;\n"
          + "}\n";
      assertNotNull(compile(query));
    }

    // GLE-2263: Negative Test: missing left-hand side vSet var
    {
      query = ""
          + "CREATE QUERY test4() FOR GRAPH testGraph{\n"
          + "  start = {person2.*};\n"
          + "    SELECT s FROM start:s - (star) - :t\n"
          + "      where s.name == \"Tom\";\n"
          + "}\n";
      err = "Need a vSet var on the left-hand side. Eg: tmp = SELECT ... FROM ...";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2601", err);
    }
  }

  @Test
  public void testStatementLHVarSemCheck() {
    // GLE-2182: Tests for missing LHV with assignment or accumulator
    // for semantic check SEM-2701
    Setup("empty_graph");
    String query, err;

    // 1) Mathematic Operations:
    // Positive test
    {
      query = ""
        + "create query test_math_pass() for graph empty_graph {\n"
        + "  int i, ii;\n"
        + "  i = 1;\n"
        + "  ii = (i + (i * 1) - (i / 1));\n"
        + "  print ii;\n"
        + "}\n";

      // compile the query - should pass
      assertNotNull(compile(query));
    }

    // Negative test, should throw semantic error
    {
      query = ""
        + "create query test_math_fail() for graph empty_graph {\n"
        + "  int i;\n"
        + "  i = 1;\n"
        + "  (i + (i * 1) - (i / 1));\n"
        + "}\n";

      // compile the query - should fail
      err = "Expected a left-hand side variable for this statement";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2701", err);
    }

    // 2) Set Operations:
    // Positive test
    {
      query = ""
        + "create query test_set_pass() for graph empty_graph {\n"
        + "  SetAccum<int> @@i, @@ii, @@iii;\n"
        + "  @@i   = (1,2,3);\n"
        + "  @@ii  = (2,3,4);\n"
        + "  @@iii = @@ii intersect (@@i union @@ii);\n"
        + "  @@ii += @@ii intersect (@@i union @@ii);\n"
        + "  print @@iii;\n"
        + "}\n";

      // compile the query - should pass
      assertNotNull(compile(query));
    }

    // Negative test, should throw semantic error
    {
      query = ""
        + "create query test_set_fail() for graph empty_graph {\n"
        + "  SetAccum<int> @@i, @@ii;\n"
        + "  @@i   = (1,2,3);\n"
        + "  @@ii  = (2,3,4);\n"
        + "  @@ii intersect (@@i union @@ii);\n"
        + "}\n";

      err = "Expected a left-hand side variable for this statement";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2701", err);
    }

    // 3) Scalar Operations
    // Positive test
    {
      query = ""
        + "create query test_scalar_pass() for graph empty_graph {\n"
        + "  int i, ii;\n"
        + "  i  = 1;\n"
        + "  ii = max((i,3,5));\n"
        + "  print ii;\n"
        + "}\n";

      // compile the query - should pass
      assertNotNull(compile(query));
    }

    // Negative test, should throw semantic error
    {
      query = ""
        + "create query test_scalar_fail() for graph empty_graph {\n"
        + "  int i, ii;\n"
        + "  i = 1;\n"
        + "  max((i,3,5));\n"
        + "}\n";

      // compile the query - should fail
      err = "Expected a left-hand side variable for this statement";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2701", err);
    }

    // 4) Functions
    // Print Positive test, no need for LHV
    {
      query = ""
        + "create query test_print_pass() for graph empty_graph {\n"
        + "  int i, ii;\n"
        + "  i = 1;\n"
        + "  ii = (i + (i * 1) - (i / 1));\n"
        + "  print ii, (i + (i * 1) - (i / 1));\n"
        + "}\n";

      // compile the query - should pass
      assertNotNull(compile(query));
    }

    // Nested Object Function Positive test, no need for LHV
    {
      query = ""
        + "create query test_func_pass () for graph empty_graph {\n"
        + "  SetAccum<Int> @@iSet;"
        + "  (((@@iSet.clear())));"
        + "}\n";

      // compile the query - should pass
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testSubQueryInBatchMode() {
    Setup("poc_graph");
    String qCallee = String.join("\n",
        "CREATE BATCH QUERY test4() {",
        "  SumAccum<int> @@sum;",
        "  SumAccum<int> @vsum;",
        "  start = {company.*};",
        "  start =",
        "    SELECT start",
        "    FROM start-(:e)-:tgt",
        "    ACCUM @@sum += start.outdegree();",
        "}"
    );
    // call runCmd() instead of compile() so that this callee can be registered in Catalog
    assertThat(command_.runCmd(qCallee), startsWith("Successfully created queries: [test4]."));
    String qCaller = String.join("\n",
        "CREATE BATCH QUERY test5() {",
        "  SumAccum<int> @@sum;",
        "  SumAccum<int> @vsum;",
        "  start = {company.*};",
        "  start =",
        "    SELECT start",
        "    FROM start-(:e)-:tgt",
        "    ACCUM test4();",
        "}"
    );
    assertNull(compile(qCaller));
    assertError(errMsg,
        "SEM-2602", "a distributed query is not allowed to call another distributed query.");

    qCaller = String.join("\n",
        "CREATE BATCH QUERY test6() {",
        "  SumAccum<int> @@sum;",
        "  SumAccum<int> @vsum;",
        "  start = {company.*};",
        "  test4();",
        "}"
    );
    assertNull(compile(qCaller));
    assertError(errMsg,
        "SEM-2602", "a distributed query is not allowed to call another distributed query.");

    // UDF/single GPR query cannot call a distributed query in ACCUM/POST-ACCUM clause
    qCaller = String.join("\n",
        "CREATE QUERY test7() {",
        "  start = {company.*};",
        "  start =",
        "    SELECT start",
        "    FROM start-(:e)-:tgt",
        "    ACCUM test4();",
        "}"
    );
    assertNull(compile(qCaller));
    assertError(errMsg, "SEM-2602",
        "The query 'test7' cannot call a distributed query 'test4'.");
    qCaller = String.join("\n",
        "CREATE QUERY test8() {",
        "  start = {company.*};",
        "  start =",
        "    SELECT start",
        "    FROM start-(:e)-:tgt",
        "    POST-ACCUM test4();",
        "}"
    );
    assertNull(compile(qCaller));
    assertError(errMsg, "SEM-2602",
        "The query 'test8' cannot call a distributed query 'test4'.");
    qCaller = String.join("\n",
        "CREATE QUERY test9() {",
        "  SumAccum<int> @@sum;",
        "  SumAccum<int> @vsum;",
        "  start = {company.*};",
        "  test4();",
        "}"
    );
    assertNull(compile(qCaller));
    assertError(errMsg,
        "SEM-2602", "The query 'test9' cannot call a distributed query 'test4'.");
  }

  /**
   * Series of tests for missing parenthesis when disjunction of edge/vertex types is used.
   * Parentheses are required for disjunction of edge and vertex types
   * when alias is provided.
   * Negative test cases throw semantic check errors.
   */

  // negative case 1 - single-hop with edge disjunction
  @Test
  public void testMissingParenInDisjTypesN1() {
    Setup("ldbc_snb_v2");
    String err = "Disjunction of edge types must be enclosed with parentheses "
        + "if an alias is applied: -((E1|E2):e)->";

    String query = String.join("\n",
        "create query test1() for graph ldbc_snb {",
        "  Start = {Person.*};",
        "  vSet = SELECT t",
        "    FROM Start:s - (HAS_CREATOR_REVERSE|LIKES:e) -> Post:t",
        "    ;",
        "}"
    );
    assertNull(compile(query));
    assertError(errMsg, "SEM-1701", err);
  }

  // negative case 2 - single-hop with vertex disjunction
  @Test
  public void testMissingParenInDisjTypesN2() {
    Setup("ldbc_snb_v2");
    String err = "Disjunction of vertex types must be enclosed with parentheses "
        + "if an alias is applied: (V1|V2):e";

    String query = String.join("\n",
        "create query test2() for graph ldbc_snb {",
        "  Start = {Person.*};",
        "  vSet = SELECT t",
        "    FROM Start:s - ((LIKES|IS_LOCATED_IN|HAS_INTEREST):e) -> Post|City|Tag:t",
        "    ;",
        "}"
    );
    assertNull(compile(query));
    assertError(errMsg, "SEM-1701", err);
  }

  // positive case
  @Test
  public void testMissingParenInDisjTypesP1() {
    Setup("ldbc_snb_v2");

    String query = String.join("\n",
        "create query test3() for graph ldbc_snb {",
        "  Start = {Person.*};",
        "  vSet1 = SELECT t",
        "    FROM Start:s - ((LIKES|IS_LOCATED_IN|HAS_INTEREST):e) -> (Post|City|Tag):t;",
        "  vSet2 = SELECT t",
        "    FROM Start:s - (HAS_CREATOR_REVERSE|LIKES) -> Post:t;",
        "}"
    );
    assertNotNull(compile(query));
  }

  // non-reserved keywords used as vertex set name
  @Test
  public void testNonReservedKeyWords() {
    Setup("ldbc_snb_v2");

    String query = String.join("\n",
        "CREATE OR REPLACE QUERY test1(STRING vType1,STRING vType2) SYNTAX _V2{",
        "  Start = {vType1};",
        "  Tgt = {vType2};",
        "  result = select s",
        "      FROM Start:s-(:e1)-Tgt:t1-(:e2)-Tgt:t2;",
        "}"
    );
    assertNotNull(compile(query));

    query = String.join("\n",
        "CREATE OR REPLACE QUERY test2(STRING vType1,STRING vType2) SYNTAX _V2{",
        "  Start = {vType1};",
        "  TGT = {vType2};",
        "  result = select s",
        "      FROM Start:s-(:e1)-TGT:t1-(:e2)-TGT:t2;",
        "}"
    );
    assertNotNull(compile(query));

    query = String.join("\n",
        "CREATE OR REPLACE QUERY test3(STRING vType1,STRING vType2) SYNTAX _V2{",
        "  Start = {vType1};",
        "  tgt = {vType2};",
        "  result = select s",
        "      FROM Start:s-(:e1)-tgt:t1-(:e2)-tgt:t2;",
        "}"
    );
    assertNotNull(compile(query));
  }

  // use non-reserved keyword "function" as attribute name
  @Test
  public void testNonReservedKeyWordFunction() {
    String cmd = "Create vertex Src(primary_id name String, function String)";
    final String output = "Successfully created vertex types: [Src].\n";
    assertEquals(output, command_.runCmd(cmd));

    cmd = String.join("\n",
                             "CREATE GLOBAL SCHEMA_CHANGE JOB drop_function {",
                             "  ALTER VERTEX Src DROP ATTRIBUTE (function);",
                             "}"
                            );
    String result = command_.runCmd(cmd);
    assertTrue(result.contains("Successfully created global schema change jobs:"));
  }

  @Test
  public void testMultiedgeUpdateDiscriminator() {
    Setup("multiedge");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace DISTRIBUTED query updateDis1() {",
          "  p1 = {Account.*};",
          "  p1 = SELECT p FROM p1:p - (transfer>:e) - Account:t",
          "                where p.id==5",
          "                ACCUM e.id =1234455;",
          "}"
      );
      err = "Cannot update the discriminator attribute 'id' in Edge 'transfer'";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2603", err);
    }
    {
      query = String.join("\n",
          "create or replace query updateDis2() {",
          "  p1 = {Account.*};",
          "  p1 = SELECT p FROM p1:p - (transfer>:e) - Account:t",
          "                where p.id==5",
          "                ACCUM e.setAttr(\"id\",123456);",
          "}"
      );
      err = "Cannot update the discriminator attribute 'id' in Edge 'transfer'";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2603", err);
    }
    {
      query = String.join("\n",
          "create or replace DISTRIBUTED query updateDis3() {",
          "p1 = {Account.*};",
          "UPDATE e",
          "FROM p1:p -(transfer2:e)- Account:t",
          "SET e.id2 = ********;",
          "}"
      );
      err = "Cannot update the discriminator attribute 'id2' in Edge 'transfer2'";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2603", err);
    }
    {
      query = String.join("\n",
          "create or replace DISTRIBUTED query updateDis4() {",
          "p1 = {Person.*};",
          "p1 = SELECT p FROM p1:p - (:e) - :t",
          "              ACCUM e.id2 = e.id2 + 1;",
          "}"
      );
      err = "Cannot update the discriminator attribute 'id2' in Edge 'transfer2'";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2603", err);
    }
    {
      query = String.join("\n",
        "create or replace query updateDis5 () {",
          "SetAccum<EDGE<transfer>> @@set;",
          "p1 = {Account.*};",
          "p1 = SELECT p FROM p1:p - (transfer>:e) - :t",
          "              ACCUM @@set += e;",
          "foreach e in @@set do",
          "   e.id = 10000;",
          "end;",
        "}"
      );
      err = "Cannot update the discriminator attribute 'id' in Edge 'transfer'";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2603", err);
    }
  }

  @Test
  public void testInsertDiscriminator() {
    Setup("multiedge");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace query insertDiscriminator () {",
          "INSERT INTO invest (FROM, TO, DISCRIMINATOR(id, creationDate))",
          " VALUES (99, 93, 1, to_datetime(\"2022-12-01\"));",
          "}"
      );
      err = "discriminator has 2 entries, but edge type 'invest' has 1 entries.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-2604", err);
    }
  }

  @Test
  public void test_insert() {
    Setup("poc_graph");
    String err;
    String query = String.join("\n",
        "create or replace query test_insert(vertex<company> t) {",
        "INSERT INTO members (PRIMARY_ID, id) VALUES (\" m1 \", \"Kevin\");",
        "INSERT INTO member_work_company (FROM, TO, id) VALUES (\" m1 \" wrongtype, t, \" id1 \");",
        "}");
    err = "Insert statement includes a vertex type that is not in the graph.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2801", err);
  }

  // Unit tests for checking roleName in is_granted_to_current_roles(roleName)
  // Negative 1: statement level print action, contains `\"`
  @Test
  public void testValidRoleName_Neg_1() {
    Setup("ldbc_snb_v2");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace query constString() {",
          "  print Is_Granted_to_Current_roles(\"\\\"01sd\");",
          "}"
      );
    }
    err = "The role name format is invalid.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2901", err);
  }
  // Negative 2: block level where clause, contains `\n`
  @Test
  public void testValidRoleName_Neg_2() {
    Setup("ldbc_snb_v2");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace query constString() {",
          "  Start = {Person.*};",
          "  result = SELECT s",
          "           FROM Start:s",
          "           WHERE Is_Granted_to_Current_roles(\"\\n01sd\")",
          "           ;",
          "}"
      );
    }
    err = "The role name format is invalid.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2901", err);
  }
  // Negative 3: subquery statement level in return, contains `\\`
  @Test
  public void testValidRoleName_Neg_3() {
    Setup("ldbc_snb_v2");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace query constString() RETURNS (BOOL) {",
          "  return Is_Granted_to_Current_roles(\"\\\\ds_\");",
          "}"
      );
    }
    err = "The role name format is invalid.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2901", err);
  }
  // Negatvie 4: subquery block level, starting with digit
  @Test
  public void testValidRoleName_Neg_4() {
    Setup("ldbc_snb_v2");
    String query;
    String err;
    {
      query = String.join("\n",
          "create or replace query constString() RETURNS (INT) {",
          "  Start = {Person.*};",
          "  result = SELECT s",
          "           FROM Start:s",
          "           WHERE Is_Granted_to_Current_roles(\"01sd\")",
          "           ;",
          "  return 1;",
          "}"
      );
    }
    err = "The role name format is invalid.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2901", err);
  }

  // Unit tests for checking roleName in is_granted_to_current_roles(roleName)
  // Postive 1: starting with underscore
  @Test
  public void testValidRoleName_Pos_1() {
    Setup("ldbc_snb_v2");
    String query;
    {
      query = String.join("\n",
          "create or replace query constString() {",
          "  print Is_Granted_to_Current_roles(\"_0ds\");",
          "}"
      );
    }
    assertNotNull(compile(query));
  }
  // Positive 2: starting with english character, ends with underscore
  @Test
  public void testValidRoleName_Pos_2() {
    Setup("ldbc_snb_v2");
    String query;
    {
      query = String.join("\n",
          "create or replace query constString() {",
          "  Start = {Person.*};",
          "  result = SELECT s",
          "           FROM Start:s",
          "           WHERE Is_Granted_to_Current_roles(\"ds01d_\")",
          "           ;",
          "}"
      );
    }
    assertNotNull(compile(query));
  }
  // Positive 3: starting with underscore, contains underscore
  @Test
  public void testValidRoleName_Pos_3() {
    Setup("ldbc_snb_v2");
    String query;
    {
      query = String.join("\n",
          "create or replace query constString() RETURNS (BOOL) {",
          "  return Is_Granted_to_Current_roles(\"_ds_01\");",
          "}"
      );
    }
    assertNotNull(compile(query));
  }
  // Positive 4: start with underscore contains only digit
  @Test
  public void testValidRoleName_Pos_4() {
    Setup("ldbc_snb_v2");
    String query;
    {
      query = String.join("\n",
          "create or replace query constString() RETURNS (INT) {",
          "  Start = {Person.*};",
          "  result = SELECT s",
          "           FROM Start:s",
          "           WHERE Is_Granted_to_Current_roles(\"_12312\")",
          "           ;",
          "  return 1;",
          "}"
      );
    }
    assertNotNull(compile(query));
  }

  // Negative: loadAccum loads a vertex with a row policy
  @Test
  public void testLoadAccumWithVertexHasRowPolicy() {
    // setup schema
    Setup("ldbc_snb_v2");
    CatalogManager.unsetGraph();
    // create packages, functions and set a row policy
    assertEquals("Successfully created Packages: [pkg1].\n",
        command_.runCmd("create package pkg1"));
    assertEquals("Successfully created Packages: [pkg1.pkg2].\n",
        command_.runCmd("create package pkg1.pkg2"));
    assertEquals("Successfully created functions: [pkg1.pkg2.func1].\n",
        command_.runCmd("create function pkg1.pkg2.func1(string i) returns(bool) { return true;}"));
    Policy p1 = new Policy();
    p1.PolicyName = "global$Person$Policy";
    p1.GraphId = 0;
    p1.TypeName = "Person";
    p1.AttrNames = Arrays.asList("gender");
    p1.FuncName = "pkg1.pkg2.func1";
    p1.isRowPolicy = true;
    p1.isReadPolicy = true;
    CatalogManager.getGlobalCatalog().getInMemoryPolicies()
        .add(CatalogManager.getGlobalCatalog(), p1);
    CatalogManager.setGraph("ldbc_snb_v2");
    String query = String.join("\n",
        "create query loadAccumWithVertexHasRowPolicy(string filename) {",
        "MapAccum<VERTEX<Person>, INT> @@test_map;",
        "@@test_map = { LOADACCUM (filename, $0, $1, \",\", false)};",
        "print @@test_map;",
        "}"
    );
    String err = "Can not load the vertex type \"Person\" when it has a row policy.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2902", err);
  }

  // Negative: loadAccum loads a vertex in a container with a row policy
  @Test
  public void testLoadAccumWithVertexInContainerHasRowPolicy() {
    // setup schema
    Setup("ldbc_snb_v2");
    CatalogManager.unsetGraph();
    // create packages, functions and set a row policy
    assertEquals("Successfully created Packages: [pkg1].\n",
        command_.runCmd("create package pkg1"));
    assertEquals("Successfully created Packages: [pkg1.pkg2].\n",
        command_.runCmd("create package pkg1.pkg2"));
    assertEquals("Successfully created functions: [pkg1.pkg2.func1].\n",
        command_.runCmd("create function pkg1.pkg2.func1(string i) returns(bool) { return true;}"));
    Policy p1 = new Policy();
    p1.PolicyName = "global$Person$Policy";
    p1.GraphId = 0;
    p1.TypeName = "Person";
    p1.AttrNames = Arrays.asList("gender");
    p1.FuncName = "pkg1.pkg2.func1";
    p1.isRowPolicy = true;
    p1.isReadPolicy = true;
    CatalogManager.getGlobalCatalog().getInMemoryPolicies()
        .add(CatalogManager.getGlobalCatalog(), p1);
    CatalogManager.setGraph("ldbc_snb_v2");
    String query = String.join("\n",
        "create query loadAccumWithVertexInContainerHasRowPolicy(string filename) {",
        "TYPEDEF TUPLE<STRING aaa, VERTEX<Person> ddd> Your_Tuple;",
        "MapAccum<String, MapAccum<INT, Your_Tuple>> @@test_map;",
        "@@test_map = { LOADACCUM (filename, $0, $1, $2, $3, \",\", false)};",
        "print @@test_map;",
        "}"
    );
    String err = "Can not load the vertex type \"Person\" when it has a row policy.";
    assertNull(compile(query));
    assertError(errMsg, "SEM-2902", err);
  }


  @Test
  public void testBlockVertexSetComparison() {
    Setup("ldbc_snb_v2");
    List<String> operators = List.of("==", "!=", "<", "<=", ">", ">=");

    String query = "CREATE OR REPLACE QUERY test(){\n"
        + "  vset1 = {Comment.*};\n"
        + "  if vset1 == vset1 then\n"
        + "    print 1;\n"
        + "  end;\n"
        + "}";
    for (String operator : operators) {
      String _query = query.replace("==", operator);
      assertNull(compile(_query));
      assertError(errMsg, "500",
          "Incompatible operand types Vertex Set and Vertex Set for the operator =="
              .replace("==", operator));
    }
  }

  @Test
  public void testBlockVertexSetOperator() {
    Setup("ldbc_snb_v2");
    String query = "CREATE OR REPLACE QUERY test(){\n"
        + "  vset1 = {Comment.*};\n"
        + "  PRINT NOT vset1;\n"
        + "}";
    assertNull(compile(query));
    assertError(errMsg, "121",
        "vertex<Comment> cannot be a condition!");

    query = "CREATE OR REPLACE QUERY test(){\n"
        + "  vset1 = {Comment.*};\n"
        + "  PRINT vset1 where vset1 == vset1;\n"
        + "}";
    assertNull(compile(query));
    assertError(errMsg, "500",
        "Incompatible operand types Vertex Set and Vertex Set for the operator ==");
  }
}