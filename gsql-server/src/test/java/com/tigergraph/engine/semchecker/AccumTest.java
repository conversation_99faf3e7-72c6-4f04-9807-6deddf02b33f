package com.tigergraph.engine.semchecker;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import com.tigergraph.engine.TestCase;
import com.tigergraph.schema.plan.query.QuerySignature;

import org.junit.Test;

public class AccumTest extends TestCase {
  @Test
  public void testGlobalAccumSameLineDeclarationWithDuplicateName() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n1() for graph empty_graph {",
      "  SumAccum<int> @@sum1, @@sum1;",
      "  print \"yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "The global accum name \"@@sum1\" has already been declared in the same scope.";
    assertError(errMsg, "SEM-10", err);
  }

  @Test
  public void testVertexAccumSameLineDeclarationWithDuplicateName() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n2() for graph empty_graph {",
      "  SumAccum<int> @sum1, @sum1;",
      "  print \"yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "The accum name \"@sum1\" has already been declared in the same scope.";
    assertError(errMsg, "SEM-11", err);
  }

  @Test
  public void testGlobalAccumSeparateLineDeclarationWithDuplicateName() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n3() for graph empty_graph {",
      "  SumAccum<int> @@sum1;",
      "  SetAccum<int> @@sum1;",
      "  print \"yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "The global accum name \"@@sum1\" has already been declared in the same scope.";
    assertError(errMsg, "SEM-10", err);
  }

  @Test
  public void testVertexAccumSeparateLineDeclarationWithDuplicateName() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n4() for graph empty_graph {",
      "  SumAccum<int> @sum1;",
      "  SetAccum<int> @sum1;",
      "  print \"yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "The accum name \"@sum1\" has already been declared in the same scope.";
    assertError(errMsg, "SEM-11", err);
  }

  @Test
  public void testStaticVertexAccumDeclaration() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query n5() for graph empty_graph {",
      "  static SumAccum<int> @sum1;",
      "  print \"yes\";",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n5 (SEM-1501): line 2, col 23"
        + "\nSTATIC cannot be applied to local accumulators such as @sum1. Either change"
        + "\naccumulator @sum1 to a global accumulator or remove the keyword STATIC.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testListAccumInSumAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  SumAccum<ListAccum<int>> @@count;",
      "  print @@count;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nListAccum cannot be nested within a SumAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testSumAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  ListAccum<SumAccum<int>> @@count;",
      "  print @@count;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 12"
               + "\nSumAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testBagAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  ListAccum<BagAccum<edge>> @@globalList;",
      "  print @@globalList;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 12"
               + "\nBagAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testBagAccumInSetAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  SetAccum<BagAccum<edge>> @@globalSet;",
      "  print @@globalSet;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nBagAccum cannot be nested within a SetAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testMapAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  ListAccum<MapAccum<int,edge>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 12"
               + "\nMapAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);

  }

  @Test
  public void testListAccumInBagAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  BagAccum<ListAccum<edge>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nListAccum cannot be nested within a BagAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testMapAccumInBagAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  BagAccum<MapAccum<int,edge>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nMapAccum cannot be nested within a BagAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testMapAccumInSetAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  SetAccum<MapAccum<int,edge>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nMapAccum cannot be nested within a SetAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testBagAccumInBagAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  BagAccum<BagAccum<edge>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nBagAccum cannot be nested within a BagAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testSetAccumInSetAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  SetAccum<SetAccum<vertex>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nSetAccum cannot be nested within a SetAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testSetAccumInBagAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  BagAccum<SetAccum<vertex>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 11"
               + "\nSetAccum cannot be nested within a BagAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testHeapAccumInSetAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  typedef tuple<int a, edge e> myTuple;",
      "  SetAccum<HeapAccum<myTuple>(1, a ASC)> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 3, col 11"
               + "\nHeapAccum cannot be nested within a SetAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testHeapAccumInBagAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  typedef tuple<int a, edge e> myTuple;",
      "  BagAccum<HeapAccum<myTuple>(1, a ASC)> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 3, col 11"
               + "\nHeapAccum cannot be nested within a BagAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testHeapAccumInMapAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  typedef tuple<int a, edge e> myTuple;",
      "  MapAccum<int, HeapAccum<myTuple>(1, a ASC)> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 3, col 16"
               + "\nHeapAccum cannot be nested within MapAccum accumulator!";
    assertEquals(err, errMsg);
  }


  @Test
  public void testHeapAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  typedef tuple<int a, edge e> myTuple;",
      "  ListAccum<HeapAccum<myTuple>(1, a ASC)> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 3, col 12"
               + "\nHeapAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testTwoLevelMapAccumInListAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  ListAccum<ListAccum<MapAccum<int, edge>>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 22"
               + "\nMapAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testTwoLevelSetAccumInMapAccumInListAccum() {
    Setup("empty_graph");
    String[] queryLines = {
      "create query test() for graph empty_graph {",
      "  ListAccum<MapAccum<int, SetAccum<edge>>> @@gacc;",
      "  print @@gacc;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query test (SEM-1301): line 2, col 12"
               + "\nMapAccum cannot be nested within ListAccum accumulator!";
    assertEquals(err, errMsg);
  }

  @Test
  public void testMapAccum() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - use OrAccum as key
    {
      query = ""
          + "create query n23() for graph poc_graph {\n"
          + "  MapAccum<OrAccum<bool>, int> @@m;\n"
          + "  print \"ok\";\n"
          + "}";
      err = "\nOrAccum cannot be used as key in a MapAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-1301", err);
    }

    // test 2 - ArrayAccum nested in MapAccum
    {
      query = ""
          + "create query n25() for graph poc_graph {\n"
          + "  MapAccum<int, ArrayAccum<SumAccum<string>>> @@map;\n"
          + "  print 1;\n"
          + "}";
      err = "\nArrayAccum cannot be nested within MapAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-1301", err);
    }

    // test 3 - BitwiseAccum nested in MapAccum is supported
    {
      query = ""
          + "create query n26() for graph poc_graph {\n"
          + "  MapAccum<INT, BitwiseOrAccum> @@map1;\n"
          + "  MapAccum<INT, BitwiseOrAccum<128>> @@map2;\n"
          + "  print 1;\n"
          + "}";
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testGroupByAccum() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - missing field name
    {
      query = ""
          + "create query test() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ListAccum<int>> @@group;\n"
          + "  print @@group;\n"
          + "}";
      err = "\nMissing alias. An alias is required for each type entry in GroupByAccum";
      assertNull(compile(query));
      assertError(errMsg, "SEM-602", err);
    }

    // test 2 - GroupByAccum.get().fieldAccess
    {
      query = ""
          + "create query n3() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ListAccum<int> b, MaxAccum<int> c> @@group;\n"
          + "  print @@group.get(1).a;\n"
          + "}";
      err = "\n@@group.get(1) doesn't have field a.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-531", err);
    }

    // test 3 - GroupByAccum.fieldAccess
    {
      query = ""
          + "create query n3() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ListAccum<int> b> @@group;\n"
          + "  foreach g in @@group do\n"
          + "    print g.c;\n"
          + "  end;\n"
          + "}";
      err = "\n@@group doesn't have field c.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-531", err);
    }

    // test 4 - GroupByAccum element mixes base and accumulator types
    {
      query = ""
          + "create query n5() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ListAccum<int> b, int c> @@group;\n"
          + "  print @@group;\n"
          + "}";
      err = "\nIt is not allowed to mix primitive types and accumulator types in GroupByAccum."
          + "\nThe syntax should be GroupByAccum<primitive_type_list, accumulator_type_list >";
      assertNull(compile(query));
      assertError(errMsg, "SEM-601", err);
    }

    // test 5 - duplicate field name
    {
      query = ""
          + "create query n8() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ListAccum<int> a> @@group;\n"
          + "  print @@group;\n"
          + "}";
      err = "GroupByAccum's type alias must be unique.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-604", err);
    }

    // test 6 - no accum type field
    {
      query = ""
          + "create query n9() for graph poc_graph {\n"
          + "  GroupByAccum<int a, int b> @@group;\n"
          + "  print @@group;\n"
          + "}";
      err = "\nGroupByAccum requires at least one accumulator type.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-603", err);
    }

    // test 7 - access field of inner GroupByAccum
    {
      query = ""
          + "create query n10() for graph poc_graph {\n"
          + "  GroupByAccum<int a, GroupByAccum<int i, SumAccum<int> s> b> @@group;\n"
          + "  print @@group.get(1).s;\n"
          + "}";
      err = "\n@@group.get(1) doesn't have field s.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-531", err);
    }

    // test 8 - ArrayAccum nested in GroupByAccum
    {
      query = ""
          + "create query n24() for graph poc_graph {\n"
          + "  GroupByAccum<int a, ArrayAccum<SumAccum<string>> b> @@group;\n"
          + "  print 1;\n"
          + "}";
      err = "\nArrayAccum cannot be nested within GroupByAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-1301", err);
    }
  }

  @Test
  public void testWriteAccumOfNonVertexType() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n1() for graph poc_graph {",
      "  SetAccum<string> @setIds;",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM tgt.@setIds += src.id;",
      "  F2 = SELECT tgt",
      "       FROM F1:src-(member_member:e)->:tgt",
      "       ACCUM",
      "             FOREACH vtx IN src.@setIds DO",
      "               vtx.@curId += tgt.id",
      "             END;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n1 (SEM-1201): line 12, col 15"
        + "\nThe loop variable 'vtx' is not VERTEX type, so accessing its accumulator is not"
        + "\nsupported.";
    assertEquals(err, errMsg);
  }

  /**
   * TODO: The error reported in this test case is outdated since now we can
   * write to the accumulator of any vertex.
   */
  @Test
  public void testWriteAccumOfVertexParam() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n3(vertex<members> vm) for graph poc_graph {",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM vm.@curId += src.id;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nType Check Error in query testGLE629n3 (TYP-15): line 6, col 13"
               + "\nThe alias vm cannot be determined.";
    assertEquals(err, errMsg);
  }

  // TODO: Error reported in the below test case is now outdated
  @Test
  public void testWriteAccumOfLocalVertexVariable() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n4() for graph poc_graph {",
      "  ListAccum<vertex> @@listV;",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM CASE WHEN @@listV.size() > 0 THEN",
      "               vertex vtx = @@listV.get(0),",
      "               vtx.@curId += src.id",
      "             END;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n4 (SEM-1413): line 8, col 15"
        + "\nIf you need update local vertex variable 'vtx' 's accumulator, it should come"
        + "\nfrom the source's or target's vertex-attached accumulator in current query"
        + "\nblock.";
    assertEquals(err, errMsg);
  }


  // TODO: The error reported here is actually outdated
  @Test
  public void testWriteAccumOfGlobalVertexVariable() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n5(vertex<members> vm) for graph poc_graph {",
      "  SumAccum<string> @curId;",
      "  vertex<members> gvm;",
      "  gvm = vm;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM gvm.@curId += src.id;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nType Check Error in query testGLE629n5 (TYP-15): line 8, col 13"
               + "\nThe alias gvm cannot be determined.";
    assertEquals(err, errMsg);
  }

  // TODO: The error reported below is outdated.
  @Test
  public void testWriteAccumOfVertexInParamSet() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n7(set<vertex<members>> setV) for graph poc_graph {",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM FOREACH vtx IN setV DO",
      "               vtx.@curId += src.id",
      "             END;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n7 (SEM-1412): line 7, col 15"
        + "\nThe foreach variable 'vtx' need come from the source's or target's"
        + "\nvertex-attached accumulator in current query block.";
    assertEquals(err, errMsg);
  }

  // TODO: The error reported below is outdated
  @Test
  public void testWriteAccumOfLoopVariableInPostAccum() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n15() for graph poc_graph {",
      "  ListAccum<vertex<members>> @listV;",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       POST-ACCUM",
      "             FOREACH vtx IN src.@listV DO",
      "               vtx.@curId += src.id",
      "             END;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n15 (SEM-1414): line 9, col 15"
        + "\nCannot update local vertex 'vtx' 's accumulator in POST-ACCUM clause, please do"
        + "\nit in ACCUM clause";
    assertEquals(err, errMsg);
  }

  @Test
  public void testWriteAccumOfLocalVariableInPostAccum() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n16() for graph poc_graph {",
      "  MaxAccum<vertex> @maxV;",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       POST-ACCUM",
      "           vertex<members> vtx = src.@maxV,",
      "           vtx.@curId += src.id;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n16 (SEM-1414): line 9, col 11"
        + "\nCannot update local vertex 'vtx' 's accumulator in POST-ACCUM clause, please do"
        + "\nit in ACCUM clause";
    assertEquals(err, errMsg);
  }

  @Test
  public void testVertexAccumOutsideDMLBlock() {
    Setup("poc_graph_2");
    String query = "", err = "";

    // Test 1 - write vertex accum outside of DML block
    {
      query = ""
          + "create query testGLE629n20() for graph poc_graph {\n"
          + "  ListAccum<vertex<members>> @@listV;\n"
          + "  SumAccum<string> @curId;\n"
          + "  FOREACH vtx IN @@listV DO\n"
          + "    vtx.@curId += \"123\"; // not allowed\n"
          + "  END;\n"
          + "}";
      err = "\nSemantic Check Error in query testGLE629n20 (SEM-105): line 5, col 4"
          + "\nA vertex-attached accumulator cannot be referred outside a DML block(except"
          + "\nPRINT statement).";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // Test 2 - read vertex accum outside of DML block
    {
      query = ""
          + "create query n108() for graph poc_graph {\n"
          + "  SumAccum<int> @count;\n"
          + "  SumAccum<int> @@A;\n"
          + "  Start = { company.* };\n"
          + "  @@A += Start.@count;\n"
          + "}";
      err = "\nSemantic Check Error in query n108 (SEM-105): line 5, col 9"
          + "\nA vertex-attached accumulator cannot be referred outside a DML block(except"
          + "\nPRINT statement).";
    }
  }

  @Test
  public void testRedeclareLocalVertexWithAccumUpdate() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n13() for graph poc_graph {",
      "  MaxAccum<vertex<members>> @maxV;",
      "  SetAccum<vertex<members>> @setV;",
      "  SumAccum<string> @curId;",
      "  F0 = {members.*};",

      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM tgt.@maxV += src, tgt.@setV += src;",

      "  F4 = SELECT tgt",
      "       FROM F1:src-(member_member:e)->:tgt",
      "       ACCUM vertex<members> vtx1 = tgt.@maxV,",
      "             FOREACH vtx IN src.@setV DO",
      "               vertex<members> vtx1 = src.@maxV,",
      "               vtx1.@curId += tgt.id",
      "             END,",
      "             vtx1.@curId += src.id;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n13 (SEM-1410): line 13, col 15"
        + "\nRedeclaring local vertex variable 'vtx1' is not allowed, since its accumulator"
        + "\nis updated.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testReassignLocalVertexWithAccumUpdate() {
    Setup("poc_graph_2");
    String[] queryLines = {
      "create query testGLE629n11() for graph poc_graph {",
      "  MaxAccum<vertex<members>> @maxV;",
      "  SetAccum<vertex<members>> @setV;",
      "  SumAccum<string> @curId;",

      "  F0 = {members.*};",
      "  F1 = SELECT tgt",
      "       FROM F0:src-(member_member:e)->:tgt",
      "       ACCUM tgt.@maxV += src, tgt.@setV += src;",

      "  F3 = SELECT tgt",
      "       FROM F1:src-(member_member:e)->:tgt",
      "       ACCUM vertex<members> vtx1 = tgt.@maxV,",
      "             vtx1.@curId += src.id,",
      "             vtx1 = src.@maxV;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query testGLE629n11 (SEM-1411): line 13, col 13"
        + "\nThere is update on local vertex 'vtx1', it cannot be assigned again";
    assertEquals(err, errMsg);
  }

  @Test
  public void testReadAccumOfLoopVertexVariableBlockLevel() {
    Setup("person_movie");
    String[] queryLines = {
      "create query n2() for graph person_movie syntax v1 {",
      "  SumAccum<int> @@sum;",
      "  SumAccum<int> @cnt;",
      "  Start = {person.*};",
      "  L1 = select src from Start:src where src.sid == 2;",
      "  S =",
      "    select u from L1-(:e)-movie:u",
      "    accum foreach i in (u, u) do",
      "            @@sum += i.@cnt",
      "          end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n2 (SEM-1201): line 9, col 21"
        + "\nAccessing the accumulator of the loop variable 'i' is not supported.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testReadAccumOfLoopVertexVariableStatementLevel() {
    Setup("person_movie");
    String[] queryLines = {
      "create query n3() for graph person_movie {",
      "  SetAccum<vertex> @@vs;",
      "  SumAccum<int> @cnt;",
      "  foreach a in vs do",
      "    print a.@cnt;",
      "  end;",
      "}"
    };

    // compile the query - should fail
    String query = String.join("\n", queryLines);
    QuerySignature qs = compile(query);
    assertNull(qs);

    // check error message
    String err = "\nSemantic Check Error in query n3 (SEM-1201): line 5, col 10"
        + "\nAccessing the accumulator of the loop variable 'a' is not supported.";
    assertEquals(err, errMsg);
  }

  @Test
  public void testAccumDeclaration() {
    Setup("empty_graph");
    String query = "", err = "";

    // test 1 - unsupported accumulator
    {
      query = ""
          + "create query n12() for graph empty_graph {\n"
          + "  OrAccum2 @@acc;\n"
          + "  print \"yes\";\n"
          + "}";
      err = "\nSemantic Check Error in query n12 (SEM-8): line 2, col 2"
          + "\nThe accumulator type OrAccum2 is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - SumAccum<bool> is unsupported
    // TODO: We should suggest supported data type here
    {
      query = ""
          + "create query n16() for graph empty_graph {\n"
          + "  SumAccum<bool> @@acc;\n"
          + "  print \"yes\";\n"
          + "}";
      err = "\nSemantic Check Error in query n16 (SEM-9): line 2, col 11"
          + "\nThe accumulator type SumAccum is not supported with data type bool!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 3 - AvgAccum cannot be given an element type
    {
      query = ""
          + "create query n17() for graph empty_graph {\n"
          + "  AvgAccum<bool> @@acc;\n"
          + "  print \"yes\";\n"
          + "}";
      err = "\nSemantic Check Error in query n17 (SEM-8): line 2, col 10"
          + "\nThe accumulator type AvgAccum cannot be given an element type.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 4 - AndAccum<int> and OrAccum<int> are supported
    {
      query = ""
          + "create query n19() for graph empty_graph {\n"
          + "  AndAccum<int> @@acc1;\n"
          + "  OrAccum<int> @@acc2;\n"
          + "  print \"yes\";\n"
          + "}";
      assertNotNull(compile(query));
    }

    // test 5 - wrong syntax for declaring HeapAccum
    // TODO: This should really be reported as a syntax error
    {
      query = ""
          + "create query n113() for graph empty_graph {\n"
          + "  HeapAccum<int> @@count;\n"
          + "  print \"ok\";\n"
          + "}";
      err = "\nSemantic Check Error in query n113 (SEM-113): line 2, col 2"
          + "\nTo define a HeapAccum please follow the grammar below:"
          + "\nHeapAccum <TUPLE_NAME> (HEAP_SIZE, SORT_KEY ASC|DESC...)";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 6 - no element type given to SetAccum
    {
      query = String.join("\n",
        "create query test10() for graph empty_graph {",
        "  SetAccum @@set;",
        "  print @@set;",
        "}"
      );
      err = "\nThe accumulator type SetAccum must specify a data type.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-8", err);
    }
  }

  @Test
  public void testAccumChangeDuringIteration() {
    Setup("empty_graph");
    String query = "", err = "";

    // test 1 - alter MapAccum during iteration
    {
      query = ""
          + "create query n236() for graph empty_graph {\n"
          + "  MapAccum<int,int> @@map;\n"
          + "  foreach (a,b) in @@map do\n"
          + "    print \"b:\", b;\n"
          + "    @@map += (1->2);\n"
          + "  end;\n"
          + "}";
      err = "\nSemantic Check Error in query n236 (SEM-129): line 5, col 4"
          + "\nThe value of the set expression '@@map', which is being iterated, cannot be"
          + "\nchanged.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testInvalidTypeInAccum() {
    Setup("poc_graph");
    {
      // wrong accumulator - LIST
      String query =
          "CREATE QUERY testGLE601n1 () FOR GRAPH poc_graph   {\n"
              + "  MapAccum<vertex<company>, LIST<double>> @@mapList;\n"
              + "  PRINT @@mapList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n1 (SEM-8): line 2, col 28\n"
          + "The accumulator type LIST is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // wrong accumulator - SET
      String query =
          "CREATE QUERY testGLE601n2 () FOR GRAPH poc_graph   {\n"
              + "  MapAccum<vertex<company>, SET<double>> @@mapList;\n"
              + "  PRINT @@mapList;\n"
              + "}";
      String err = "line 2:28 no viable alternative at input 'MapAccum<vertex<company>, set'\n"
          + "line 2:26 mismatched input ',' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "line 2:28 mismatched input 'set' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "line 2:38 mismatched input '>' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "Parsing encountered 4 syntax error(s)\n\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
    {
      // wrong accumulator - MAP
      String query =
          "CREATE QUERY testGLE601n3 () FOR GRAPH poc_graph   {\n"
              + "  MapAccum<vertex<company>, MAP<double, int>> @@mapList;\n"
              + "  PRINT @@mapList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n3 (SEM-8): line 2, col 28\n"
          + "The accumulator type MAP is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // wrong accumulator - BAG
      String query =
          "CREATE QUERY testGLE601n4 () FOR GRAPH poc_graph   {\n"
              + "  MapAccum<vertex<company>, BAG<double>> @@mapList;\n"
              + "  PRINT @@mapList;\n"
              + "}";
      String err = "line 2:28 no viable alternative at input 'MapAccum<vertex<company>, bag'\n"
          + "line 2:26 mismatched input ',' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "line 2:28 mismatched input 'bag' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "line 2:38 mismatched input '>' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "Parsing encountered 4 syntax error(s)\n\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
    {
      // wrong accumulator - mytype
      String query =
          "CREATE QUERY testGLE601n5 () FOR GRAPH poc_graph   {\n"
              + "  MapAccum<vertex<company>, mytype<double>> @@mapList;\n"
              + "  PRINT @@mapList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n5 (SEM-8): line 2, col 28\n"
          + "The accumulator type mytype is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // wrong accumulator - LIST
      String query =
          "CREATE QUERY testGLE601n6 () FOR GRAPH poc_graph   {\n"
              + "  ListAccum<LIST<double>> @@myList;\n"
              + "  PRINT @@myList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n6 (SEM-8): line 2, col 12\n"
          + "The accumulator type LIST is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // wrong accumulator - SET
      String query =
          "CREATE QUERY testGLE601n7 () FOR GRAPH poc_graph   {\n"
              + "  ListAccum<SET<double>> @@myList;\n"
              + "  PRINT @@myList;\n"
              + "}";
      String err = "line 2:23 mismatched input '>' expecting '('\n"
          + "Parsing encountered 1 syntax error(s)\n\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
    {
      // wrong accumulator - MAP
      String query =
          "CREATE QUERY testGLE601n8 () FOR GRAPH poc_graph   {\n"
              + "  ListAccum<MAP<int, double>> @@myList;\n"
              + "  PRINT @@myList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n8 (SEM-8): line 2, col 12\n"
          + "The accumulator type MAP is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
    {
      // wrong accumulator - BAG
      String query =
          "CREATE QUERY testGLE601n9 () FOR GRAPH poc_graph   {\n"
              + "  ListAccum<BAG<double>> @@myList;\n"
              + "  PRINT @@myList;\n"
              + "}";
      String err = "line 2:12 no viable alternative at input 'ListAccum<bag'\n"
          + "line 2:22 mismatched input '>' expecting {ABORT, BY, COMMIT, DISTINCT, FILE, "
          + "FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, MATCH, NOW, PATH, PER, REPLACE, "
          + "SRC, TGT, TO_DATETIME, UPDATE, NAME}\n"
          + "Parsing encountered 2 syntax error(s)\n\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
    {
      // wrong accumulator - mytype
      String query =
          "CREATE QUERY testGLE601n10 () FOR GRAPH poc_graph   {\n"
              + "  ListAccum<mytype<double>> @@myList;\n"
              + "  PRINT @@myList;\n"
              + "}";
      String err = "\nSemantic Check Error in query testGLE601n10 (SEM-8): line 2, col 12\n"
          + "The accumulator type mytype is not supported!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testEdgeInSumAccum() {
    Setup("poc_graph");
    String query = String.join("\n",
        "create query test() for graph poc_graph {",
        "  SumAccum<EDGE> @@acc;",
        "  print @@acc;",
        "}");
    String err = "The accumulator type SumAccum is not supported with data type edge!";
    assertNull(compile(query));
    assertError(errMsg, "SEM-9", err);
  }

  /**
   * Test edge attached accumulator
   */
  @Test
  public void testEdgeAccum() {
    Setup("poc_graph");
    {
      // SEM-3001: The vertex-attached accumulator cannot be accessed by edge types
      String[] queryLines = {
        "create query n9 () for graph poc_graph {",
        "  SumAccum<int> @cnt;",
        "  Start = {_};",
        "  L1 = select v",
        "       from Start:s-(:e)->:v",
        "       accum e.@cnt += 1",
        "       ;",
        "  print L1;",
        "}"
      };

      // compile the query - should fail
      String query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      String err = "\nSemantic Check Error in query n9 (SEM-3001): line 6, col 15\n"
                   + "The vertex-attached accumulator \"@cnt\" cannot be accessed by edge types";
      assertEquals(err, errMsg);
    }

    {
      // SEM-3002: The edge-attached accumulator cannot be accessed by vertex types
      String[] queryLines = {
        "create query n10 () for graph poc_graph {",
        "  SumAccum<int> EDGE @cnt;",
        "  Start = {_};",
        "  L1 = select v",
        "       from Start:s-(:e)->:v",
        "       accum s.@cnt += 1",
        "       ;",
        "  print L1;",
        "}"
      };

      // compile the query - should fail
      String query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      String err = "\nSemantic Check Error in query n10 (SEM-3002): line 6, col 15\n"
                   + "The edge-attached accumulator \"@cnt\" cannot be accessed by vertex types";
      assertEquals(err, errMsg);
    }
    {
      // SEM-103: Edge cannot be referred in a POST-ACCUM clause - test in post-accum clause
      String[] queryLines = {
          "create query n11 () for graph poc_graph {",
          "  SumAccum<int> EDGE @cnt;",
          "  Start = {_};",
          "  L1 = select v",
          "       from Start:s-(:e)->:v",
          "       post-accum e.@cnt += 1",
          "       ;",
          "  print L1;",
          "}"
      };

      // compile the query - should fail
      String query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      String err = "\nSemantic Check Error in query n11 (SEM-103): line 6, col 18\n"
          + "The edge variable \"e\" cannot be referred in a POST-ACCUM clause.";
      assertEquals(err, errMsg);
    }
    {
      // SEM-103: Edge cannot be referred in a POST-ACCUM clause - test vAccPrevRval
      String[] queryLines = {
          "create query n12 () for graph poc_graph {",
          "SumAccum<int> @@sum;",
          "  SumAccum<int> EDGE @op;",
          "  Start = {_};",
          "  L1 = select v",
          "       from Start:s-(:e)->:v",
          "       post-accum @@sum += e.@op'",
          "       ;",
          "  print L1;",
          "}"
      };

      // compile the query - should fail
      String query = String.join("\n", queryLines);
      assertNull(compile(query));

      // check error message
      String err = "\nSemantic Check Error in query n12 (SEM-103): line 7, col 27\n"
          + "The edge variable \"e\" cannot be referred in a POST-ACCUM clause.";
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testStaticAccumDeclaration() {
    Setup("empty_graph");
    String query = "", err = "";

    // test 1 - default value provided
    {
      query = ""
          + "create query test14() for graph empty_graph {\n"
          + "  static SumAccum<int> @@intSum = 1;\n"
          + "  print @@intSum;\n"
          + "}";
      err = "\nSemantic Check Error in query test14 (SEM-1001): line 2, col 23"
          + "\nThe static accumulator has a default initial value"
          + "\n--  0 for numeric,"
          + "\n--  empty string for string,"
          + "\n--  1970-01-01 00:00:00 for datetime,"
          + "\n--  true for AndAccum,"
          + "\n--  false for OrAccum,"
          + "\n--  -1 (64-bits of 1 = 0xFFFFFFFFFFFFFFFF) for BitwiseAndAccum,"
          + "\n--  0 for BitwiseOrAccum,"
          + "\n--  empty for ListAccum, SetAccum, BagAccum and ArrayAccum,"
          + "\nwhich cannot be customized. "
          + "\nHowever, you can use '+=' and '=' in your query on the static accumulator. Each"
          + "\nquery invocation will share the same static accumulator, thus its state.";
      assertNull(compile(query));
      assertEquals(err, errMsg);

    }
  }

  @Test
  public void testArrayAccum() {
    Setup("empty_graph");
    String query = "", err = "";

    // test 1 - ArrayAccum<string>
    // TODO: should suggest supported data type for ArrayAccum
    {
      query = ""
          + "create query test11() for graph empty_graph {\n"
          + "  ArrayAccum<string> @@str[5];\n"
          + "  print \"Test\";\n"
          + "}";
      err = "\nSemantic Check Error in query test11 (SEM-9): line 2, col 13"
          + "\nThe accumulator type ArrayAccum is not supported with data type string!";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }

    // test 2 - ArrayAccum<MapAccum>
    {
      query = ""
          + "create query test12() for graph empty_graph {\n"
          + "  ArrayAccum<MapAccum<int, string>> @@map[2];\n"
          + "  print \"Test\";\n"
          + "}";
      err = "The accumulator type ArrayAccum is not supported with "
          + "data type MapAccum<int, string>!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-9", err);
    }

    // test 3 - ArrayAccum<GroupByAccum>
    {
      query = ""
          + "create query test13() for graph empty_graph {\n"
          + "  ArrayAccum<GroupByAccum<int a, string b, MaxAccum<int> maxa>> @@group[5];\n"
          + "  print \"Test\";\n"
          + "}";
      err = "The accumulator type ArrayAccum is not supported with "
          + "data type GroupByAccum<int a, string b, MaxAccum<int> maxa>!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-9", err);
    }

    // test 4 - invalid dimension value
    {
      query = String.join("\n",
          "create query test34() for graph empty_graph {",
          "  ArrayAccum<SumAccum<int>> @@something[0][1];",
          "  print @@something;",
          "}"
      );
      err = "\nDimension of ArrayAccum must be larger than 0.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-818", err);
    }

    // test 5 - not specifying all indices when referring to an element
    {
      query = String.join("\n",
          "create query test32() for graph empty_graph {",
          "  ArrayAccum<SumAccum<int>> @@something[][];",
          "  @@something.reallocate(1, 2);",
          "  print @@something[2];",
          "}"
      );
      err = "When an element of ArrayAccum is reffered, index of all dimensions are required.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-817", err);
    }

    // test 6 - reallocate function dimension count mismatch with accum declaration
    {
      query = String.join("\n",
          "create query test31() for graph empty_graph {",
          "  ArrayAccum<SumAccum<int>> @@something[1][2];",
          "   @@something.reallocate(1);",
          "}"
      );
      err = "\nReallocate function must has same dimension number with arrayAccum does when it"
          + "\nis declared";
      assertNull(compile(query));
      assertError(errMsg, "SEM-816", err);
    }

    // test 7 - not all dimensions are specified
    {
      query = String.join("\n",
          "create query test29() {",
          "  ArrayAccum<SumAccum<int>> @@something[][1];",
          "  print @@something;",
          "}"
      );
      err = "\nWhen declared, an ArrayAccum must have either all dimensions specified or no"
          + "\ndimensions specified.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-815", err);
    }

    // test 8 - declare SetAccum with dimension info
    {
      query = String.join("\n",
          "create query test28() {",
          "  SetAccum<int> @@something[][];",
          "  print @@something;",
          "}"
      );
      err = "\nOnly ArrayAccum can be declared with dimension info! While 'SetAccum<int>' has"
          + "\nbeen used.";
      assertNull(compile(query));
      assertError(errMsg, "SEM-814", err);
    }

    // test 9 - declare global array accum without defining dimensions
    {
      query = "CREATE QUERY test9() FOR GRAPH empty_graph { \n"
          + "  ArrayAccum<SumAccum<INT>> @@arrayAccum;\n"
          + "  PRINT @@arrayAccum;\n"
          + "}";
      err = "\nSemantic Check Error in query test9 (SEM-1703): line 2, col 2 The ArrayAccum"
          + " '@@arrayAccum' requires its dimensions to be specified.\n"
          + "i.e. ArrayAccum<SumAccum<int>> @@arrayAccum[3]";
      assertNull(compile(query));
      assertError(errMsg, "SEM-1703", err);
    }

    // test 10 - declare local array accum without defining dimensions
    {
      query = "CREATE QUERY test10() FOR GRAPH empty_graph { \n"
          + "  ArrayAccum<SumAccum<INT>> @arrayAccum;\n"
          + "  PRINT \"Test Works!\";\n"
          + "}";
      err = "\nSemantic Check Error in query test10 (SEM-1703): line 2, col 2 The ArrayAccum"
          + " '@arrayAccum' requires its dimensions to be specified.\n"
          + "i.e. ArrayAccum<SumAccum<int>> @arrayAccum[3]";
      assertNull(compile(query));
      assertError(errMsg, "SEM-1703", err);
    }
  }

  @Test
  public void testGlobalAccumResetInBlock() {
    Setup("poc_graph");
    String query = "", errCode = "", errDesc = "";

    // test 1 - accum clause
    {
      query = ""
          + "create query n37() for graph poc_graph {\n"
          + "  SumAccum<int> @@cnt;\n"
          + "  Seed = { members.* };\n"
          + "  F = select v from Seed-(:e)->:v accum @@cnt = 3;\n"
          + "}";
      // TODO: error code conflict with sample clause semantic check
      errCode = "SEM-424";
      errDesc = "\nThe assignment operator '=' cannot be used on a global accumulator in a DML"
              + "\nblock!"
              + "\nOnly '+=' is allowed for global accumulators inside a DML block.";
      assertNull(compile(query));
      assertError(errMsg, errCode, errDesc);
    }

    // test 2 - post-accum clause
    {
      query = ""
          + "create query n38() for graph poc_graph {\n"
          + "  SumAccum<int> @@cnt;\n"
          + "  Seed = { members.* };\n"
          + "  F = select v from Seed-(:e)->:v post-accum @@cnt = 3;\n"
          + "}";
      // TODO: error code conflict with sample clause semantic check
      errCode = "SEM-424";
      errDesc = "\nThe assignment operator '=' cannot be used on a global accumulator in a DML"
              + "\nblock!"
              + "\nOnly '+=' is allowed for global accumulators inside a DML block.";
      assertNull(compile(query));
      assertError(errMsg, errCode, errDesc);
    }
  }

  @Test
  public void testPreAccum() {
    Setup("poc_graph");
    String query = "", err = "";

    // test 1 - read v.@acc' in ACCUM
    {
      query = ""
          + "create query n12() for graph poc_graph {\n"
          + "  SumAccum<int> @op;\n"
          + "  Seed = { company.* };\n"
          + "  L0 = select src from Seed:src accum src.@op += src.@op';\n"
          + "}";
      err = "\nThe pre-accum value of an accumulator can only be used in a POST-ACCUM clause!";
      assertNull(compile(query));
      assertError(errMsg, "SEM-50", err);
    }
  }

  // SumAccum declaration
  // negative test cases
  // Tester: Jing Qin
  @Test
  public void testSumAccum1() {
    Setup("poc_graph_2");
    String query = "", err = "", errCode = "";

    // test 1_1
    {
      query = ""
          + "create query sumac_neg_test1_1() FOR GRAPH poc_graph{\n"
          + "  TYPEDEF TUPLE<STRING str, INT score> testtuple;\n"
          + "  SumAccum<testtuple> @@tupleSum;\n"
          + "  print @@tupleSum;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type testtuple<string,"
          + "\nint>!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_3
    {
      query = ""
          + "create query sumac_neg_test1_3() FOR GRAPH poc_graph{\n"
          + "  SumAccum<VERTEX> @@vertexSum;\n"
          + "  print @@vertexSum;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type vertex!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_4
    {
      query = ""
          + "create query sumac_neg_test1_4() FOR GRAPH poc_graph{\n"
          + "  SumAccum<EDGE> @@edgeSum;\n"
          + "  print @@edgeSum;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type edge!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_5
    {
      query = ""
          + "create query sumac_neg_test1_5() FOR GRAPH poc_graph{\n"
          + "  SumAccum<MaxAccum<INT>> @@maxSum;\n"
          + "  print @@maxSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMaxAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_6
    {
      query = ""
          + "create query sumac_neg_test1_6() FOR GRAPH poc_graph{\n"
          + "  SumAccum<MinAccum<INT>> @@minSum;\n"
          + "  print @@minSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_7
    {
      query = ""
          + "create query sumac_neg_test1_7() FOR GRAPH poc_graph{\n"
          + "  SumAccum<AvgAccum> @@avgSum;\n"
          + "  print @@avgSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nAvgAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_8
    {
      query = ""
          + "create query sumac_neg_test1_8() FOR GRAPH poc_graph{\n"
          + "  SumAccum<BitwiseAndAccum> @@bwaSum;\n"
          + "  print @@bwaSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseAndAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_9
    {
      query = ""
          + "create query sumac_neg_test1_9() FOR GRAPH poc_graph{\n"
          + "  SumAccum<BitwiseOrAccum> @@bwoSum;\n"
          + "  print @@bwoSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseOrAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_10
    {
      query = ""
          + "create query sumac_neg_test1_10() FOR GRAPH poc_graph{\n"
          + "  SumAccum<ListAccum<INT>> @@listSum;\n"
          + "  print @@listSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nListAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_11
    {
      query = ""
          + "create query sumac_neg_test1_11() FOR GRAPH poc_graph{\n"
          + "  SumAccum<SetAccum<VERTEX>> @@setSum;\n"
          + "  print @@setSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSetAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_12
    {
      query = ""
          + "create query sumac_neg_test1_12() FOR GRAPH poc_graph{\n"
          + "  SumAccum<BagAccum<STRING>> @@bagSum;\n"
          + "  print @@bagSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBagAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_13
    {
      query = ""
          + "create query sumac_neg_test1_13() FOR GRAPH poc_graph{\n"
          + "  SumAccum<MapAccum<INT, INT>> @@mapSum;\n"
          + "  print @@mapSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMapAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_14
    {
      query = ""
          + "create query sumac_neg_test1_14() FOR GRAPH poc_graph{\n"
          + "  TYPEDEF TUPLE<STRING str, INT score> testtuple;\n"
          + "  SumAccum<HeapAccum<testtuple>(2, score ASC)> @@heapSum;\n"
          + "  print @@heapSum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nHeapAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }

    // test 1_15
    {
      query = ""
          + "create query sumac_neg_test1_15() FOR GRAPH poc_graph{\n"
          + "  SumAccum<ArrayAccum<SetAccum<INT>>> @@arraySum;\n"
          + "  print @@arraySum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nArrayAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    // test 1_16
    {
      query = ""
          + "create query sumac_neg_test1_16() FOR GRAPH poc_graph{\n"
          + "  SumAccum<GroupByAccum<INT a, MaxAccum<INT> mymax>> @@groupbySum;\n"
          + "  print @@groupbySum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nGroupByAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }

  // SumAccum declaration
  // negative test cases
  // Tester: Jing Qin
  @Test
  public void testSumAccum3() {
    Setup("poc_graph_2");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query sumac_neg_test3_1(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  //test = operator in select block, should return semantic check error\n"
          + "  SumAccum<UINT> @@uintSum;\n"
          + "  SumAccum<INT> @@intSum;  \n"
          + "  SumAccum<FLOAT> @@floatSum;\n"
          + "  SumAccum<DOUBLE> @@doubleSum;\n"
          + "  SumAccum<STRING> @@stringSum;\n"
          + "  // test = operator in select block\n"
          + "  L0 = {seed};\n"
          + "  L1 = select tgt\n"
          + " from L0: s - (member_work_company: e) - company: tgt\n"
          + " accum @@uintSum = 2, @@intSum = 6, @@floatSum = 1.5,\n"
          + "      @@doubleSum = 1.55, @@stringSum = \"test\";\n"
          + "}";
      errCode = "SEM-424";
      err = ""
          + "\nThe assignment operator '=' cannot be used on a global accumulator in a DML"
          + "\nblock!"
          + "\nOnly '+=' is allowed for global accumulators inside a DML block.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test3_2(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<UINT> @@uintSum1;\n"
          + "  SumAccum<INT> @@intSum1; \n"
          + "  SumAccum<FLOAT> @@floatSum1;\n"
          + "  SumAccum<DOUBLE> @@doubleSum1;\n"
          + "  SumAccum<STRING> @@stringSum1;\n"
          + "  SumAccum<STRING> @@stringSum2;\n"
          + "  SumAccum<STRING> @@stringSum3;\n"
          + "  SumAccum<STRING> @@stringSum4;\n"
          + " // test = operator with wrong type in select block\n"
          + " L0 = {seed};\n"
          + " L1 = select tgt\n"
          + "    from L0: s - (member_work_company: e) - company: tgt\n"
          + "    accum @@uintSum1 = s.id, @@intSum1 = e.industryId, \n"
          + "        @@intSum2 = s.id, @@floatSum = s.id,\n"
          + "          @@floatSum1 = tgt.nCount, @@doubleSum1 = s.id,\n"
          + "          @@stringSum1 = 0, @@stringSum2 = -2, \n"
          + "          @@stringSum3 = e.industryId, @@stringSum4 = e.positionId;\n"
          + "}";
      errCode = "SEM-424";
      err = ""
          + "\nThe assignment operator '=' cannot be used on a global accumulator in a DML"
          + "\nblock!"
          + "\nOnly '+=' is allowed for global accumulators inside a DML block.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }
  // SumAccum declaration
  // negative test cases
  // Tester: Jing Qin
  @Test
  public void testSumAccum4() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";

    {
      query = ""
          + "create query sumac_neg_test4_1(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  TYPEDEF TUPLE<STRING str, INT score> testtuple;\n"
          + "  SumAccum<testtuple> @tupleSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type testtuple<string,"
          + "\nint>!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_3(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<VERTEX> @vertexSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type vertex!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_4(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<EDGE> @edgeSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type edge!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_5(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<MaxAccum<INT>> @maxSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMaxAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_6(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<MinAccum<INT>> @minSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_7(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<AvgAccum> @avgSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nAvgAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_8(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<BitwiseAndAccum> @bwaSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseAndAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_9(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<BitwiseOrAccum> @bwoSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseOrAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_10(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<ListAccum<INT>> @listSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nListAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_11(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<SetAccum<VERTEX>> @setSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSetAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_12(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<BagAccum<STRING>> @bagSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBagAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_13(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<MapAccum<INT, INT>> @mapSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMapAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_14(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  TYPEDEF TUPLE<STRING str, INT score> testtuple;\n"
          + "  SumAccum<HeapAccum<testtuple>(2, score ASC)> @heapSum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nHeapAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_15(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<ArrayAccum<SetAccum<INT>>> @arraySum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nArrayAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query sumac_neg_test4_16(VERTEX<members> seed) FOR GRAPH poc_graph{\n"
          + "  SumAccum<GroupByAccum<INT a, MaxAccum<INT> mymax>> @groupbySum;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nGroupByAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }

  // SumAccum declaration
  // negative test cases
  // Tester: Jing Qin
  @Test
  public void testSumAccum6() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query sumac_neg_test6(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  SumAccum<VERTEX> @@sumac;\n"
          + "  @@sumac = seed;\n"
          + "  L0 = {@@sumac};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type SumAccum is not supported with data type vertex!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }

  // MinAccum & MaxAccum declaration
  // negative test cases
  // Tester: Yong Tan
  @Test
  public void testMinMaxAccum2() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query minmaxac_neg_test2() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  SumAccum<MinAccum<int>> @@sumMinAccum;\n"
          + "  PRINT @@sumMinAccum;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within a SumAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }
  }
  // MinAccum & MaxAccum declaration
  // negative test cases
  // Tester: Yong Tan
  @Test
  public void testMinMaxAccum3() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query minmaxac_neg_test2() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  MinAccum<MinAccum<int>> @@minMinAccum;\n"
          + "  # The compiler will quit when checking the above line,\n"
          + "  # so the following lines will NOT be checked.\n"
          + "  # It will be better to create a query for each of them.\n"
          + "  MaxAccum<MinAccum<int>> @@maxMinAccum;\n"
          + "  OrAccum<MinAccum<int>> @@orMinAccum;\n"
          + "  AndAccum<MinAccum<int>> @@andMinAccum;\n"
          + "  AvgAccum<MinAccum<int>> @@avgMinAccum;\n"
          + "  ListAccum<MinAccum<int>> @@listMinAccum;\n"
          + "  HeapAccum<MinAccum<int>> @@heapMinAccum;\n"
          + "  MapAccum<MinAccum<int>> @@mapMinAccum;\n"
          + "  GroupByAccum<MinAccum<int>> @@groupByMinAccum;\n"
          + "  BagAccum<MinAccum<int>> @@bagMinAccum;\n"
          + "  SetAccum<MinAccum<int>> @@setMinAccum;\n"
          + "  BitwiseAndAccum<MinAccum<int>> @@bwAndMinAccum;\n"
          + "  BitwiseOrAccum<MinAccum<int>> @@bwOrMinAccum;\n"
          + "  MaxAccum<string> @@strMax;\n"
          + "  MaxAccum<string compress> @@cstrMax;\n"
          + "  SumAccum<MaxAccum<int>> @@sumMaxAccum;\n"
          + "  MinAccum<MaxAccum<int>> @@minMaxAccum;\n"
          + "  MaxAccum<MaxAccum<int>> @@maxMaxAccum;\n"
          + "  OrAccum<MaxAccum<int>> @@orMaxAccum;\n"
          + "  AndAccum<MaxAccum<int>> @@andMaxAccum;\n"
          + "  AvgAccum<MaxAccum<int>> @@avgMaxAccum;\n"
          + "  ListAccum<MaxAccum<int>> @@listMaxAccum;\n"
          + "  HeapAccum<MaxAccum<int>> @@heapMaxAccum;\n"
          + "  MapAccum<MaxAccum<int>> @@mapMaxAccum;\n"
          + "  GroupByAccum<MaxAccum<int>> @@groupByMaxAccum;\n"
          + "  BagAccum<MaxAccum<int>> @@bagMaxAccum;\n"
          + "  SetAccum<MaxAccum<int>> @@setMaxAccum;\n"
          + "  BitwiseAndAccum<MaxAccum<int>> @@bwAndMaxAccum;\n"
          + "  BitwiseOrAccum<MaxAccum<int>> @@bwOrMaxAccum;\n"
          + "  PRINT @@minMinAccum;\n"
          + "}";

      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within a MinAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }
  }
  // MinAccum & MaxAccum declaration
  // negative test cases
  // Tester: Yong Tan
  @Test
  public void testMinMaxAccum4() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query minmaxac_neg_test2(VERTEX<company> seed) FOR"
          + " GRAPH poc_graph api(\"v2\") {\n"
          + "  MinAccum<VERTEX> @@vertexMin;\n"
          + "  @@vertexMin = seed;\n"
          + "  L0 = {@@vertexMin};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-153";
      err = ""
          + "\nSeed must be a single string, a container of strings, or a container of vertex";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }
  // MinAccum & MaxAccum declaration
  // negative test cases
  // Tester: Yong Tan
  @Test
  public void testMinMaxAccum5() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query minmaxac_neg_test2(VERTEX<company> seed) FOR GRAPH "
          + "poc_graph api(\"v2\") {\n"
          + "  MaxAccum<VERTEX> @@vertexMax;\n"
          + "  @@vertexMax = seed;\n"
          + "  L1 = {@@vertexMax};\n"
          + "  print L1;\n"
          + "}";

      errCode = "SEM-153";
      err = ""
          + "\nSeed must be a single string, a container of strings, or a container of vertex";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }
  }

  @Test
  public void testAvgAccum2() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query avgac_neg_test2_1(VERTEX<members> seed) FOR GRAPH poc_graph {\n"
          + "  // test = operator for global variable in select block \n"
          + "  L0 = {seed};\n"
          + "  AvgAccum @@avg;\n"
          + "  L1 = select tgt\n"
          + "       from L0: s - (member_work_company: e) - company: tgt\n"
          + "       accum @@avg = 3;\n"
          + "  print @@avg;\n"
          + "}";
      errCode = "SEM-424";
      err = ""
          + "\nThe assignment operator '=' cannot be used on a global accumulator in a DML"
          + "\nblock!"
          + "\nOnly '+=' is allowed for global accumulators inside a DML block.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query avgac_neg_test2_2() FOR GRAPH poc_graph {\n"
          + "  // data type of AvgAccum should not be declared\n"
          + "  AvgAccum<INT> @@avg;\n"
          + "  print @@avg;\n"
          + "}";
      errCode = "SEM-8";
      err = ""
          + "\nThe accumulator type AvgAccum cannot be given an element type.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query avgac_neg_test2_3(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  // data type of AvgAccum should not be declared\n"
          + "  AvgAccum<DOUBLE> @avg;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-8";
      err = ""
          + "\nThe accumulator type AvgAccum cannot be given an element type.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }
  // AndAccum & OrAccum declaration
  // negative test cases
  // Tester: Yong Tan
  @Test
  public void testAndOrAccum() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query AndOrAccum_neg_test2() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  # Test unsupported types\n"
          + "  AndAccum<float> @@andAccumVar;\n"
          + "  PRINT @@andAccumVar;\n"
          + "}";
      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type AndAccum is not supported with data type float!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }

    {
      query = ""
          + "create query AndOrAccum_neg_test3() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  # Test unsupported types\n"
          + "  OrAccum<string> @@orAccumVar;\n"
          + "  PRINT @@orAccumVar;\n"
          + "}";

      errCode = "SEM-9";
      err = ""
          + "\nThe accumulator type OrAccum is not supported with data type string!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }
  }

  @Test
  public void testBitwiseAndOrAccum() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query BitwiseAndOrAccum_neg_test2() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  # Test unsupported types\n"
          + "  BitwiseAndAccum<float> @@bwAndAccumVar;\n"
          + "  PRINT @@bwAndAccumVar;\n"
          + "}";
      errCode = "SEM-8";
      err = ""
          + "\nThe accumulator type BitwiseAndAccum cannot be given an element type.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);

    }

    {
      query = ""
          + "create query BitwiseAndOrAccum_neg_test3() FOR GRAPH poc_graph api(\"v2\") {\n"
          + "  # Test unsupported types\n"
          + "  BitwiseOrAccum<string> @@bwOrAccumVar;\n"
          + "  PRINT @@bwOrAccumVar;\n"
          + "}";

      errCode = "SEM-8";
      err = ""
          + "\nThe accumulator type BitwiseOrAccum cannot be given an element type.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }

  @Test
  public void testListAccum2() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query listac_neg_test2_1() FOR GRAPH poc_graph {\n"
          + "  ListAccum<SumAccum<INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSumAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_2() FOR GRAPH poc_graph {\n"
          + "  ListAccum<MaxAccum<INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMaxAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_3() FOR GRAPH poc_graph {\n"
          + "  ListAccum<MinAccum<INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_4() FOR GRAPH poc_graph {\n"
          + "  ListAccum<AvgAccum> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nAvgAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_5() FOR GRAPH poc_graph {\n"
          + "  ListAccum<OrAccum> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-45";
      err = ""
          + "\nThe tuple name OrAccum is not defined.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_6() FOR GRAPH poc_graph {\n"
          + "  ListAccum<AndAccum> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-45";
      err = ""
          + "\nThe tuple name AndAccum is not defined.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_7() FOR GRAPH poc_graph {\n"
          + "  ListAccum<BitwiseOrAccum> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseOrAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_8() FOR GRAPH poc_graph {\n"
          + "  ListAccum<BitwiseAndAccum> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseAndAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_9() FOR GRAPH poc_graph {\n"
          + "  ListAccum<SetAccum<INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSetAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_10() FOR GRAPH poc_graph {\n"
          + "  ListAccum<BagAccum<INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBagAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_11() FOR GRAPH poc_graph {\n"
          + "  ListAccum<MapAccum<STRING, INT>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMapAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_12() FOR GRAPH poc_graph {\n"
          + "  ListAccum<ArrayAccum<SumAccum<INT>>> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nArrayAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_13() FOR GRAPH poc_graph {\n"
          + "  TYPEDEF TUPLE<STRING name, INT score> mytuple;\n"
          + "  ListAccum<HeapAccum<mytuple>(2, score ASC)> @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nHeapAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test2_14() FOR GRAPH poc_graph {\n"
          + "  ListAccum<GroupByAccum<INT a, STRING b, MaxAccum<INT> maxa, SumAccum<INT> suma>>"
          + " @@list;\n"
          + "  print @@list;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nGroupByAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }
  @Test
  public void testListAccum3() {
    Setup("poc_graph");
    String query = "", err = "", errCode = "";
    {
      query = ""
          + "create query listac_neg_test3_1(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "        ListAccum<SumAccum<INT>> @list;\n"
          + "        L0 = {seed};\n"
          + "        print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSumAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_2(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<MaxAccum<INT>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMaxAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_3(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<MinAccum<INT>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMinAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_4(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<AvgAccum> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nAvgAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_5(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<BitwiseOrAccum> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseOrAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_6(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<BitwiseAndAccum> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBitwiseAndAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_7(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<SetAccum<INT>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nSetAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_8(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<BagAccum<INT>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nBagAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_9(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<MapAccum<STRING, INT>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nMapAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_10(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<ArrayAccum<SumAccum<INT>>> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nArrayAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_11(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<AndAccum> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-45";
      err = ""
          + "\nThe tuple name AndAccum is not defined.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_12(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<OrAccum> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-45";
      err = ""
          + "\nThe tuple name OrAccum is not defined.";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_13(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  TYPEDEF TUPLE<STRING name, INT score> mytuple;\n"
          + "  ListAccum<HeapAccum<mytuple>(2, score ASC)> @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nHeapAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
    {
      query = ""
          + "create query listac_neg_test3_14(VERTEX<company> seed) FOR GRAPH poc_graph {\n"
          + "  ListAccum<GroupByAccum<INT a, STRING b, MaxAccum<INT> maxa, SumAccum<INT> suma>>"
          + " @list;\n"
          + "  L0 = {seed};\n"
          + "  print L0;\n"
          + "}";
      errCode = "SEM-1301";
      err = ""
          + "\nGroupByAccum cannot be nested within ListAccum accumulator!";
      assertNull(compile(query));
      assertError(errMsg, errCode, err);
    }
  }

  @Test
  public void testDeviationAccumDeclarationWithEleTp_Global() {
    Setup("empty_graph");
    String query = "", err = "";

    // Same as AvgAccum, DeviationAccum doesn't take element type
    {
      query = ""
              + "create query q1() for graph empty_graph {\n"
              + "  DeviationAccum<bool> @@acc;\n"
              + "  print @@acc;\n"
              + "}";
      err = "\nSemantic Check Error in query q1 (SEM-8): line 2, col 16"
              + "\nThe accumulator type DeviationAccum cannot be given an element type.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }

  @Test
  public void testDeviationAccumDeclarationWithEleTp_Local() {
    Setup("empty_graph");
    String query = "", err = "";

    // Same as AvgAccum, DeviationAccum doesn't take element type
    {
      query = ""
              + "create query q1() for graph empty_graph {\n"
              + "  DeviationAccum<bool> @acc;\n"
              + "}";
      err = "\nSemantic Check Error in query q1 (SEM-8): line 2, col 16"
              + "\nThe accumulator type DeviationAccum cannot be given an element type.";
      assertNull(compile(query));
      assertEquals(err, errMsg);
    }
  }
}
