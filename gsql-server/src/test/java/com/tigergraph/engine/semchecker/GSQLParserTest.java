package com.tigergraph.engine.semchecker;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertNotNull;

import com.tigergraph.engine.TestCase;

import java.util.ArrayList;
import java.util.List;
import static org.hamcrest.CoreMatchers.startsWith;
import org.junit.Test;

public class GSQLParserTest extends TestCase {

  @Test
  public void testHeapAccum() {
    Setup("poc_graph");
    {
      String query = ""
          + "create query n7() for graph poc_graph   {\n"
          + "  TYPEDEF tuple<string s, int i> Pair;\n"
          + "  int k = 1;\n"
          + "  HeapAccum<Pair>(GSQL_INT_MAX, s ASC, i DESC) @@heap;\n"
          + "  PRINT @@heap;\n"
          + "}";
      String err = "line 4:34 no viable alternative at input"
          + " 'HeapAccum<Pair>(GSQL_INT_MAX, s asc'\n"
          + "line 4:34 no viable alternative at input '(GSQL_INT_MAX, s asc'\n"
          + "Parsing encountered 2 syntax error(s)\n"
          + "\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
  }

  @Test
  public void testWhileClause() {
    Setup("poc_graph");
    {
      String query = ""
          + "create query n8() for graph poc_graph   {\n"
          + "  while false limit GSQL_INT_MAX do\n"
          + "    PRINT \"OK\";\n"
          + "  end;"
          + "}";
      String err = "line 2:20 mismatched input 'GSQL_INT_MAX' expecting "
          + "{ABORT, BY, COMMIT, DISTINCT, FILE, FUNCTION, GROUP, INSERT, LASTHOP, LIST, LOG, MAP, "
          + "MATCH, NOW, PATH, PER, REPLACE, SRC, TGT, TO_DATETIME, UPDATE, CONST_INT, NAME}\n"
          + "Parsing encountered 1 syntax error(s)\n\n";
      assertNull(compile(query));
      assertEquals(err, outMsg);
    }
  }

  @Test
  public void testParen1() {
    Setup("poc_graph");
    {
      String query = "" + "create query parentest() for graph poc_graph {\n"
          + "Start = {members.*};\n" + "R = SELECT src\n"
          + "FROM Start:src -(((member_work_company|member_member)):e)-> :tgt;\n" + "}";
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testParen2() {
    Setup("poc_graph");
    {
      String query = "" + "create query parenvTest() for graph poc_graph {\n"
          + "Start = {members.*};\n"
          + " R = SELECT src FROM ((Start)):src -((member_work_company|member_member)"
          + ":e)-> :tgt;}\n";
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testParen3() {
    Setup("poc_graph");
    {
      String query = "" + "create query parenvTest() for graph poc_graph {\n"
          + " R = SELECT src FROM (((members|company))):src -(all_to_skill:e)-> :tgt;}\n";
      assertNotNull(compile(query));
    }
  }

  @Test
  public void testCreateAndShow() {
    List<String> allowedIdentifier = List.of("ABORT", "ACL", "PASSWORD", "USER", "VECTOR" );
    for (String vtxName : allowedIdentifier) {
      String create_cmd = String.join(" ", "create", "vertex", vtxName + "(PRIMARY_ID id UINT)");
      assertThat(command_.runCmd(create_cmd),startsWith(
          "Successfully created vertex"));
      String show_cmd = String.join(" ", "show", "vertex", vtxName);
      assertThat(command_.runCmd(show_cmd),startsWith(
          "- VERTEX " + vtxName));
      show_cmd = String.join(" ", "show", "vertex", vtxName.substring(0, 2) + "*");
      assertThat(command_.runCmd(show_cmd),startsWith(
          "- VERTEX " + vtxName));
    }
  }

}
