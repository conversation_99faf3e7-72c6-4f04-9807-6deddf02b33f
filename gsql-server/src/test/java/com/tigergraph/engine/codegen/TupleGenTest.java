package com.tigergraph.engine.codegen;

import org.junit.Test;
import static org.junit.Assert.*;

import com.tigergraph.engine.PositiveTestCase;

public class TupleGenTest extends PositiveTestCase {

  @Test
  public void testTupleComparatorGenForHeapDist() {
    assertNotNull(command_.runCmd("TYPEDEF TUPLE <i UINT(8), i2 INT(8), s STRING(10)> testTup"));
    // setup graph
    Setup("empty_graph");

    // compile query
    String query = String.join("\n",
        "CREATE distributed QUERY test() for graph empty_graph {",
        "  HeapAccum<testTup>(5, i DESC, i2 ASC, s DESC) @@TestHeap;",
        "  Print 1;",
        "}");
    flags_.add(compileFlags.DIST_MODE);
    runCodeGenTest(query);
  }

  @Test
  public void testTupleComparatorGenForHeapUDF() {
    assertNotNull(command_.runCmd("TYPEDEF TUPLE <i UINT(8), i2 INT(8), s STRING(10)> testTup"));
    // setup graph
    Setup("empty_graph");

    // compile query
    String query = String.join("\n",
        "CREATE QUERY test() for graph empty_graph {",
        "  HeapAccum<testTup>(5, i DESC, i2 ASC, s DESC) @@TestHeap;",
        "  Print 1;",
        "}");
    flags_.add(compileFlags.UDF_MODE);
    runCodeGenTest(query);
  }

  @Test
  public void testOtherAsTupleFieldNameUDF() {
    // setup graph
    Setup("empty_graph");

    // compile query
    String query = String.join("\n",
        "CREATE QUERY test() for graph empty_graph {",
        "  TYPEDEF TUPLE<INT something, INT other> DATA;",
        "  Print 1;",
        "}");
    flags_.add(compileFlags.UDF_MODE);
    runCodeGenTest(query);
  }

  @Test
  public void testOtherAsTupleFieldNameDist() {
    // setup graph
    Setup("empty_graph");

    // compile query
    String query = String.join("\n",
        "CREATE QUERY test() for graph empty_graph {",
        "  TYPEDEF TUPLE<INT something, INT other> DATA;",
        "  Print 1;",
        "}");
    flags_.add(compileFlags.DIST_MODE);
    runCodeGenTest(query);
  }
}
