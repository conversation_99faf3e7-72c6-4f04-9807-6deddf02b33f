package com.tigergraph.engine.preproc;

import com.tigergraph.engine.PositiveTestCase;
import com.tigergraph.engine.codegen.compileFlags;

import org.junit.Before;
import org.junit.Test;

public class LdbcV2ConjunctivePatternTest extends PositiveTestCase {
  @Before
  public void setup() {
    Setup("ldbc_snb_v2");
    flags_.add(compileFlags.CONJUNCTIVE_TO_LINEAR_TEST);
  }

  @Test
  public void testGLE3287RewriteNonLocalCond() {
    String query = String.join("\n",
        "CREATE QUERY testGLE3287() FOR GRAPH ldbc_snb {",
        "  vSet =",
        "    SELECT m2",
        "    FROM Post:m2 -(HAS_CREATOR>)- Person:p2,",
        "         Post:m2 -(HAS_TAG>)- Tag:t,",
        "         Person:p2 -(IS_LOCATED_IN>)- City:c2,",
        "         Post:m1 -(HAS_TAG>)- Tag:t,",
        "         Post:m1 -(HAS_CREATOR>)- Person:p1,",
        "         Person:p1 -(IS_LOCATED_IN>)- City:c1",
        "    WHERE (c1 != c2) and (m1 != m2)",
        "          and abs(datetime_diff(m1.creationDate, m2.creationDate)) < 60;",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testGLE2240() {
    String query = String.join("\n",
        "CREATE QUERY testGLE2240(VERTEX<Person> p1, STRING tag) FOR GRAPH ldbc_snb SYNTAX V2 {",
        "  OrAccum @known;",
        "  person1 = { p1 };",
        "  tmp = SELECT t FROM person1:s -(KNOWS)- Person:t ACCUM t.@known += true;",
        "  tagWithName = SELECT t FROM Tag:t WHERE t.name == tag;",
        "  person2s = SELECT p FROM tagWithName -(<HAS_INTEREST)- Person:p",
        "    WHERE p != p1 AND NOT p.@known;",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testPushNegation() {
    String query = String.join("\n",
        "CREATE QUERY testPushNegation(VERTEX<Person> p1, STRING tag) FOR GRAPH "
        + "ldbc_snb SYNTAX V2 {",
        "  OrAccum @known;",
        "  person1 = { p1 };",
        "  person2s =",
        "    SELECT p",
        "    FROM person1:s -(KNOWS)- Person:p -(HAS_INTEREST>)- Tag:t",
        "    WHERE NOT (p.birthday > to_datetime(\"1996-01-01\") OR t.name != tag);",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testGLE1570() {
    String query = String.join("\n",
        "CREATE QUERY testGLE1570() FOR GRAPH ldbc_snb SYNTAX V2 {",
        "  vSet = SELECT p",
        "    FROM (Post|Comment) -(<LIKES)- Person:p -(STUDY_AT>)- University:u,",
        "         :p -(IS_LOCATED_IN>)- City:c;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testTest_03Bug3() {
    String query = String.join("\n",
        "CREATE OR REPLACE QUERY bug3() FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  SumAccum<int> @cnt;",
        "  //add 1 to the t1 node @cnt",
        "  Result =  SELECT t1",
        "  FROM TagClass:s - (IS_SUBCLASS_OF>) - :t1",
        "  WHERE s.name == \"TennisPlayer\"",
        "  ACCUM t1.@cnt +=1;",
        "  //this one has a false predicate  t3.@cnt > t1.@cnt",
        "  Result2 =  SELECT t3",
        "  FROM TagClass:s - (IS_SUBCLASS_OF>) - :t1 - (IS_SUBCLASS_OF>) - :t2",
        "       - (IS_SUBCLASS_OF>) - TagClass:t3",
        "  WHERE s.name == \"TennisPlayer\" and t3.@cnt > t1.@cnt;",
        "  PRINT  Result2;",
        "  PRINT Result;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testTest_GLE1280Test_GLE1280() {
    String query = String.join("\n",
        "CREATE QUERY test_GLE1280(string com) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "",
        "  SetAccum<edge> @@FinalEdgeSet_1;",
        "  SetAccum<edge> @@FinalEdgeSet_2;",
        "  SetAccum<edge> @@FinalEdgeSet_3;",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_1;",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_2;",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_3;",
        "",
        "  VertexSet_1 =",
        "        SELECT p1",
        "        FROM City:c1 -(IS_PART_OF>:pt1)- Country:c,",
        "             City:c2 -(IS_PART_OF>:pt2)- Country:c,",
        "             City:c3 -(IS_PART_OF>:pt3)- Country:c,",
        "             Person:p1 -(IS_LOCATED_IN>:l1)- City:c1,",
        "             Person:p2 -(IS_LOCATED_IN>:l2)- City:c2,",
        "             Person:p3 -(IS_LOCATED_IN>:l3)- City:c3,",
        "             Person:p2 -(KNOWS:k1)- Person:p1,",
        "             Person:p1 -(KNOWS:k2)- Person:p3,",
        "             Person:p2 -(KNOWS:k3)- Person:p3",
        "        WHERE (c.name == com)",
        "        ACCUM @@FinalEdgeSet_1 += k1,",
        "              @@FinalEdgeSet_2 += k2,",
        "              @@FinalEdgeSet_3 += k3",
        "        POST-ACCUM @@FinalVertexSet_1 += p1",
        "        POST-ACCUM @@FinalVertexSet_2 += p2",
        "        POST-ACCUM @@FinalVertexSet_3 += p3",
        "        ;",
        "",
        "  PRINT @@FinalEdgeSet_1;",
        "",
        "  PRINT @@FinalEdgeSet_2;",
        "",
        "  PRINT @@FinalEdgeSet_3;",
        "",
        "  VertexSet_1 = { @@FinalVertexSet_1 };",
        "  PRINT VertexSet_1[",
        "    VertexSet_1.firstName AS firstName,",
        "    VertexSet_1.lastName AS lastName,",
        "    VertexSet_1.gender AS gender,",
        "    VertexSet_1.birthday AS birthday,",
        "    VertexSet_1.creationDate AS creationDate,",
        "    VertexSet_1.locationIP AS locationIP,",
        "    VertexSet_1.browserUsed AS browserUsed,",
        "    VertexSet_1.speaks AS speaks,",
        "    VertexSet_1.email AS email",
        "  ];",
        "",
        "  VertexSet_2 = { @@FinalVertexSet_2 };",
        "  PRINT VertexSet_2[",
        "    VertexSet_2.firstName AS firstName,",
        "    VertexSet_2.lastName AS lastName,",
        "    VertexSet_2.gender AS gender,",
        "    VertexSet_2.birthday AS birthday,",
        "    VertexSet_2.creationDate AS creationDate,",
        "    VertexSet_2.locationIP AS locationIP,",
        "    VertexSet_2.browserUsed AS browserUsed,",
        "    VertexSet_2.speaks AS speaks,",
        "    VertexSet_2.email AS email",
        "  ];",
        "",
        "  VertexSet_3 = { @@FinalVertexSet_3 };",
        "  PRINT VertexSet_3[",
        "    VertexSet_3.firstName AS firstName,",
        "    VertexSet_3.lastName AS lastName,",
        "    VertexSet_3.gender AS gender,",
        "    VertexSet_3.birthday AS birthday,",
        "    VertexSet_3.creationDate AS creationDate,",
        "    VertexSet_3.locationIP AS locationIP,",
        "    VertexSet_3.browserUsed AS browserUsed,",
        "    VertexSet_3.speaks AS speaks,",
        "    VertexSet_3.email AS email",
        "  ];",
        "",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testLinearGroupSet() {
    String query = String.join("\n",
        "CREATE QUERY linear_group_set() FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  GroupByAccum<vertex continent, SetAccum<int> len, SetAccum<string> fn,",
        "               SetAccum<string> ln> @@groupby_continent;",

        "  GroupByAccum<vertex continent, vertex country, SetAccum<int> len,",
        "               SetAccum<string> fn, SetAccum<string> ln>",
        "               @@groupby_continent_country;",

        "  GroupByAccum<vertex continent, vertex country, vertex city,",
        "               SetAccum<int> len, SetAccum<string> fn, SetAccum<string> ln>",
        "               @@groupby_continent_country_city;",

        "  T =",
        "    SELECT city",
        "    FROM Post:p -(<LIKES)- Person:ps -(IS_LOCATED_IN>)- City:city",
        "          -(IS_PART_OF>)- Country:country -(IS_PART_OF>)- Continent:continent",
        "    ACCUM",
        "      @@groupby_continent += (continent -> p.length, ps.firstName, ps.lastName),",
        "      @@groupby_continent_country += (continent, country -> p.length,",
        "          ps.firstName, ps.lastName),",
        "      @@groupby_continent_country_city += (continent, country, city -> p.length,",
        "          ps.firstName, ps.lastName);",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testGLE1282() {
    String query = String.join("\n",
        "CREATE QUERY test_GLE1282(set<vertex<Post>> p) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  ListAccum<int> @acc;",
        "  VSet1 = { p };",
        "  VSet1 =",
        "    SELECT p2",
        "    FROM Comment:c2 -(REPLY_OF>:r2)- VSet1:p2,",
        "         Comment:c2 -(HAS_CREATOR>:h2)- Person:p4,",
        "         VSet1:p2 -(HAS_CREATOR>:h1)- Person:p3,",
        "         Person:p3 -(KNOWS:k1)- Person:p4",
        "    ACCUM p2.@acc += 1;",
        "  PRINT VSet1;",

        "  VSet2 = { p };",
        "  VSet2 =",
        "    SELECT p1",
        "    FROM Comment:c1 -(REPLY_OF>:r1)- VSet2:p1;",
        "  PRINT VSet2;",
        "}"
    );
    runTransformTest(query);
  }

  /**
   * Expected behavior: proper query transformation
   * Encountered behavior: type inference error
   */
  @Test
  public void testGLE1411FalseTypeError() {
    String query = String.join("\n",
        "CREATE QUERY test_GLE1411_1(string name) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  TYPEDEF TUPLE <vertex<Person> a, vertex<Person> b, vertex<Person> c> ResTuple;",
        "  SetAccum<ResTuple> @@result;",
        "  SumAccum<int> @@tupleCount;",
        "",
        "  vSet =",
        "    SELECT c",
        "    FROM Country:c -(<IS_PART_OF.<IS_LOCATED_IN)- Person:p1,",
        "         :c -(<IS_PART_OF.<IS_LOCATED_IN)- Person:p2,",
        "         :c -(<IS_PART_OF.<IS_LOCATED_IN)- Person:p3,",
        "         :p1 -(KNOWS)- :p2 -(KNOWS)- :p3 -(KNOWS)- :p1",
        "    WHERE c.name == name AND p1.id < p2.id AND p2.id < p3.id",
        "    ACCUM @@result += ResTuple(p1, p2, p3);",
        "",
        "  @@tupleCount = @@result.size();",
        "  @@result.clear();",
        "",
        "  PRINT @@tupleCount;",
        "}"
    );
    runTransformTest(query);
  }

  /**
   * Expected behavior: proper query transformation
   * Encountered behavior: parse error in generated output
   */
  @Test
  public void testGLE1411FalseParseError() {
    String query = String.join("\n",
        "CREATE QUERY test_GLE1411_2(string name) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  TYPEDEF TUPLE <vertex<Person> a, vertex<Person> b, vertex<Person> c> ResTuple;",
        "  SetAccum<ResTuple> @@result;",
        "  SumAccum<int> @@tupleCount;",
        "",
        "  vSet =",
        "    SELECT c",
        "    FROM Person:p1 -(IS_LOCATED_IN>.IS_PART_OF>)- Country:c,",
        "         Person:p2 -(IS_LOCATED_IN>.IS_PART_OF>)- :c,",
        "         Person:p3 -(IS_LOCATED_IN>.IS_PART_OF>)- :c,",
        "         :p1 -(KNOWS)- :p2 -(KNOWS)- :p3 -(KNOWS)- :p1",
        "    WHERE c.name == name AND p1.id < p2.id AND p2.id < p3.id",
        "    ACCUM @@result += ResTuple(p1, p2, p3);",
        "",
        "  @@tupleCount = @@result.size();",
        "  @@result.clear();",
        "",
        "  PRINT @@tupleCount;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testGLE1559() {
    String query = String.join("\n",
        "CREATE QUERY testGLE1559(string name) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  SetAccum<edge> @@eSet;",
        "  vSet =",
        "    SELECT a",
        "    FROM Person:a -(KNOWS:e1)- Person:b,",
        "         Person:b -(KNOWS:e2)- Person:c",
        "    WHERE e1 == e2",
        "    ACCUM @@eSet += e1;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testGLE1559a() {
    String query = String.join("\n",
        "CREATE QUERY testGLE1559a(string name) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  SetAccum<edge> @@eSet;",
        "  vSet =",
        "    SELECT a",
        "    FROM Person:a -(KNOWS:e1)- Person:b,",
        "         Person:b -(KNOWS:e1)- Person:c",
        "    ACCUM @@eSet += e1;",
        "}"
    );
    runTransformTest(query);
  }

  /**
   * Query with two path patterns is actually quasi-endpoint-local,
   * the two patterns should be merged at alias t into a single one.
   */
  @Test
  public void testGLE1602() {
    String query = String.join("\n",
        "CREATE OR REPLACE QUERY mergePathPatterns(String tag, DateTime date)",
        "FOR GRAPH ldbc_snb SYNTAX v2 {",
        "SetAccum<vertex<Person>> @@personSet;",
        "SetAccum<vertex> @messageSet;",
        "SumAccum<int> @score;",
        "",
        "vSet =",
        "SELECT p2",
        "FROM Person:p1 -(HAS_INTEREST>)- Tag:t,",
        "     :t -(<HAS_TAG)- (Post|Comment):m -(HAS_CREATOR>)- Person:p2",
        "WHERE t.name == tag AND m.creationDate > date",
        "ACCUM",
        "      p1.@score = 100, p2.@messageSet += m,",
        "      @@personSet += p1, @@personSet += p2",
        "POST-ACCUM",
        "      p2.@score += <EMAIL>(),",
        "      <EMAIL>();",
        "}"
    );
    runTransformTest(query);
  }

  /** Skip match extension for cyclic patterns. */
  @Test
  public void testGLE1606() {
    String query = String.join("\n",
        "CREATE OR REPLACE QUERY testGLE1606(vertex<Person> pid, datetime minDate)",
        "for graph ldbc_snb syntax v2 {",
        "  SetAccum<vertex<Post>> @postSet;",
        "  SumAccum<int> @postCount;",
        "  S = { pid };",
        "",
        "  F =",
        "  SELECT f",
        "  FROM S:s -(KNOWS*1..2)- Person:p -(<HAS_MEMBER:e)- Forum:f,",
        "       :f -(CONTAINER_OF>)- Post:m -(HAS_CREATOR>)- :p",
        "  WHERE p != s AND e.joinDate > minDate",
        "  ACCUM f.@postSet += m",
        "  POST-ACCUM f.@postCount = <EMAIL>(), <EMAIL>();",
        "",
        "  PRINT F[F.title AS forumTitle, F.@postCount AS postCount];",
        "}"
    );
    runTransformTest(query);
  }

  /**
   * Test case for pattern type checker bug - incomplete type annotation saved
   * for ACCUM clause, therefore causing {@code <IS_SUBCLASS_OF} to be dropped from
   * transformation output.
   * <p>
   * The correct baseline expects both edge patterns to be present.
   * <p>
   * TODO: design test to specifically examine type annotation set at any context
   */
  @Test
  public void testGLE1787() {
    String query = String.join("\n",
        "CREATE QUERY testGLE1787() syntax v2 {",
        "  OrAccum @valid = false;",
        "  vSet (Tag|TagClass) = { TagClass.* };",
        "  vSet =",
        "    SELECT t",
        "    FROM vSet:s -((<IS_SUBCLASS_OF|<HAS_TYPE):e)- (TagClass|Tag):t",
        "    ACCUM CASE WHEN t.type == \"Tag\" THEN t.@valid = true END;",
        "}"
    );
    runTransformTest(query);
  }

  /**
   * Test case for WHERE clause condition parsing.
   * <p>
   * The WHERE condition here does not use any terms
   * @see https://graphsql.atlassian.net/browse/GLE-2162
   */
  @Test
  public void testGLE2162() {
    String query = String.join("\n",
        "CREATE QUERY test(bool cond) FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  vSet = { Person.* };",
        "  vSet = ",
        "    SELECT t",
        "    FROM vSet:s -(LIKES>)- Post -(HAS_CREATOR>)- Person:t",
        "    WHERE cond;",
        "}"
    );
    runTransformTest(query);
  }
  /**
   * Test case for conjuntive pattern transform layer when
   * tgt is a vertex set and edge alias is required in the output.
   * <p>
   * @see https://graphsql.atlassian.net/browse/GLE-2641
   */
  @Test
  public void testGLE2641() {
    String query = String.join("\n",
        "CREATE QUERY test() FOR GRAPH ldbc_snb SYNTAX v2 {",
        "  SetAccum<edge> @@all_edges;",
        "  posts = { Post.* };",
        "  vSet = ",
        "    SELECT p",
        "    FROM Person:p -(LIKES>:e)- posts:m",
        "    ACCUM @@all_edges += e;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testAliasFreeCondExtractedFromWhereClause_1() {
    // Tests that alias free where clause condition is extracted
    // to outside of block and removed from where clause. Where
    // clause is removed since it is empty (GLE-2454).
    String query = String.join("\n",
        "CREATE QUERY test() FOR GRAPH ldbc_snb API(\"v2\") SYNTAX v2 {",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_1;",
        "  SetAccum<string> @@FinalVertexSet_2;",
        "",
        "  VertexSet_1 = ",
        "    SELECT u",
        "    FROM Person:p -(STUDY_AT>:e)- University:u",
        "    WHERE e.classYear == 2000",
        "    POST-ACCUM @@FinalVertexSet_1 += p",
        "    ;",
        "",
        "  VertexSet_a = {@@FinalVertexSet_1};",
        "\n",
        "  VertexSet_2 = ",
        "    SELECT p",
        "    FROM VertexSet_a:p -(STUDY_AT>:e)- VertexSet_1:u",
        "    WHERE \"a\" != \"b\"",
        "    POST-ACCUM @@FinalVertexSet_2 += p.firstName",
        "    ;",
        "",
        "  PRINT @@FinalVertexSet_2;",
        "",
        "  PRINT VertexSet_2;",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testAliasFreeCondExtractedFromWhereClause_2() {
    // Tests that alias free where clause condition is extracted
    // to outside of block and removed from where clause. Endpoint
    // local condition remains in where clause (GLE-2454).
    String query = String.join("\n",
        "CREATE QUERY test() FOR GRAPH ldbc_snb API(\"v2\") SYNTAX v2 {",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_1;",
        "  SetAccum<string> @@FinalVertexSet_2;",
        "",
        "  VertexSet_1 = ",
        "    SELECT u",
        "    FROM Person:p -(STUDY_AT>:e)- University:u",
        "    WHERE e.classYear == 2000",
        "    POST-ACCUM @@FinalVertexSet_1 += p",
        "    ;",
        "",
        "  VertexSet_a = {@@FinalVertexSet_1};",
        "\n",
        "  VertexSet_2 = ",
        "    SELECT p",
        "    FROM VertexSet_a:p -(STUDY_AT>:e)- VertexSet_1:u",
        "    WHERE \"a\" != \"b\" and u.name == \"Test U\"",
        "    POST-ACCUM @@FinalVertexSet_2 += p.firstName",
        "    ;",
        "",
        "  PRINT @@FinalVertexSet_2;",
        "",
        "  PRINT VertexSet_2;",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testAliasFreeCondExtractedFromWhereClause_3() {
    // Tests that alias free where clause condition is extracted
    // to outside of block and removed from where clause, but
    // evaluate function remains in where clause (GLE-2454).
    String query = String.join("\n",
        "CREATE QUERY test(string evalCond) FOR GRAPH ldbc_snb API(\"v2\") SYNTAX v2 {",
        "  SetAccum<vertex<Person>> @@FinalVertexSet_1;",
        "  SetAccum<string> @@FinalVertexSet_2;",
        "",
        "  VertexSet_1 = ",
        "    SELECT u",
        "    FROM Person:p -(STUDY_AT>:e)- University:u",
        "    WHERE e.classYear == 2000",
        "    POST-ACCUM @@FinalVertexSet_1 += p",
        "    ;",
        "",
        "  VertexSet_a = {@@FinalVertexSet_1};",
        "\n",
        "  VertexSet_2 = ",
        "    SELECT p",
        "    FROM VertexSet_a:p -(STUDY_AT>:e)- VertexSet_1:u",
        "    WHERE \"a\" != \"b\" and evaluate(evalCond)",
        "    POST-ACCUM @@FinalVertexSet_2 += p.firstName",
        "    ;",
        "",
        "  PRINT @@FinalVertexSet_2;",
        "",
        "  PRINT VertexSet_2;",
        "}");
    runTransformTest(query);
  }

  @Test
  public void testBI11() {
    String query = String.join("\n",
        "CREATE QUERY bi_11(string cName, set<string> blacklist) for graph ldbc_snb {",
        "  AndAccum @legalContent;",
        "  SumAccum<uint> @personId;",
        "  SetAccum<vertex<Tag>> @replyTags, @msgTags, @sharedTags;",
        "  SetAccum<vertex<Person>> @likeSet;",
        "  SumAccum<int> @sharedTagCount, @replyCount, @likeCount;",
        "  vSet =",
        "    SELECT r",
        "    FROM Comment:r -(HAS_CREATOR>)- Person:p -(IS_LOCATED_IN>.IS_PART_OF>)- Country:c,",
        "         :r -(HAS_TAG>)- Tag:t1,",
        "         :r -(REPLY_OF>)- (Post|Comment):m -(HAS_TAG>)- Tag:t2,",
        "         Person:p2 -(LIKES>)- :r",
        "    WHERE c.name == cName",
        "    ACCUM",
        "      FOREACH wordPattern IN blacklist DO",
        "        CASE WHEN r.content LIKE wordPattern THEN",
        "          r.@legalContent += false",
        "        END",
        "      END,",
        "      r.@personId = p.id,",
        "      r.@replyTags += t1,",
        "      r.@msgTags += t2,",
        "      r.@likeSet += p2",
        "    POST-ACCUM",
        "      r.@sharedTags = r.@replyTags INTERSECT r.@msgTags,",
        "      r.@sharedTagCount = <EMAIL>(),",
        "      <EMAIL>(),",
        "      <EMAIL>(),",
        "      <EMAIL>(),",
        "      r.@likeCount = <EMAIL>(),",
        "      <EMAIL>()",
        "    HAVING r.@legalContent == true AND r.@sharedTagCount == 0;",
        "}"
    );
    runTransformTest(query);
  }

  @Test
  public void testGLE6929() {
    String[] qlines = {"CREATE DISTRIBUTED QUERY test(Vertex<Person> ver) FOR GRAPH ldbc_snb",
        "  SYNTAX v2 {",
        "  SumAccum<INT> @activityAmount;",
        "  male = SELECT s",
        "         FROM Person:s",
        "         WHERE s.gender == \"male\";", "  start = {ver};",
        "  result1 =",
        "    SELECT s",
        "    FROM male:s -(LIKES>:e1)- :t1 -(<LIKES:e2)- start:t",
        "    ACCUM s.@activityAmount += 1",
        "    HAVING s.gender == \"female\"",
        "    LIMIT 10;",
        "  SELECT s AS people, count(t) AS peopleCount INTO T",
        "    FROM male:s -(LIKES>:e1)- :t1 -(<LIKES:e2)- start:t",
        "    GROUP BY s",
        "    HAVING peopleCount > 1",
        "    ORDER BY peopleCount DESC",
        "    LIMIT 10;" ,
        "  PRINT result1;",
        "  PRINT T;",
        "}"
    };
    flags_.add(compileFlags.TRANSFORMATION_TEST);
    runTransformTest(String.join("\n", qlines));
  }

  @Test
  public void testGLE7166() {
    String[] qlines = {"CREATE OR REPLACE QUERY test(UINT personId=10995116278566) ",
        "FOR GRAPH ldbc_snb SYNTAX V2 {",
        "  ListAccum<EDGE> @@res;",
        "  P = SELECT s ",
        "      FROM Person:s -(LIKES>:e1)- ()-(HAS_CREATOR>:e2)- ()",
        "      WHERE s.id == personId",
        "      ACCUM @@res += e1;",
        "  PRINT @@res;",
        "}"
    };
    flags_.add(compileFlags.TRANSFORMATION_TEST);
    runTransformTest(String.join("\n", qlines));
  }

  @Test
  public void testLimitWithNonLocalCondition() {
    // GLE-10084 limit table with non-local condition
    String query = String.join("\n",
        "CREATE OR REPLACE QUERY testLimitWithNonLocalCondition() FOR GRAPH ldbc_snb SYNTAX V3 {",
        "  SELECT p INTO T",
        "  FROM (s:Person) -[:KNOWS]- (f:Person) -[:KNOWS]- (p:Person)",
        "  WHERE  s != p",
        "  LIMIT 3;",
        "  PRINT T;",
        "}"
    );
    flags_.add(compileFlags.TRANSFORMATION_TEST);
    runTransformTest(query);
  }
}
