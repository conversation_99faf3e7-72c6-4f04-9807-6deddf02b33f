package com.tigergraph.schema.handler;

import com.tigergraph.schema.SchemaSetup;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Test;
import org.skyscreamer.jsonassert.JSONAssert;

import java.io.IOException;
import java.net.URISyntaxException;

import static org.junit.Assert.assertEquals;

public class CodeCheckHandlerTest extends BaseHandlerTest {
  @Override
  public void setup() throws IOException, URISyntaxException {
    super.setup();
    SchemaSetup.CreateGraph("poc_graph", command_);
  }

  @Test
  public void testInvalidGraphName() throws IOException {
    // prepare expected output
    JSONObject expected = new JSONObject();
    expected.put("warnings", new JSONArray());
    JSONArray errors = new JSONArray();
    JSONObject msg = new JSONObject();
    msg.put("msg", "Graph G does not exist!");
    errors.put(0, msg);
    expected.put("errors", errors);

    // prepare input
    String query = String.join("\n",
        "create query test() for graph G {",
        "  print \"okay\";",
        "}"
    );
    JSONObject input = new JSONObject();
    input.put("code", query);
    input.put("graph", "G");
    writeRequestBody(input.toString());

    // run test and check reported error
    CodeCheckHandler handler = new CodeCheckHandler();
    runTest(handler);
    JSONObject output = new JSONObject(response.toString());
    JSONAssert.assertEquals(expected, output, true);
  }

  @Test
  public void testQueryWithErrorAndWarning() throws IOException {
    CodeCheckHandler handler = new CodeCheckHandler();

    // prepare input
    String query = String.join("\n",
        "create query test(int a, string b) for graph poc_graph {",
        "  if a == 5 then",
        "    mSet = { members.* };",
        "  end;",
        // below line should generate a warning since mSet may not have been initialized
        "  cSet = select t from mSet:s -(member_follow_company)-> company:t;",
        // below line should generate an error since int and string are incompatible
        "  print a == b;",
        "}"
    );
    JSONObject input = new JSONObject();
    input.put("code", query);
    input.put("graph", "poc_graph");
    writeRequestBody(input.toString());

    // run test and check reported error
    runTest(handler);
    JSONObject output = new JSONObject(response.toString());
    JSONArray warnings = output.getJSONArray("warnings");
    JSONArray errors = output.getJSONArray("errors");
    assertEquals(warnings.toString(), 1, warnings.length());
    assertEquals(errors.toString(), 2, errors.length());
  }

  @Test
  public void testBitwiseQueryWithError() throws IOException {
    CodeCheckHandler handler = new CodeCheckHandler();

    // prepare input
    String query = String.join("\n",
        "create query test() for graph poc_graph {",
        // below line should generate Bitwise length larger than 10000000 error
        "  BitwiseAndAccum<85899345920> @bit_big_a;",
        "  S=select s from ANY:s accum s.@bit_big_a+=1;",
        "}"
    );
    JSONObject input = new JSONObject();
    input.put("code", query);
    input.put("graph", "poc_graph");
    writeRequestBody(input.toString());

    // run test and check reported error
    runTest(handler);
    JSONObject output = new JSONObject(response.toString());
    JSONArray warnings = output.getJSONArray("warnings");
    JSONArray errors = output.getJSONArray("errors");
    assertEquals(warnings.toString(), 0, warnings.length());
    assertEquals(errors.toString(), 2, errors.length());
  }
}
