package com.tigergraph.schema.handler;

import com.tigergraph.schema.SchemaSetup;
import com.tigergraph.schema.utility.HttpMethod;
import org.json.JSONObject;
import org.junit.Test;

import java.io.IOException;
import java.net.URISyntaxException;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import tigergraph.tutopia.common.pb.Config;
import com.tigergraph.schema.config.ConfigManager;

public class DataSourcesHandlerTest extends BaseHandlerTest {
  private final String createDataSource4 = "begin\n"
      + "create data_source s0 = \"\"\"{\n"
      + "    \"type\":\"mirrormaker\",\n"
      + "    \"source.cluster.bootstrap.servers\":\"aaa\",\n"
      + "    \"consumer.ssl.truststore.password\": \"1234\",\n"
      + "    \"consumer.sasl.kerberos.service.name\": \"kafka\",\n"
      + "    \"consumer.sasl.jaas.config\":"
      + "\"org.apache.kafka.common.security.plain.PlainLoginModule "
      + "required username=\\\"svc_npcpdstrmsetdev\\\" "
      + "password=\\\"Q~7URvJcd*+He689fMpNWGLY\\\"\"\n"
      + "}\"\"\" FOR GRAPH empty_graph\n"
      + "end";

  @Test
  public void testMaskPasswordInDataSource() throws Exception {
    SchemaSetup.EmptyGraph(command_);
    command_.runCmd("use graph empty_graph");
    command_.runCmd(createDataSource4);
    String str = command_.runCmd("show data_source s0");
    // should mask LDAP password inside sasl.jaas.config
    assertEquals("Data Sources:\n"
        + "  - MIRRORMAKER s0 ({\"consumer.ssl.truststore.password\":\"****\","
        + "\"consumer.sasl.kerberos.service.name\":\"kafka\",\"consumer.sasl.jaas.config\":"
        + "\"org.apache.kafka.common.security.plain.PlainLoginModule required username="
        + "\\\"svc_npcpdstrmsetdev\\\" password=\\\"****\\\"\",\"type\":\"mirrormaker\","
        + "\"source.cluster.bootstrap.servers\":\"aaa\"})\n", str);

    // set env to mask jaas config
    Config.Configuration config = ConfigManager.get().getConfig();
    Config.Configuration.Builder builder = config.toBuilder();
    String oldEnv = config.getGSQL().getBasicConfig().getEnv();
    String newEnv = oldEnv + " MASK_JAAS_CONFIG=true;";

    Config.Configuration.Builder builder2 = Config.Configuration.newBuilder()
        .setGSQL(Config.GSQLConfig.newBuilder()
            .setBasicConfig(Config.BasicConfig.newBuilder()
                .setEnv(newEnv)));

    builder.mergeFrom(builder2.build());
    ConfigManager.get().setConfig(builder.build());
    str = command_.runCmd("show data_source s0");
    assertEquals("Data Sources:\n"
            + "  - MIRRORMAKER s0 ({\"consumer.ssl.truststore.password\":\"****\","
            + "\"consumer.sasl.kerberos.service.name\":\"kafka\",\"consumer.sasl.jaas.config\":"
            + "\"****\",\"type\":\"mirrormaker\",\"source.cluster.bootstrap.servers\":\"aaa\"})\n",
        str);
  }

  @Test
  public void testGetPostDeleteDataSource() throws URISyntaxException, IOException {
    SchemaSetup.PersonMovie(command_);
    command_.runCmd("create data_source kafka k1");
    command_.runCmd("create data_source kafka k2 for graph person_movie");
    DataSourcesHandler handler = new DataSourcesHandler();

    // create data source
    setMethod(HttpMethod.POST);
    writeRequestBody("{\"name\":\"ds_1\",\"type\":\"kafka\","
        + "\"config\":{\"broker\":\"127.0.0.1:9092\"}}");
    runTest(handler);
    JSONObject json = new JSONObject(response.toString());
    assertThat(json.toString(), containsString("Data source 'ds_1' is created."));

    // graph not exit
    setParam("graph", "graph_test");
    runTest(handler);
    assertThat(response.toString(), containsString("Graph 'graph_test' doesn't exist."));

    // get data source
    setMethod(HttpMethod.GET);
    setParam("graph", "person_movie");
    runTest(handler);
    json = new JSONObject(response.toString());
    String js = json.toString();
    assertTrue(js, js.contains("\"name\":\"k2\"") && !js.contains("\"name\":\"k1\""));

    // delete data source
    setMethod(HttpMethod.DELETE);
    addParam("name", "ds_not_exist");
    runTest(handler);
    json = new JSONObject(response.toString());
    assertEquals(json.getString("message"), "Data source 'ds_not_exist' does not exist.");


    setParam("name", "ds_1");
    runTest(handler);
    json = new JSONObject(response.toString());
    assertEquals(json.getString("message"),
        "Data source 'ds_1' is revoked from graph 'person_movie'.");
  }
}
