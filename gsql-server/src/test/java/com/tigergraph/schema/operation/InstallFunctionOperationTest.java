package com.tigergraph.schema.operation;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.SchemaSetup;
import com.tigergraph.schema.crr.ReplicationManager;
import com.tigergraph.schema.plan.function.FunctionInfo;
import com.tigergraph.schema.plan.query.Queries;

import java.util.List;

import org.junit.Test;

public class InstallFunctionOperationTest extends BaseMetadataUpdateOperationTest {

  @Test
  public void testInstallFunctionOperation() {
    // setup schema
    SchemaSetup.TestGraph(command_);
    // use global
    CatalogManager.unsetGraph();

    // setup packages
    String cmd = "create package lib1\n"
        + "create package lib1.lib2\n"
        + "create package lib1.lib3\n"
        + "create package lib4\n"
        + "create package lib4.lib5\n";
    
    assertThat(command_.runCmd(cmd), containsString(
        "Successfully created Packages:"));

    // setup functions
    final String SIMPLE_FUNC_FMT = "create function %s() returns (bool) { return true; }\n";
    cmd = String.format(SIMPLE_FUNC_FMT, "lib1.func1_1")
        + String.format(SIMPLE_FUNC_FMT, "lib1.func_X")
        + String.format(SIMPLE_FUNC_FMT, "lib1.lib2.func12")
        + String.format(SIMPLE_FUNC_FMT, "lib1.lib3.func13_1")
        + String.format(SIMPLE_FUNC_FMT, "lib1.lib3.func_X")
        + String.format(SIMPLE_FUNC_FMT, "lib4.func_X");
    assertThat(command_.runCmd(cmd), containsString(
        "Successfully created functions:"));

    long curSeqId = ReplicationManager.get().getLastSeqId().get();
    // test 1: install single function
    cmd = "Install Function lib1.func_X";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    verifyHttpRequest(++curSeqId,
        "INSTALL FUNCTION lib1.func_X");

    // test 2: install 2 functions from different packages
    cmd = "Install Function lib1.func1_1, lib1.lib2.func12";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    verify2ndLastHttpRequest(++curSeqId, "POST", "/gsql/file",
        "INSTALL FUNCTION lib1.func1_1");
    verifyHttpRequest(++curSeqId,
        "INSTALL FUNCTION lib1.lib2.func12");

    // test 3: install all direct functions in a package
    cmd = "Install Function lib1.*";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    verifyHttpRequest(++curSeqId,
        "INSTALL FUNCTION lib1.*");
    
    // test 4: install all functions in nested subpackages
    cmd = "Install Function lib1.**";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    // there are 3 (sub)packages under lib1:
    // lib1, lib1.lib2, lib1.lib3
    curSeqId += 3;
    verifyHttpRequest(curSeqId,
        "INSTALL FUNCTION lib1.lib3.*");
    
    // test 5: install all functions
    cmd = "Install Function All";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    // there are 5 (sub)packages in total
    curSeqId += 5;
    verifyHttpRequest(curSeqId,
        "INSTALL FUNCTION lib4.lib5.*");
    
    // test 6: install functions with '-force'
    cmd = "Install Function -Force All";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    // there are 5 (sub)packages in total
    curSeqId += 5;
    verifyHttpRequest(curSeqId,
        "INSTALL FUNCTION -FORCE lib4.lib5.*");

    // test 7: install functions with '-force -debug'
    cmd = "Install Function -Force -Debug lib1.lib3.func_X";
    assertThat(command_.runCmd(cmd), containsString("Function installation finished"));
    verifyHttpRequest(++curSeqId,
        "INSTALL FUNCTION -FORCE -DEBUG lib1.lib3.func_X");

    // test 8: verify that successful function names are printed
    cmd = "create function lib1.testFunc() returns (bool) { return true; }\n"
        + "install function lib1.testFunc";
    String result = command_.runCmd(cmd);
    assertThat(result, containsString("Function installation finished"));
    assertThat(result, containsString("The following functions were successfully installed:"));
    assertThat(result, containsString("[testFunc]"));
  }

  @Test
  public void testCompileFunctionUpToLink() {
    // setup schema
    SchemaSetup.TestGraph(command_);
    // use global
    CatalogManager.unsetGraph();

    assertEquals("Successfully created Packages: [p1].\n",
                 command_.runCmd("create package p1"));
    assertEquals("Successfully created Packages: [p1.p2].\n",
                 command_.runCmd("create package p1.p2"));

    String cmd;

    // Simple Testcase
    cmd = "create function p1.f1(String s) returns (bool) {"
          + " int i = 4; return true; }\n";
    assertEquals("Successfully created functions: [p1.f1].\n",
                 command_.runCmd(cmd));

    FunctionInfo fi = (FunctionInfo) CatalogManager.getGlobalCatalog()
                                                   .getInMemoryPackages()
                                                   .get("p1")
                                                   .getObjectMap()
                                                   .get("f1");

    // Populate a Queries object to send to installFunction
    Queries queries = new Queries();
    queries.addFunctionInfoToQueries(fi);

    InstallFunctionOperation installFuncOp = new InstallFunctionOperation(
        CatalogManager.getGlobalCatalog(),
        List.of("p1"), List.of("f1"), false, false);

    installFuncOp.run();

    assertTrue(installFuncOp.getSucceed());
  }

}
