package com.tigergraph.schema.operation;

import static org.hamcrest.CoreMatchers.containsString;
import static org.junit.Assert.assertThat;

import com.tigergraph.schema.SchemaSetup;
import com.tigergraph.schema.crr.ReplicationManager;

import org.junit.Test;

public class InstallQueryOperationTest extends BaseMetadataUpdateOperationTest {

  @Test
  public void testInstallQueryOperation() {
    // setup graph
    SchemaSetup.PersonMovie2(command_);
    String cmd = "create query q1() { print 1; }\n"
        + "create query q2() { print 2; }\n"
        + "create query q3() { print 3; }\n"
        + "create query q4() { print 4; }\n"
        + "create query q5() { print 5; }\n";
    assertThat(command_.runCmd(cmd), containsString(
        "Successfully created queries: [q5]."));
    long curSeqId = ReplicationManager.get().getLastSeqId().get();

    // test 1: simple install query
    cmd = "Install Query q1";
    assertThat(command_.runCmd(cmd), containsString("Query installation finished."));
    verifyHttpRequest(++curSeqId,
        "INSTALL QUERY q1");

    // test 2: install using syntax sugar
    cmd = "Install Query *";
    assertThat(command_.runCmd(cmd), containsString("Query installation finished."));
    verifyHttpRequest(++curSeqId,
        "INSTALL QUERY q1, q2, q3, q4, q5");

    // test 3: install with -optimize should throw semantic error
    cmd = "Install Query -Optimize";
    assertThat(command_.runCmd(cmd),
        containsString("The optimize flag has been deprecated and is no longer supported."));

    // test 4: verify that successful query names are printed
    cmd = "create query testQuery() { print \"test\"; }\n"
        + "install query testQuery";
    String result = command_.runCmd(cmd);
    assertThat(result, containsString("Query installation finished."));
    assertThat(result, containsString("The following queries were successfully installed:"));
    assertThat(result, containsString("[testQuery]"));
  }

}
