package com.tigergraph.schema.ast.ddl.create;

import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.GlobalCatalog;
import com.tigergraph.schema.SchemaSetup;
import com.tigergraph.schema.TestCase;
import com.tigergraph.common.MessageBundle;

import com.tigergraph.schema.topology.DataSource;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;

public class CreateDataSourceTest extends TestCase {
  @Test
  // use to test the escapeConfig method in DataSource class is correct or not
  public void testEscapeDataSourceConfig() {
    // Step 1: set up a graph
    SchemaSetup.CreateGraph("ldbc_snb_v2", command_);
    StringBuilder command = new StringBuilder("begin\n"
        + "create data_source s0 = \"\"\"{\n"
        + "\"type\":\"mirrormaker\",\n"
        + "\"source.cluster.bootstrap.servers\":\"aaa\",\n"
        + "\"consumer.ssl.truststore.password\": \"1234\",\n"
        + "\"consumer.sasl.kerberos.service.name\": \"kafka\",\n"
        + "\"consumer.sasl.jaas.config\":\"org.apache.kafka.common.security.plain.PlainLoginModule"
        + " required username=\\\"svc_npcpdstrmsetdev\\\" "
        + "password=\\\"Q~7URvJcd*+He689fMpNWGLY\\\"\"\n"
        + "}\"\"\"\n"
        + "end\n");
    // Step 2: Create initial data source
    String str = command_.runCmd(command.toString());
    // Step 3: Verify initial data source creation
    assertEquals("Successfully created data sources: [s0].\n", str);

    // Step 4: Create a copy of the data source using the escapedConfig
    GlobalCatalog globalCatalog = CatalogManager.getGlobalCatalog();
    command = new StringBuilder("CREATE DATA_SOURCE ");
    command.append("s0_copy");
    command.append(" = \"");
    command.append(globalCatalog.getInMemoryDataSources().get("s0").escapeConfig()).append("\"");
    str = command_.runCmd(command.toString());

    // Execute the command to create the data source copy, should succeed
    assertEquals("Successfully created data sources: [s0_copy].\n", str);

    // Step 5: Verify the copied data source configuration is the same as the original
    assertEquals(
        globalCatalog.getInMemoryDataSources().get("s0").getDecryptedJsonConfig().toString(),
        globalCatalog.getInMemoryDataSources().get("s0_copy").getDecryptedJsonConfig().toString());
  }

  @Test
  public void testCreateDataSource() {
    SchemaSetup.CreateGraph("poc_graph", command_);

    // Test 1 - Create data_source with existing name
    String command = "CREATE DATA_SOURCE poc_graph";
    assertEquals("Semantic Check Fails: The data source name conflicts with another"
            + " type or existing data source names! Please use a different name.\n",
        command_.runCmd(command));

    // Test 2 - Create data_source for non-existing graph
    command = "CREATE DATA_SOURCE s3a for graph no_graph";
    assertEquals("Semantic Check Fails: Graph \"no_graph\" does not exist.\n",
                 command_.runCmd(command));

    command = "CREATE DATA_SOURCE s2 = \"{ "
        + "access.key:********************, secret.key:8yWfA54skmiORZVDTafLPL6O3jfo07YsvwBMRYbx}\" "
        + "for graph poc_graph";
    assertEquals("SemanticException: \"type\" key must be specified in the configuration.\n",
                 command_.runCmd(command));
  }

  @Test
  public void testReadJson() {
    // Test 1 - read json from a file
    String output = command_.runCmd("CREATE DATA_SOURCE s1 = '''/tmp/notExist/abc'''");
    String expected = "SemanticException: File \"/tmp/notExist/abc\" is not found on server\n";
    assertEquals(expected, output);
    // Test 2 - read json from a json string
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{a:d,c:d,dd}'''");
    expected = String.join("\n",
    "SemanticException: Expected a ':' after a key at 12 [character 13 line 1]:",
        "{a:d,c:d,dd}\n");
    assertEquals(expected, output);
  }

  @Test
  public void testS3Config() {
    // Test 1 - invalid type
    String output = command_.runCmd(
        "CREATE DATA_SOURCE s1 = '''{type:999}'''");
    String expected =
        "SemanticException: \"type\" must be one of following: "
        + StringUtils.join(DataSource.Type.values(), ", ") + "\n";

    assertEquals(expected, output);
    // Test 2 - secret.key is missing
    output = command_.runCmd(
        "CREATE DATA_SOURCE s1 = '''{type:s3,access.key:a}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "secret.key", "STRING",
            "{\"access.key\":\"****\",\"type\":\"s3\"}")
        + "\n";
    assertEquals(expected, output);
  }

  @Test
  public void testSnowflakeConfig() {
    // Test 1 - Username is missing
    String output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:snowflake,query:select}'''");
    String expected = "Semantic Check Fails: "
                      + MessageBundle.get("dsConfig.notFound",
                                          "connection.user",
                                          "STRING",
                                          "{\"query\":\"select\",\"type\":\"snowflake\"}")
                      + "\n";
    assertEquals(expected, output);

    // Test 2 - Password missing
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:snowflake,query:select,"
                             + "connection.user:test1}'''");
    expected = "Semantic Check Fails: "
               + MessageBundle.get("dsConfig.notFound",
                       "connection.password", "STRING",
                       "{\"connection.user\":"
                       + "\"test1\",\"query\":\"select\",\"type\":\"snowflake\"}")
               + "\n";
    assertEquals(expected, output);

    // Test 3 - connection url missing. Password should be masked.
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:snowflake,query:select,"
                             + "connection.user:test1, connection.password:password}'''");
    expected = "Semantic Check Fails: "
               + MessageBundle.get("dsConfig.notFound", "connection.url", "STRING",
                                   "{\"connection.password\":\"****\","
                                   + "\"connection.user\":\"test1\","
                                   + "\"query\":\"select\",\"type\":\"snowflake\"}")
               + "\n";
    assertEquals(expected, output);

    // Test 4 - Success create datasource
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:snowflake,query:select,"
                             + "connection.user:test1, connection.password:password,"
                             + " connection.url:url}'''");
    expected = "Successfully created data sources: [s1].\n";
    assertEquals(expected, output);
  }

  @Test
  public void testBigQueryConfig() {
    // Test 4 - ProjectId is missing
    String output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:bigquery,query:select}'''");
    String expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "ProjectId", "STRING",
            "{\"query\":\"select\",\"type\":\"bigquery\"}")
        + "\n";
    assertEquals(expected, output);

    // Test 5 - Missing OAuthServiceAcctEmail
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:0,parameters:{OAuthPvtKeyPath:test}}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "parameters->OAuthServiceAcctEmail", "STRING",
            "{\"OAuthType\":0,\"query\":\"select\",\"ProjectId\":\"test1\",\"type\":\"bigquery\","
            + "\"parameters\":{\"OAuthPvtKeyPath\":\"****\"}}")
        + "\n";
    assertEquals(expected, output);

    // Test 6 - Missing OAuthPvtKeyPath
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:0,parameters:{OAuthServiceAcctEmail:email}}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "parameters->OAuthPvtKeyPath", "STRING",
            "{\"OAuthType\":0,\"query\":\"select\",\"ProjectId\":\"test1\",\"type\":\"bigquery\","
            + "\"parameters\":{\"OAuthServiceAcctEmail\":\"email\"}}")
        + "\n";
    assertEquals(expected, output);

    // Test 7 - Missing OAuthType
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:bigquery,query:select,"
        + "ProjectId:test1}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "OAuthType", "STRING",
            "{\"query\":\"select\",\"ProjectId\":\"test1\",\"type\":\"bigquery\"}")
        + "\n";
    assertEquals(expected, output);

    // Test 8 - Successful test with OAuthType 0
    output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:0,parameters:{OAuthServiceAcctEmail:email,"
        + "OAuthPvtKeyPath:test}}'''");
    expected = "Successfully created data sources: [s1].\n";
    assertEquals(expected, output);

    // Test 9 - OAuthType 0 with type 2 configs
    output = command_.runCmd("CREATE DATA_SOURCE s2 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:0,parameters:{OAuthAccessToken:atoken,"
        + "OAuthRefreshToken:rtoken}}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "parameters->OAuthServiceAcctEmail", "STRING",
            "{\"OAuthType\":0,\"query\":\"select\",\"ProjectId\":\"test1\",\"type\":\"bigquery\","
            + "\"parameters\":{\"OAuthRefreshToken\":\"****\",\"OAuthAccessToken\":\"****\"}}")
        + "\n";
    assertEquals(expected, output);

    // Test 10 - OAuthType 2 with type 0 configs
    output = command_.runCmd("CREATE DATA_SOURCE s2 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:2,parameters:{OAuthServiceAcctEmail:email,"
        + "OAuthPvtKeyPath:test}}'''");
    expected = "Semantic Check Fails: When OAuthType is 2, one of OAuthAccessToken and "
        + "OAuthRefreshToken is required.\n";
    assertEquals(expected, output);

    // Test 11 - Invalid OAuthType 1
    output = command_.runCmd("CREATE DATA_SOURCE s2 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:1}'''");
    expected = "Semantic Check Fails: OAuthType must be 0 or 2.\n";
    assertEquals(expected, output);

    // Test 12 - Successful test with OAuthType 2 and OAuthRefreshToken
    output = command_.runCmd("CREATE DATA_SOURCE s2 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:2,parameters:{OAuthRefreshToken:rtoken}}'''");
    expected = "Successfully created data sources: [s2].\n";
    assertEquals(expected, output);

    // Test 13 - Missing refresh or access token
    output = command_.runCmd("CREATE DATA_SOURCE ds3 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:2}'''");
    expected = "Semantic Check Fails: When OAuthType is 2, one of OAuthAccessToken and "
        + "OAuthRefreshToken is required.\n";
    assertEquals(expected, output);

    // Test 14 - Successful test with OAuthType 2 and OAuthAccessToken
    output = command_.runCmd("CREATE DATA_SOURCE ds3 = '''{type:bigquery,query:select,"
        + "ProjectId:test1,OAuthType:2,parameters:{OAuthAccessToken:atoken}}'''");
    expected = "Successfully created data sources: [ds3].\n";
    assertEquals(expected, output);

    // Test 15 - Successful test with OAuthType 2
    output = command_.runCmd("CREATE DATA_SOURCE s4 = '''{type:bigquery,query:select,ProjectId:t1"
        + ",OAuthType:2,parameters:{OAuthAccessToken:atoken,OAuthRefreshToken:rtoken}}'''");
    expected = "Successfully created data sources: [s4].\n";
    assertEquals(expected, output);
  }

  @Test
  public void testPostgreSQLConfig() {
    // Test 1 - host is missing
    String output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:postgresql,query:select}'''");
    String expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "host", "STRING",
            "{\"query\":\"select\",\"type\":\"postgresql\"}")
        + "\n";
    assertEquals(expected, output);

    // Test 2 - user name is missing
    output = command_.runCmd(
        "CREATE DATA_SOURCE s1 = '''{type:postgresql,query:select,host:host}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "connection.user", "STRING",
            "{\"query\":\"select\",\"host\":\"host\",\"type\":\"postgresql\"}")
        + "\n";
    assertEquals(expected, output);

    // Test 3 - password is missing
    output = command_.runCmd(
        "CREATE DATA_SOURCE s1 = " 
        + "'''{type:postgresql,query:select,host:host,connection.user:user}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "connection.password", "STRING",
            "{\"connection.user\":\"user\",\"query\":\"select\"," 
            + "\"host\":\"host\",\"type\":\"postgresql\"}")
        + "\n";
    assertEquals(expected, output);

    // Test 4 - Successful creation of Data Source
    output = command_.runCmd(
        "CREATE DATA_SOURCE s1 = "
        + "'''{type:postgresql,query:select,host:host," 
        + "connection.user:user,connection.password:pass}'''");
    expected = "Successfully created data sources: [s1].\n";
    assertEquals(expected, output);
  }

  @Test
  public void testCreateDataSourceOptions() {
    String output = command_.runCmd("CREATE DATA_SOURCE --local s1");
    String expected = "Warning: Option --local is no longer recognized.\n"
                      + "Successfully created data sources: [s1].\n";
    assertThat(output, containsString(expected));
  }

  @Test
  public void testShowDataSourceOptions() {
    String output = command_.runCmd("CREATE DATA_SOURCE s4 = "
        + "'''{type:bigquery,query:select,ProjectId:t1,OAuthType:2,parameters:"
        + "{OAuthAccessToken:atoken,OAuthRefreshToken:rtoken}}'''");
    String expected = "Successfully created data sources: [s4].\n";
    assertEquals(expected, output);
    expected = "Data Sources:\n"
               + "  - BIGQUERY s4 ({\"OAuthType\":2,\"query\":\"select\",\"ProjectId\":\"t1\","
               + "\"type\":\"bigquery\",\"parameters\":{\"OAuthRefreshToken\":\"****\","
               + "\"OAuthAccessToken\":\"****\"}})\n";
    assertEquals(expected, command_.runCmd("show data_source -r \"s.\""));
  }

  @Test
  public void testMirrorMakerConfig() {
    // Test 1 - Positive
    String output = command_.runCmd("CREATE DATA_SOURCE s1 = '''{type:mirrormaker,"
        + "source.cluster.bootstrap.servers:\"1.1.1.1:30002,1.1.1.2:30002\","
        + "source.ssl.keystore.location:key.jks,source.ssl.keystore.password:abc123}'''");
    String expected = "Successfully created data sources: [s1].\n";
    assertEquals(expected, output);
    // Test 2 - Missing source.cluster.bootstrap.servers, meanwhile, the SSL info should be masked
    output = command_.runCmd("CREATE DATA_SOURCE s2 = '''{type:mirrormaker,"
        + "source.ssl.keystore.location:key.jks,source.ssl.keystore.password:abc123}'''");
    expected = "Semantic Check Fails: "
        + MessageBundle.get("dsConfig.notFound", "source.cluster.bootstrap.servers", "STRING",
            "{\"source.ssl.keystore.location\":\"****\",\"source.ssl.keystore.password\":\"****\"," 
                + "\"type\":\"mirrormaker\"}")
        + "\n";
    assertEquals(expected, output);
  }
}
