package com.tigergraph.schema.catalog;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertTrue;

import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.SchemaSetup;
import com.tigergraph.schema.TestCase;
import com.tigergraph.schema.Util;
import com.tigergraph.schema.config.Config;
import com.tigergraph.schema.topology.GPESchema;

import java.io.File;
import java.security.Permission;
import java.util.Arrays;

import org.apache.commons.cli.CommandLine;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;

public class SpecialOperationRunnerTest extends TestCase {

  protected static class ExitException extends SecurityException {
    public final int status;
    public ExitException(int status) {
      super("Exit with status " + status);
      this.status = status;
    }
  }

  private static class NoExitSecurityManager extends SecurityManager {
    @Override
    public void checkPermission(Permission perm) {}
    @Override
    public void checkPermission(Permission perm, Object context) {}

    @Override
    public void checkExit(int status) {
      super.checkExit(status);
      throw new ExitException(status);
    }
  }

  @Rule
  public ExpectedException exceptionRule = ExpectedException.none();

  @Override
  public void setUp() {
    super.setUp();
    System.setSecurityManager(new NoExitSecurityManager());
  }

  @Override
  public void cleanUp() {
    System.setSecurityManager(null); // or save and restore original
    super.cleanUp();
  }

  @Test
  public void testReset() {
    SchemaSetup.CreateGraph("person_movie", command_);

    CommandLine cli = Mockito.mock(CommandLine.class);
    Mockito.when(cli.hasOption("reset")).thenReturn(true);

    exceptionRule.expect(ExitException.class);
    exceptionRule.expectMessage("Exit with status 0");
    SpecialOperationRunner.HandleSpecialOperation(cli);

    assertFalse("schema should be cleared", CatalogManager.isSchemaReady());
  }

  @Test
  public void testPullPushDict() {
    SchemaSetup.CreateGraph("person_movie", command_);
    String filepath = Config.sys().getTempRoot() + "/" + Util.NextRandomString(32);

    CommandLine cli = Mockito.mock(CommandLine.class);
    // test pull
    Mockito.when(cli.hasOption("pulldict")).thenReturn(true);
    Mockito.when(cli.getOptionValue("pulldict"))
        .thenReturn(filepath);

    exceptionRule.expect(ExitException.class);
    exceptionRule.expectMessage("Exit with status 0");
    SpecialOperationRunner.HandleSpecialOperation(cli);

    File f = new File(filepath);
    assertTrue(f.exists());
    assertTrue(f.isDirectory());
    // sub-graph is there
    File graph = new File(filepath + "/person_movie");
    assertTrue(graph.exists());
    assertTrue(graph.isDirectory());

    // remove in-mem catalog
    CatalogManager.RemoveAll();

    // test push
    Mockito.when(cli.hasOption("pulldict")).thenReturn(false);
    Mockito.when(cli.hasOption("pushdict")).thenReturn(true);
    Mockito.when(cli.getOptionValue("pushdict"))
        .thenReturn(filepath);
    SpecialOperationRunner.HandleSpecialOperation(cli);
    assertEquals(Arrays.asList("person_movie"), CatalogManager.getAllGraphNames());
  }

  @Test
  public void testParsingGPEconfig() {
    String yamlString = "SchemaVersion: 0\n"
        + "SegmentSizeInPowerof2: 20\n"
        + "VertexTypes:\n"
        + "  - VertexId: 0\n"
        + "    VertexName: vertex_type_1\n"
        + "    Subtype: false\n"
        + "    VertexStatisticsType: 2\n"
        + "    Primary_Id_As_Attribute: true\n"
        + "    PrimaryId:\n"
        + "      AttributeName: \"\"\n"
        + "      AttributeType: \"\"\n"
        + "      PrimaryIdAsAttribute: true\n"
        + "    GraphId: 0\n"
        + "    Attributes:\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: id\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE\n"
        + "        AttributeName: latitute\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE\n"
        + "        AttributeName: longtitute\n"
        + "        FixedSize: 0\n"
        + "  - VertexId: 1\n"
        + "    VertexName: vertex_type_2\n"
        + "    Subtype: false\n"
        + "    VertexStatisticsType: 2\n"
        + "    Primary_Id_As_Attribute: true\n"
        + "    PrimaryId:\n"
        + "      AttributeName: \"\"\n"
        + "      AttributeType: \"\"\n"
        + "      PrimaryIdAsAttribute: true\n"
        + "    GraphId: 1\n"
        + "    Attributes:\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: id\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE\n"
        + "        AttributeName: latitute\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE_LIST\n"
        + "        AttributeName: longList\n"
        + "        FixedSize: 0\n"
        + "  - VertexId: 2\n"
        + "    VertexName: vertex_type_3\n"
        + "    Subtype: false\n"
        + "    VertexStatisticsType: 2\n"
        + "    Primary_Id_As_Attribute: true\n"
        + "    PrimaryId:\n"
        + "      AttributeName: \"\"\n"
        + "      AttributeType: \"\"\n"
        + "      PrimaryIdAsAttribute: true\n"
        + "    GraphId: 1\n"
        + "    Attributes:\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: id\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE\n"
        + "        AttributeName: latitute\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: DOUBLE_SET\n"
        + "        AttributeName: longList\n"
        + "        FixedSize: 0\n"
        + "EdgeTypes:\n"
        + "  - EdgeId: 0\n"
        + "    EdgeName: Study_At1\n"
        + "    Subtype: false\n"
        + "    FromVertexTypeName: \"vertex_type_1\"\n"
        + "    ToVertexTypeName: \"vertex_type_2\"\n"
        + "    GraphId: 1\n"
        + "    IsDirected: true\n"
        + "    DiscriminatorCount: 2\n"
        + "    Attributes:\n"
        + "      - AttributeType: INT\n"
        + "        AttributeName: class_year\n"
        + "        DefaultValue: 12\n"
        + "        FixedSize: 0\n"
        + "        isDiscriminator: true\n"
        + "      - AttributeType: INT\n"
        + "        AttributeName: class_month\n"
        + "        FixedSize: 0\n"
        + "        isDiscriminator: true\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: major\n"
        + "        FixedSize: 0\n"
        + "  - EdgeId: 1\n"
        + "    EdgeName: Study_At2\n"
        + "    Subtype: false\n"
        + "    FromVertexTypeName: \"vertex_type_1\"\n"
        + "    ToVertexTypeName: \"vertex_type_2\"\n"
        + "    GraphId: 1\n"
        + "    IsDirected: true\n"
        + "    DiscriminatorCount: 2\n"
        + "    Attributes:\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: class_year\n"
        + "        DefaultValue: NULL\n"
        + "        FixedSize: 0\n"
        + "        isDiscriminator: true\n"
        + "      - AttributeType: INT\n"
        + "        AttributeName: class_month\n"
        + "        FixedSize: 0\n"
        + "        isDiscriminator: true\n"
        + "      - AttributeType: STRING\n"
        + "        AttributeName: major\n"
        + "        FixedSize: 0";
    exceptionRule.expect(RuntimeException.class);
    GPESchema.deserialize(yamlString);

    // Fix yaml string to remove PrimaryId object and deserialize again
    yamlString = GPESchema.fixYamlString(yamlString);
    GPESchema.deserialize(yamlString);
    assertNotEquals(GPESchema.get(), null);
  }

  @Test
  public void testParsingGPEconfig2() {
    // GLE-9592
    // This schema invludes two vertices:
    // - v0(PRIMARY_ID id UINT, test_deleted UINT) has no deleted attributes
    // - v1(PRIMARY_ID id UINT, id2 UINT) has id2 deleted and becomes v1(PRIMARY_ID id UINT)
    String yamlString = "SchemaVersion: 2\n"
        + "SegmentSizeInPowerof2: 20\n"
        + "VertexTypes:\n"
        + "  - VertexId: 0\n"
        + "    VertexName: v0\n"
        + "    Subtype: false\n"
        + "    VertexStatisticsType: 2\n"
        + "    Primary_Id_As_Attribute: true\n"
        + "    PrimaryId:\n"
        + "      AttributeName: id\n"
        + "      AttributeType: UINT\n"
        + "      PrimaryIdAsAttribute: true\n"
        + "    GraphId: 1\n"
        + "    Attributes:\n"
        + "      - AttributeType: UINT\n"
        + "        AttributeName: id\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: UINT\n"
        + "        AttributeName: test_deleted\n"
        + "        FixedSize: 0\n"
        + "  - VertexId: 1\n"
        + "    VertexName: v1\n"
        + "    Subtype: false\n"
        + "    VertexStatisticsType: 2\n"
        + "    Primary_Id_As_Attribute: true\n"
        + "    PrimaryId:\n"
        + "      AttributeName: id\n"
        + "      AttributeType: UINT\n"
        + "      PrimaryIdAsAttribute: true\n"
        + "    GraphId: 1\n"
        + "    Attributes:\n"
        + "      - AttributeType: UINT\n"
        + "        AttributeName: id\n"
        + "        FixedSize: 0\n"
        + "      - AttributeType: UINT\n"
        + "        AttributeName: id2_deleted\n"
        + "        FixedSize: 0\n"
        + "        Deleted: true\n"
        + "EdgeTypes:\n"
        + "  []";
    GPESchema.deserialize(yamlString);
    GPESchema schema = GPESchema.get();
    assertNotEquals(schema, null);
    // schema has two vertices
    assertEquals(schema.VertexTypes.size(), 2);
    // v0 has two attributes, id and test_deleted
    assertEquals(schema.getVertexType(0).attributeCount(), 2);
    // v1 has one attribute, id (id2 is already deleted)
    assertEquals(schema.getVertexType(1).attributeCount(), 1);
  }

  @Test
  public void testPushDictWithEmptyFolder() {
    SchemaSetup.CreateGraph("person_movie", command_);
    String filepath = Config.sys().getTempRoot() + "/" + Util.NextRandomString(32);

    CommandLine cli = Mockito.mock(CommandLine.class);
    File f = new File(filepath);
    assertFalse(f.exists());

    // test push
    Mockito.when(cli.hasOption("pulldict")).thenReturn(false);
    Mockito.when(cli.hasOption("pushdict")).thenReturn(true);
    Mockito.when(cli.getOptionValue("pushdict"))
        .thenReturn(filepath);
    exceptionRule.expect(ExitException.class);
    exceptionRule.expectMessage("Exit with status 1");
    SpecialOperationRunner.HandleSpecialOperation(cli);
    assertEquals(Arrays.asList("person_movie"), CatalogManager.getAllGraphNames());
  }
}