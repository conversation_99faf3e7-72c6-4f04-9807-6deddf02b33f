package com.tigergraph.schema.operation;

import static com.tigergraph.schema.CatalogManager.QUERY_LIST_CATALOG_FILENAME;
import static com.tigergraph.schema.Util.generateSortedQueryList;

import com.tigergraph.common.JsonAPIVersion;
import com.tigergraph.common.MessageBundle;
import com.tigergraph.common.SyntaxVersion;
import com.tigergraph.engine.codegen.GSQL2UDFCompiler;
import com.tigergraph.engine.codegen.compileFlags;
import com.tigergraph.engine.typechecker.TypeTriplet;
import com.tigergraph.engine.typechecker.VarTypeBase;
import com.tigergraph.gtest.Comparator;
import com.tigergraph.schema.*;
import com.tigergraph.schema.ast.ddl.other.InstallQueryQb;
import com.tigergraph.schema.config.Config;
import com.tigergraph.schema.grpc.ControllerClient;
import com.tigergraph.schema.lock.LockManager;
import com.tigergraph.schema.plan.function.FunctionInfo;
import com.tigergraph.schema.plan.query.Queries;
import com.tigergraph.schema.plan.query.QueryInfo;
import com.tigergraph.schema.plan.query.QuerySignature;
import com.tigergraph.schema.plan.query.install.CompileServerUtil;
import com.tigergraph.schema.plan.query.install.CompileTaskManager;
import com.tigergraph.schema.plan.query.install.CompileTaskManager.CompileGraphStatus;
import com.tigergraph.schema.plan.query.install.CompileTaskManager.CompileTaskList;
import com.tigergraph.schema.security.rbac.privilege.DataObjPrivilege;
import com.tigergraph.schema.security.fileinputoutput.FileInputOutputPolicy;
import com.tigergraph.schema.security.util.PermissionUtil;
import com.tigergraph.schema.topology.GSQLFunctionGraph;
import com.tigergraph.schema.topology.Graph;
import org.apache.commons.collections.CollectionUtils;

import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.locks.Lock;

public class InstallQueryOperation extends MetadataUpdateOperation {
  /**
   * a temporary member that is used to store the query installed state
   * it will be set before the query installation started
   * and reset after all the query installation progress done
   * The reason why not using the data in {@link #backupCatalogMap} is that the installed flag
   *    will be set to false in InstallQuery4SchemaChange
   */
  public static ThreadLocal<Set<String>> INSTALLED_QUERY = new InheritableThreadLocal<>() {
    @Override
    protected Set<String> initialValue() {
      return new HashSet<>();
    }
  };

  private InstallQueryQb installQb;
  // save enable flag of all queries for restore when install fails
  private HashMap<String, Boolean> enableMap;

  // set to true only if needs to upload catalog
  private boolean needUploadCatalog = false;

  // queries that are actually installed
  HashSet<String> enabledSet = new HashSet<>();

  public InstallQueryOperation(Catalog ctlg, InstallQueryQb qb) {
    super(ctlg);
    installQb = qb;
  }

  @Override
  public String getBody() {
    String ret = "INSTALL QUERY";
    if (installQb.o_level != 0) {
      ret += " -O" + installQb.o_level;
    }
    if (installQb.force) {
      ret += " -FORCE";
    }
    // single gpr option
    if (installQb.single) {
      ret += " -SINGLE";
    }
    if (installQb.legacy) {
      ret += " -LEGACY";
    }
    if (installQb.commit) {
      ret += " -UI";
    }
    if (installQb.cost) {
      ret += " -COST";
    }

    // GLE-2334, on DR, only install queries that are installed on primary cluster
    List<String> qNames = new ArrayList<>();
    Queries inMemQueryList = catalog.getInMemoryQueries();
    for (String qName : installQb.QueryNames) {
      QueryInfo qi = inMemQueryList.get(qName);
      if (qi != null && qi.installed) {
        qNames.add(qName);
      }
    }
    if (qNames.isEmpty()) {
      // do nothing if no queries were successfully installed
      return "";
    }

    ret += " " + String.join(", ", qNames);
    return ret;
  }

  @Override
  protected void saveCatalogKeys4Update(Map<String, List<String>> ctlgKeysMap) {
    List<String> keys = new ArrayList<String>();
    keys.add(QUERY_LIST_CATALOG_FILENAME);
    ctlgKeysMap.put(catalog.getName(), keys);
  }

  @Override
  protected boolean preprocess() {
    // Check for usage of deprecated optimize option
    if (installQb.optimize) {
      semanticErrors.add("The optimize flag has been deprecated and is no longer supported.");
      return false;
    }
    return true;
  }

  @Override
  protected ReturnStatus executeInMemory() {
    boolean ret = InstallQueryInternal(
        installQb, installQb.GetEarlyReleaseLocks());
    return getOperationReturnStatus(ret);
  }

  @Override
  protected void handleSuccess() {
    Util.printTestTag(Comparator.Tag.GTEST_IL);
    Util.printlnToStream("Query installation finished.");

    // Print the names of successfully installed queries
    if (!installQb.newlyInstalledQueries.isEmpty()) {
      List<String> sortedQueries = new ArrayList<>(installQb.newlyInstalledQueries);
      Collections.sort(sortedQueries);
      Util.printlnToStream(String.format("The following queries were successfully installed:\n[%s]",
          String.join(", ", sortedQueries)));
    }
  }

  @Override
  protected void handleFail() {
    String str = "Query";
    if (installQb.QueryNames.size() > 1) {
      str = "Queries";
    }
    Util.printlnToStream(str + " installation failed!");
  }

  @Override
  protected boolean persistToDisk(ReturnStatus returnStatus) {
    Util.logTimeElapsed("start upload catalog...", true);
    // Override persistToDisk to handle lock system
    if (!needUploadCatalog && !Config.isDisasterRecoveryCluster()) {
      /**
       * [GLE-5403] For DR cluster, we still need to go through super.persistToDisk
       * in order to increment lastSeqId and keep in sync with Primary cluster.
       */
      Util.LogInfo("skip upload query catalog");
      return true;
    }

    // Install query takes long time, and we do not want to block other operations.
    // So we lock it after query installation done, and before upload to dictionary.
    int lockTimeout = Catalog.GetIntSessionParameter("lock_timeout");
    Util.LogDebug("Try to lock query write lock for graph '%s' with timeout: %s seconds ...",
        catalog.getName(),
        lockTimeout);
    // release the read lock before acquire query write lock
    // use try catch because query install in DSC will only acquire query write lock
    LockManager.releaseLocksWithTryCatch(Arrays.asList(catalog.queryLock.readLock()));
    if (!LockManager.acqurieLock(catalog.queryLock.writeLock(), lockTimeout)) {
      Util.printlnToStream("Failed to lock the query catalog.");
      ResetInstallFlag(installQb, catalog.getInMemoryQueries());
      return false;
    }
    boolean uploadSuccess = false;
    try {
      // store Queries in catalog map for upload
      updateCatalogMap.putAll(catalog.SerializeCatalog(QUERY_LIST_CATALOG_FILENAME));
      // only write query yaml, and query endpoints
      if (installQb.fromGadminBackupRestore && returnStatus == ReturnStatus.SUCCEED_WRITE) {
        // [GLE-4739] when install happens in gadmin backup (restoration) handle updateCatalog
        //            differently to avoid writing new messages into Kafka metadata topic
        uploadSuccess = handleUpdateCatalogInRestore();
      } else {
        uploadSuccess = super.persistToDisk(returnStatus);
      }
    } finally {
      catalog.queryLock.writeLock().unlock();
    }

    if (!uploadSuccess) {
      // TODO: add rollback
      return false;
    }
    Util.logTimeElapsed("#QUERY INSTALLATION# 8: upload catalog finished", false);
    return true;
  }

  protected void postPersistHandler(ReturnStatus returnStatus) {
    Util.logTimeElapsed("start refresh restpp...", true);
    boolean refreshRestppConfSucc = ControllerClient.get(false).reloadRestppCfg();
    Util.LogInfo("Refreshed restpp: " + refreshRestppConfSucc);

    // If not success, inform user
    if (!refreshRestppConfSucc) {
      //installed successfully, but refresh restpp fails
      Util.printlnToStream("Query installed successfully, but refresh restpp fails, "
          + "please manually restart restpp to refresh.");
    }
    Util.logTimeElapsed("#QUERY INSTALLATION# 9: refresh restpp finished", false);
  }

  @Override
  public void postExecuteHandler() {
    INSTALLED_QUERY.get().clear();
  }

  /**
   * Installing a query meens supplying the Queries directly since we are using
   * FunctionInfo->QueryInfo instead of getInMemoryQueries
   * @param allFuncsInPkg holds all gsql functions in the package
   * @return success or failure
   */
  public boolean InstallQueryForFunction(Queries allFuncsInPkg) {
    return InstallQueryInternal(installQb, installQb.GetEarlyReleaseLocks(), allFuncsInPkg,
                                GSQLFunctionGraph.GSQLFUNCTION_GRAPH_NAME);
  }

  /**
   * The entry point to install queries
   * @param qb install query block
   * @param locks early release locks
   * @return true if installation success
   *
   * Detail:
   * 1. generate dispatcher and UDF code
   * 2. generate endpoints
   * 3. compile code
   *
   * [NOTE]:
   * the function cannot run concurrently on same graph, due to the following reason:
   * the {@code qi.pendingInstall} flag will affect the code gen in query dispatcher,
   * which may lead undefined symbol to all_query_xxx.
   *
   * But concurrent install on different graph is allowed
   */
  private boolean InstallQueryInternal(InstallQueryQb qb, List<Lock> earlyReleaseLocks) {
    return InstallQueryInternal(qb, earlyReleaseLocks, catalog.getInMemoryQueries(),
        catalog.getName());
  }

  private boolean InstallQueryInternal(
      InstallQueryQb qb, List<Lock> earlyReleaseLocks, Queries inMemQueryList, String graphName) {
    Util.LogInfo("Install %s '%s' in graph '%s', force: %s, fromSchemaChange: %s,"
               + " fromGadminBackupRestore: %s",
        (inMemQueryList.isFunction ? "function" : "query"),
        qb.QueryNames.toString(),
        graphName,
        qb.force,
        qb.fromSchemaChange,
        qb.fromGadminBackupRestore);

    enableMap = inMemQueryList.getEnableFlagMap();

    /***************************** 1. Simple checks ****************************/
    // need shutdown executor
    boolean startExecutor = false;

    try {
      boolean reinstallAll = false;
      Set<Integer> libudfIdSet = new HashSet<>();
      // some queries (decprecate, or not enabled) are skipped when using "install query all"
      Set<String> skippedSet = new HashSet<>();
      boolean allInstalled = true;
      // If query name contains only one *, replace it with all query names
      if (qb.QueryNames.size() == 1 && qb.QueryNames.get(0).equals("*")) {
        qb.QueryNames.clear();
        for (QueryInfo qi : inMemQueryList) {
          // ignore all draft queries while running "install query *"
          if (qi.isDraftQuery()) {
            qb.draftQueries.add(qi.QueryName);
            continue;
          }
          qb.QueryNames.add(qi.QueryName);
        }
        reinstallAll = true;
      }

      /*
       * NOTE: have to use a separate for loop, otherwise the check of enabled
       * flag is not the latest value.
       * enable main query if all recursive subquery will be installed or enabled
       */
      final HashSet<String> toBeInstalledSet = new HashSet<>(qb.QueryNames);
      for (String qname : qb.QueryNames) {
        inMemQueryList.enableCallerQueriesRecursively(
            inMemQueryList.get(qname), enabledSet, toBeInstalledSet);
      }
      // only implicitly install query which has been installed before
      for (Map.Entry<String, QueryInfo> entry : inMemQueryList.getQueryMap().entrySet()) {
        if (!entry.getValue().installed) {
          enabledSet.remove(entry.getKey());
        }
      }
      Util.LogInfo("Implicitly install the enabled queries: " + enabledSet);

      Map<String, String> sessionParameters =
          SessionManager.getCurrentSession().getSessionParameters();
      for (String qname : qb.QueryNames) {
        //1. Semantic check
        QueryInfo qi = inMemQueryList.get(qname);

        //1.1 query not exists
        if (qi == null) {
          Util.SemanticErrorMsg("The query " + qname + " cannot be found!");
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        // 1.2 query is draft status
        if (qi.isDraftQuery()) {
          skippedSet.add(qi.QueryName);
          String msg = "The status of query " + qname + " is DRAFT, skip.";
          Util.LogInfo(msg);
          // specified the queryName to be installed, report the error message
          if (!reinstallAll) {
            Util.printlnToStream(msg);
          }
          qb.draftQueries.add(qname);
          continue;
        }

        // 1.3 query is deprecated
        boolean skipDeprecate = qi.deprecate && reinstallAll && !qb.force;
        if (skipDeprecate) {
          skippedSet.add(qi.QueryName);
          Util.LogInfo(qi.QueryName + " is deprecated, skip.");
          continue;
        }

        // 1.4 query is not enabled
        if (!qi.enabled) {
          if (reinstallAll) {
            // ignore queries not enabled for case "install query all"
            skippedSet.add(qi.QueryName);
            Util.LogInfo(qi.QueryName + " is not enabled, skip.");
            continue;
          } else {
            // the query must be enabled before install
            inMemQueryList.semanticCheckSubQueriesRecursively(
                qi, new HashSet<>(qb.QueryNames));
            ResetInstallFlag(qb, inMemQueryList);
            return false;
          }
        }

        //1.5 query is installed?
        if (!qi.installed) {
          allInstalled = false;
        } else {
          // INSTALLED_QUERY will be cleared once the whole operation finished.
          INSTALLED_QUERY.get().add(qi.QueryName);
        }

        //1.6 graph still exists?
        if (qi.GraphName != null
            && !GSQLFunctionGraph.isFuncGraph(qi.GraphName)
            && !catalog.getInMemoryGraphs().contains(qi.GraphName)) {
          Util.SemanticErrorMsg(MessageBundle.get("graph.wrong", qi.GraphName, graphName));
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        //1.7 check GDKDir
        Path gdkDir = Config.getGdkDir();
        if (gdkDir == null || !Files.exists(gdkDir)) {
          Util.printlnToStream("Invalid GDKDir: " + gdkDir);
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        /*
         * install query is triggered implicitly when doing schema change,
         * don't check extra permission if from schema change job
         */
        if (!qb.skipPermissionCheck) {
          // permission check
          if (!PermissionUtil.checkPermission(CatalogManager.GetCurrentUser(),
                  catalog.getName(), qb.getRequiredPermissions())) {
            return false;
          }

          // add tag-permission check here
          if (PermissionUtil.checkPermissionTagAccess(qi, catalog)) {
            return false;
          }
        }

        //1.8 check calling relation
        Set<String> uninstalledSet = new HashSet<String>();
        Set<String> uiModeSubQuerySet = new HashSet<String>();
        Set<String> uninstalledFunctionSet = new HashSet<String>();
        for (String queryName : qi.subQueries.keySet()) {
          QueryInfo subQi = inMemQueryList.get(queryName);
          if (subQi == null) {
            uninstalledSet.add(queryName);
            // only skips this iteration
            continue;
          }

          // check if sub-query is enabled and installed/going to install
          // Notice if a query is deprecate and this is "install query all", then the
          // deprecate query will be skiped. See above "1.2 query is deprecated"
          if (!subQi.enabled
              || (!subQi.installed && !qb.QueryNames.contains(subQi.QueryName))
              || skipDeprecate) {
            // 1. the sub-query is disabled
            // 2. the sub-query is not installed or deprecated, and not going to be installed
            uninstalledSet.add(subQi.QueryName);
          }

          // check sub-query update immediately mode
          if (subQi.commit
              || qb.QueryNames.contains(subQi.QueryName) && qb.commit) {
            uiModeSubQuerySet.add(subQi.QueryName);
          }
        }

        if (uninstalledSet.size() != 0) {
          String msg = String.format(
              "The following queries called in '%s' are not installed yet: %s. "
                  + "Please install them in advance or together.",
              qname, uninstalledSet);
          Util.SemanticErrorMsg(msg);
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        if (uiModeSubQuerySet.size() != 0) {
          String queryStr = uiModeSubQuerySet.size() == 1 ? "query" : "queries";
          String beStr = uiModeSubQuerySet.size() == 1 ? "is" : "are";
          Util.SemanticErrorMsg(
              "The " + queryStr + " "
                  + uiModeSubQuerySet + " " + beStr
                  + " installed with -ui option, which is not allowed to be called in query '"
                  + qname
                  + "'.");
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        // Check if direct called GSQL function in this query are installed
        Set<FunctionInfo> directcalleeFunctions = qi.getDirectCallGSQLFunctionInfos();
        for (FunctionInfo fi : directcalleeFunctions) {
          if (!fi.installed) {
            uninstalledFunctionSet.add(fi.getFullFunctionPath());
          }
        }

        if (uninstalledFunctionSet.size() != 0) {
          Util.SemanticErrorMsg(
              "The function:"
                  + Util.asSortedList(uninstalledFunctionSet)
                  + " called in '"
                  + qname
                  + "' is not installed yet. Please install it in advance '"
                  + qname
                  + "'.");
          ResetInstallFlag(qb, inMemQueryList);
          return false;
        }

        //2. mark pending installed, clear failed compilation flag
        qi.pendingInstall = true;
        qi.failedCompilation = false;

        //3. mark optimization level
        qi.o_level = qb.o_level;

        //4. set commit flag
        if (!reinstallAll || qb.commit) {
          qi.commit = qb.commit;
        }

        // 5. single mode
        qi.singleMode = qb.single || Boolean.parseBoolean(sessionParameters.get("single_gpr"));

        // 6. cost_opt : linear cost optimization
        if (qb.cost) {
          qi.setCostOpt();
        }
        // currently hidden from user
        qi.legacyMode = qb.legacy;
      }
      // change qi in the loop is not enough, since it is in-memory only and will not be sent
      // to compile server, need to use session parameters to pass by
      if (qb.debug) {
        sessionParameters.put("debug", "true");
      }

      // unless install query with '-force' option,
      // report error when all queries have installed
      // INSTALL QUERY xxx -FORCE
      if (allInstalled && !qb.force) {

        //If no query for current graph
        if (qb.QueryNames != null && qb.QueryNames.size() == 0) {
          String tmp = "Graph " + graphName;
          Util.SemanticErrorMsg(tmp + ": there is no query in this graph to be installed");
          return true;
        }

        String str = "Graph " + graphName;
        str += ": all queries in this catalog have been installed already.";
        Util.SemanticErrorMsg(str);
        ResetInstallFlagForQueriesInInstallQb(qb, inMemQueryList);
        return true;
      }

      if (inMemQueryList.isEmpty()) {
        Util.LogInfo("No any queries in the graph %s", graphName);
        return true;
      }

      /***************************** 2. Query transform/compile ****************************/
      // generate code and compile to .o file
      if (inMemQueryList.isFunction) {
        Util.printlnToStream(String.format("Start installing functions for package '%s' ...",
            inMemQueryList.pkgs.replaceAll("-", ".")));
      } else {
        Util.printlnToStream("Start installing queries, about 1 minute ...");
      }

      boolean ret = false;
      // add interrupt point
      if (Thread.interrupted()) {
        Util.LogInfo("[Interrupted] thread: " + Thread.currentThread() + " is interrupted.");
        return false;
      }
      // unify the compiling on single node and cluster
      // set its compile leader to itself
      CompileServerUtil.setMyLeader(Util.getReplicaNum());
      // prepare progress bar
      ProgressBar pb = new ProgressBar("[:bar] :percent% (:done/:total)");
      // don't print progressbar if it's from SchemaChangeHandler
      if (CompileServerUtil.isFromSchemaChangeHandler()) {
        Util.LogDebug("Hide progressbar for SchemaChangeHandler");
        pb.setHidden();
      }
      // start Timer to profile installation time cost
      Util.logTimeElapsed("#QUERY INSTALLATION# 0: Code generation started for graph "
          + graphName, true);

      Util.LogInfo("Query code generation started for graph '%s'", graphName);

      // initialize files before compile
      Graph graph;
      if (inMemQueryList.isFunction) {
        graph = GSQLFunctionGraph.get();
        Queries.initialSettingForQueryAndFunctionCompile(
            graph, true, inMemQueryList.pkgs);
      } else {
        graph = catalog.getInMemoryGraphs().get(0);
        Queries.initialSettingForQueryAndFunctionCompile(graph, true, "");
      }

      // prepare varTypeBase, must after UDF files are updated in initialSettingForQueryCompile
      VarTypeBase varTypeBase
          = inMemQueryList.isFunction ? null : GSQL2UDFCompiler.initVarTypeBase(graph);
      Util.LogInfo("Prepare global UDT, Accum, and functions done");

      // GLE-2801, if a query failed in query object compilation,
      // need to uninstall its caller queries.
      Set<String> uninstallQueries = new HashSet<>();
      try {
        startExecutor = true;
        /*
         * I. Generate Cpp code
         *   Semantic check, find all queries that needs compile and gen cpp code for them
         */
        // if it's from schema change, then do not force re-compile all query objects.
        boolean forceRecompile = qb.force && !qb.fromSchemaChange;
        CompileTaskList needCompileList = inMemQueryList.checkQueryAndGenCpp(
            catalog, varTypeBase, forceRecompile, libudfIdSet);
        Util.logTimeElapsed("#QUERY INSTALLATION# 1: Code generation finished for graph "
            + graphName, false);
        if (needCompileList == null) {
          Util.LogError("Null needCompileList!");
          return false;
        }


        String additionalPath = "";
        if (inMemQueryList.isFunction) {
          // Functions have additional file paths due to packages. Queries may use this in the
          // future as well but for now it is just functions
          additionalPath = GSQLFunctionGraph.getPackageNamesDir(inMemQueryList.pkgs);
        }

        // initialize the compile leader for query install of the current graph
        CompileServerUtil.initializeCompileLeader(
            graphName, inMemQueryList, needCompileList);

        if (needCompileList.isEmpty()) {
          // no tasks to compile, skip to linking
          String str = String.format("Graph %s: no queries in this catalog need to be re-compiled."
              + " Skip to linking.",
              graphName);
          Util.printTestTag(Comparator.Tag.GTEST_IL);
          Util.printlnToStream(str);
          // prepare the leader node as a linker
          ret = CompileServerUtil.get().prepareLeaderForLinking();
        } else {
          // There are some compile tasks that need compiling
          /*
           * II. Prepare stage:
           *     single: initialize node status for itself
           *     cluster: designate compile server and collect MAX_TASK for each node,
           *              initialize node status for all of them
           */
          ret = CompileServerUtil.get().prepareCompileServers(graphName, additionalPath);
          Util.logTimeElapsed("#QUERY INSTALLATION# 2: Prepare compile servers finished for graph "
                  + graphName,
              false);
          if (!ret) {
            ResetInstallFlag(qb, inMemQueryList);
            return false;
          }

          /*
           * III. release the all locks except global read since all cpp code are generate
           * TODO: consider cases that user drop something (tuple, vertex, edge) after this
           */
          LockManager.releaseLocks(earlyReleaseLocks);
          Util.logTimeElapsed("#QUERY INSTALLATION# 3: Early release locks finished", false);

          /*
           * IV: Assign tasks stage -- for both single-node and cluster
           *     - single: run compile tasks on itself
           *     - cluster: dynamically assign compiling tasks to compile servers
           *     Then run a "checking" loop to print progress, assign more tasks, check node status
           */
          ret = false;
          ret = CompileServerUtil.get().assignCompileTasks(graphName, pb, uninstallQueries);

          Util.logTimeElapsed("#QUERY INSTALLATION# 4: Compile object files finished for graph "
              + graphName, false);
          Util.LogInfo("Query compiling object files for graph '%s' finished with code: %s",
              graphName,
              ret);
        }
        // end of compiling of .o files
      } catch (Exception e) {
        Util.LogExceptions(e);
        ResetInstallFlag(qb, inMemQueryList);
        pb.abort();
        // Notice: the thrown exception may be suppressed since there is return in "finally"
        throw e;
      } finally {
        if (!ret) {
          ResetInstallFlag(qb, inMemQueryList);
          pb.abort();
          Util.printTestTag(Comparator.Tag.GTEST_IL);
          Util.printlnToStream("Query installation failed during compiling!");
          return false;
        }
      }

      /***************************** 3. Linking ****************************/
      if (!qb.force && !CompileServerUtil.get().needsLinking(graphName)) {
        // GLE-2143: do NOT need to run linking if meeting these conditions:
        // 1. no queries or schemaIndex files are re-compiled
        // 2. it's not "INSTALL QUERY -FORCE xxx"
        ResetInstallFlag(qb, inMemQueryList);
        Util.LogInfo("No queries or schemaIndex succeeded compile, skip linking.");
        return false;
      }

      // Linking, combine all .o files to .so file
      try {
        ret = false;
        String sessionId = SessionManager.getCurrentSession().getSessionId();
        List<CompileTaskManager.CompileTask> linkTasks = new ArrayList<>();

        if (inMemQueryList.isFunction) {
          // TODO: refactor QueryInfo into ObjectInfo -> Query/Func info and we will not need this
          // TODO:RBAC check what fields of current in-memory QueryInfo we want to keep in
          // FunctionInfo generated QueryInfo.
          CompileTaskManager.CompileTask linkTask =
              new CompileTaskManager.CompileTask(
                  graphName, sessionId, inMemQueryList.pkgs, null);
          linkTask.libudfId = 0;
          linkTask.type = CompileTaskManager.CompileTask.Type.LINK;
          linkTasks.add(linkTask);
        } else {
          // if the libudfMap corresponding to libudfId is empty, then skip link this libudf.
          catalog.getInMemoryQueries().libudfMap.forEach((k, v) -> {
            if (CollectionUtils.isEmpty(v)) {
              libudfIdSet.remove(k);
            }
          });
          Util.LogDebug("Start link libudf %s for graph %s.", libudfIdSet, graphName);
          for (int libudfId : libudfIdSet) {
            CompileTaskManager.CompileTask linkTask =
                new CompileTaskManager.CompileTask(
                    graphName, sessionId, String.valueOf(libudfId), null);
            linkTask.libudfId = libudfId;
            linkTask.type = CompileTaskManager.CompileTask.Type.LINK;
            linkTasks.add(linkTask);
          }
        }
        // initialize the compile leader for query install of the current graph
        CompileTaskManager.get().initializeGraphLink(graphName, linkTasks);

        ret = CompileServerUtil.get().assignLinkTask(graphName, inMemQueryList);
        Util.LogInfo("Query code linking for graph '%s' finished with code: %s",
            graphName, ret);
      } catch (Exception e) {
        Util.LogExceptions(e);
        ResetInstallFlag(qb, inMemQueryList);
        pb.abort();
        // Notice: the thrown exception may be suppressed since there is return in "finally"
        throw e;
      } finally {
        // finish the progress bar printing
        if (ret) {
          pb.finish();
          // show failed queries if there is any
          CompileServerUtil.showFailedQueries(graphName);
          // if a function is failed to compile, we should not return true
          // otherwise, it will return the schema change succeeded
          // while the function is not installed
          if (inMemQueryList.isFunction) {
            CompileGraphStatus graphStatus = CompileTaskManager.get(graphName);
            if (graphStatus != null && graphStatus.getFailedQueries().size() != 0) {
              return false;
            }
          }
          // GLE-2801, show uninstalled queries due to fail compilation of its sub-queries
          if (!uninstallQueries.isEmpty()) {
            Util.printlnToStream(String.format(
                "Caller queries of these failed queries are uninstalled:\n"
                + "[%s]",
                String.join(", ", Util.asSortedList(uninstallQueries))));
          }
          // show node contributions
          CompileServerUtil.showNodeContribution(graphName);
        } else {
          // not installed successfully
          ResetInstallFlag(qb, inMemQueryList);
          pb.abort();
          Util.printTestTag(Comparator.Tag.GTEST_IL);
          Util.printlnToStream("Query installation failed during linking!");
          return false;
        }
      }

      // update query status flag of all installed queries and enabled queries
      enabledSet.addAll(qb.QueryNames);
      Set<String> newInstalledQueries = new HashSet<>();
      for (String qname : enabledSet) {
        QueryInfo qi = inMemQueryList.get(qname);
        if (qi != null) {
          // when query is deprecated or not enabled, and use "install query all"
          if (skippedSet.contains(qi.QueryName)) {
            continue;
          }
          // update QueryInfo of the caller queries for the recently installed query
          if (!qi.installed && !qi.failedCompilation) {
            newInstalledQueries.add(qi.QueryName);
          }
          // if the query used to be deprecate, and failed in compilation, then it's still deprecate
          qi.deprecate = (qi.deprecate && qi.failedCompilation) ? true : false;
          qi.installed = !qi.failedCompilation;
          qi.enabled = qi.installed;
          qi.pendingInstall = false;
          // collect query names for the queries that didn't get installed successfully (GLE-2633)
          if (!qi.installed) {
            deleteCatalogKeys.add(qi.getEndPointFileName());
          }
        }
      }
      qb.newlyInstalledQueries = new HashSet<>(newInstalledQueries);

      if (!inMemQueryList.isFunction) {
        // update affected caller queries' query info in memory query list
        for (String qname : generateSortedQueryList(catalog, newInstalledQueries)) {
          QueryInfo qi = catalog.getInMemoryQueries().get(qname);
          // don't update query info if caller query is a draft query
          if (qi != null && !qi.isDraftQuery()) {
            updateQueryInfo(qname, inMemQueryList, varTypeBase);
          }
        }

        // persist catalogs with updated query endpoints into DICT
        for (String qname : enabledSet) {
          QueryInfo qi = inMemQueryList.get(qname);
          // if a query in enabledSet that is 1) not null AND 2) not skipped AND 3)installed
          // then this query should be put into updateCatalogMap
          if (qi != null
              && !skippedSet.contains(qi.QueryName)
              && qi.installed) {
            OperationUtil.getQueryAndCallersEndpoints(qi, updateCatalogMap);
          }
        }
      }

      /**
       * Avoid the issue like this:
       *   1. query q1 calls q2
       *   2. install query q1, q2
       *   3. q1 failed during compilation, but q2 succeeded
       *   4. q1 endpoint is in both {@code updateCatalogMap} and {@code deleteCatalogKeys}
       */
      for (String queryEnd : deleteCatalogKeys) {
        updateCatalogMap.remove(queryEnd);
      }
      Util.logTimeElapsed("#QUERY INSTALLATION# 7: upload query endpoints done for graph "
          + graphName, false);
    } catch (Exception e) {
      Util.LogExceptions(e);
      return false;
    } finally {
      // remove timer
      Util.TIMER.remove();
      if (startExecutor) {
        // shutdown executor after all tasks done
        CompileServerUtil.get().requestShutDownExecutor(graphName);
        // clear the compiling status for the graph
        CompileServerUtil.clearCompileStatus(graphName);
      }
      // remove session parameter debug
      SessionManager.getCurrentSession()
          .getSessionParameters().remove("debug");
    }
    // only upload catalog when runs to here
    needUploadCatalog = true;
    Util.LogInfo(
        "Install query '%s' in graph '%s' done in memory.",
        qb.QueryNames.toString(),
        graphName);
    return true;
  }

  /**
   * reset QueryInfo install flag when installation failed.
   */
  private void ResetInstallFlag(InstallQueryQb qb,
                                Queries inMemQueryList) {
    ResetInstallFlagForQueriesInInstallQb(qb, inMemQueryList);

    // restore enable flag
    for (QueryInfo qi : inMemQueryList) {
      qi.enabled = enableMap.get(qi.QueryName);
    }
  }

  /**
   * Update a list of queries' query info.
   * such as update query's data lists in order
   *         update query's needCurrentRoles field
   * @param queryList queries need to update
   * @param inMemQueryList query list saved in memory
   */
  private void updateQueryInfo(List<String> queryList, Queries inMemQueryList,
      VarTypeBase varTypeBase) {
    if (queryList == null || queryList.size() == 0) {
      return;
    }

    Graph graph = catalog.getInMemoryGraphs().get(0);
    // generates graph triplets, shared by all queries
    var triplets = TypeTriplet.RegisterAllTriplet(graph);

    for (String query : queryList) {
      QueryInfo qi = inMemQueryList.get(query);
      // only update enabled queries, disabled ones may have syntax errors
      if (qi.enabled) {
        // regenerate query signature for this query to get the data lists in this query
        int offset = qi.FileLineOffset;
        String queryText = qi.getRealQueryText();
        boolean commit = qi.commit;
        JsonAPIVersion api = qi.ApiVersion;
        // Set syntax version to the value of session parameter if query is null
        SyntaxVersion syntaxVersion = qi.SyntaxV;

        StringBuilder udfCpp = new StringBuilder();

        EnumSet<compileFlags> flags = EnumSet.of(compileFlags.SINGLE_MODE);

        if (commit) {
          flags.add(compileFlags.COMMIT_UPDATE_IN_PLACE);
        }

        QuerySignature qs = new QuerySignature();
        boolean compiledResult = GSQL2UDFCompiler.compile(
            catalog,
            queryText,
            udfCpp,
            offset,
            api,
            syntaxVersion,
            flags,
            null,
            triplets,
            SessionManager.getCurrentSession().getSessionParameters(),
            FileInputOutputPolicy.getOutputPolicy(), qs,
            false,
            varTypeBase);
        if (!compiledResult) {
          Util.LogError("Failed to parse query to update data lists");
          return;
        }

        // update query catalog and endpoint from extracted query signature.
        qi.updateInfoAndEndpointFromQuerySignature(qs);
      }
    }
  }

  /**
   * Wrapper function to update one query's query info
   */
  private void updateQueryInfo(String queryName, Queries inMemQueryList, VarTypeBase varTypeBase) {
    List<String> queryList = Arrays.asList(queryName);
    updateQueryInfo(queryList, inMemQueryList, varTypeBase);
  }

  /**
   * reset QueryInfo install flag when installation failed.
   */
  private void ResetInstallFlagForQueriesInInstallQb(InstallQueryQb qb, Queries inMemQueryList) {
    for (String qname : qb.QueryNames) {
      QueryInfo qi = inMemQueryList.get(qname);
      if (qi != null) {
        qi.o_level = 0;
        qi.pendingInstall = false;
      }
    }
  }

  /**
   * helper function to handle updateCatalog in restoration
   * [GLE-4739] Avoid write to Kafka metadata topic when a cluster reinstall queries during its
   *            restoration by avoid using ReplicationManager's updateCatalog method.
   */
  private boolean handleUpdateCatalogInRestore() {
    List<String> deleteKeys = Util.asSortedList(deleteCatalogKeys);
    Util.LogDebug("%s, deleteKeys: %s", operationName, deleteKeys);
    Util.LogDebug("%s, updateKeys: %s", operationName,
        Util.asSortedList(updateCatalogMap.keySet()));
    return CatalogManager.adminServer.updateCatalog(updateCatalogMap, deleteKeys);
  }

  /**
   * judge if the installation succeeds.
   *
   * @return ReturnStatus.SUCCEED_WRITE if one of the installed queries succeeds or none installed
   */
  private ReturnStatus getOperationReturnStatus(boolean ret) {
    if (!ret) {
      return ReturnStatus.FAILED;
    }
    if (enabledSet.isEmpty()) {
      return ReturnStatus.SUCCEED_WRITE;
    }
    for (String queryName : enabledSet) {
      QueryInfo qi = catalog.getInMemoryQueries().get(queryName);
      if (qi != null && qi.installed) {
        return ReturnStatus.SUCCEED_WRITE;
      }
    }
    return ReturnStatus.FAILED;
  }

}
