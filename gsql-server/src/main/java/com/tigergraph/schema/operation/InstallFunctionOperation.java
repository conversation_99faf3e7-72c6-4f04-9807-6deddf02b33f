package com.tigergraph.schema.operation;

import static com.tigergraph.schema.CatalogManager.PACKAGE_CATALOG_FILENAME;

import com.tigergraph.schema.Catalog;
import com.tigergraph.schema.CatalogManager;
import com.tigergraph.schema.Util;
import com.tigergraph.schema.ast.ddl.other.InstallQueryQb;
import com.tigergraph.schema.config.Config;
import com.tigergraph.schema.plan.function.FunctionInfo;
import com.tigergraph.schema.plan.packages.Packages;
import com.tigergraph.schema.plan.query.Queries;
import com.tigergraph.schema.security.rbac.permission.PermissionTrees;
import com.tigergraph.schema.security.rbac.privilege.FunctionObjPrivilege;
import com.tigergraph.schema.security.util.PermissionUtil;
import com.tigergraph.schema.topology.CatalogObject;
import com.tigergraph.schema.topology.GSQLFunctionGraph;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

public class InstallFunctionOperation extends MetadataUpdateOperation {
  /*
   * list of string for package and function names.
   * The last name can be specific function name, or 
   * eg:
   * install function lib1.lib2.func1 -> [lib1, lib2], [func1]
   * install function lib1.lib2.*     -> [lib1, lib2], [*]
   */
  private List<String> pkgNameList;
  private List<String> funcNameList;

  private boolean force = false;
  private boolean debug = false;

  private boolean skipPermissionCheck = false;

  // intermediate object, that contains functions that will be actually compiled.
  private List<FunctionInfo> funcInfoList4Compile;

  public InstallFunctionOperation(Catalog ctlg, List<String> pkgName, List<String> funcNames,
      boolean force, boolean debug) {
    super(ctlg);
    this.pkgNameList = pkgName;
    this.funcNameList = funcNames;
    this.force = force;
    this.debug = debug;

    // for DR cluster always skip permission check since it's replayed from Primary cluster.
    this.skipPermissionCheck = Config.isDisasterRecoveryCluster();

    this.funcInfoList4Compile = new ArrayList<>();
  }

  public void setSkipPermissionCheck() {
    skipPermissionCheck = true;
  }

  @Override
  public String getBody() {
    String ret = "INSTALL FUNCTION ";
    if (this.force) {
      ret += "-FORCE ";
    }
    if (this.debug) {
      ret += "-DEBUG ";
    }
    String pkgName = String.join(".", this.pkgNameList);
    boolean isFirst = true;
    for (String funcName : this.funcNameList) {
      if (!isFirst) {
        ret += ", ";
      }
      ret += (pkgName + "." + funcName);
      isFirst = false;
    }
    return ret;
  }

  @Override
  protected boolean preprocess() {

    // i. verify package
    Packages inMemPackages = globalCatalog.getInMemoryPackages();
    if (!inMemPackages.contains(pkgNameList)) {
      semanticErrors.add(String.format(
          "The package '%s' doesn't exist.",
          String.join(".", pkgNameList)));
      return false;
    }
    Packages pkg = inMemPackages.get(pkgNameList);

    // ii. verify function
    boolean funcVerifyPass = true;
    for (String funcName : this.funcNameList) {
      if ("*".equals(funcName)) {
        // a. all functions in direct level
        addAllDirectFuncsInPackage(pkg, this.force);
      } else  {
        // b. individual function name
        if (!pkg.containsObject(funcName)
            || !(pkg.getObject(funcName) instanceof FunctionInfo)) {
          semanticErrors.add(String.format(
              "The function '%s' doesn't exist in package '%s'.",
              funcName, String.join(".", pkg.getFullPackageName())));
          funcVerifyPass = false;
          continue;
        }
        // add to map
        FunctionInfo fObj = (FunctionInfo) pkg.getObject(funcName);
        addSingleFunctionInPackage(pkg, fObj, this.force);
      }
    }
    if (!funcVerifyPass) {
      return false;
    }

    // iii. check permission on Function
    if (!skipPermissionCheck && !funcInfoList4Compile.isEmpty()) {
      List<String> fullPath = new ArrayList<>(this.pkgNameList);
      // placeholder for function name
      fullPath.add("");
      String[] pathArray = fullPath.toArray(new String[]{});
      int funcIndex = pathArray.length - 1;

      PermissionTrees requiredTrees = new PermissionTrees();
      for (FunctionInfo fi : funcInfoList4Compile) {
        pathArray[funcIndex] = fi.FunctionName;
        requiredTrees.addPrivilege(FunctionObjPrivilege.USE, pathArray);
      }
      StringJoiner errMsg = new StringJoiner("\n");
      if (!PermissionUtil.checkPermission(CatalogManager.GetCurrentUser(), null, requiredTrees,
          false, errMsg)) {
        permissionErrors.add(errMsg.toString());
        return false;
      }
    }

    return true;
  }

  @Override
  protected void saveCatalogKeys4Update(Map<String, List<String>> ctlgKeysMap) {
    List<String> keys = new ArrayList<String>();
    keys.add(PACKAGE_CATALOG_FILENAME);
    ctlgKeysMap.put(catalog.getName(), keys);
  }

  @Override
  protected ReturnStatus executeInMemory() {
    if (funcInfoList4Compile.isEmpty()) {
      Util.printlnToStream(String.format(
          "No functions need to be installed for package '%s'.",
          String.join(".", pkgNameList)));
      return ReturnStatus.SUCCEED_WRITE;
    }

    Queries funcQ = new Queries();
    for (FunctionInfo funcInfo : funcInfoList4Compile) {
      funcQ.addFunctionInfoToQueries(funcInfo);
    }
    boolean success = installFunctionInternal(funcQ.getAllQueryNames());
    return success ? ReturnStatus.SUCCEED_WRITE : ReturnStatus.FAILED;
  }

  @Override
  protected void handleSuccess() {
    Util.printlnToStream(String.format(
        "Function installation finished for package '%s'.",
        String.join(".", this.pkgNameList)));

    // Print the names of successfully installed functions
    if (!funcInfoList4Compile.isEmpty()) {
      List<String> installedFunctions = new ArrayList<>();
      for (FunctionInfo fi : funcInfoList4Compile) {
        if (fi.installed) {
          installedFunctions.add(fi.FunctionName);
        }
      }
      if (!installedFunctions.isEmpty()) {
        Collections.sort(installedFunctions);
        Util.printlnToStream(String.format("The following functions were successfully installed:\n[%s]",
            String.join(", ", installedFunctions)));
      }
    }
  }

  @Override
  protected void handleFail() {
    Util.printlnToStream(String.format(
        "Function installation failed for package '%s'!",
        String.join(".", this.pkgNameList)));
  }

  protected void postPersistHandler(ReturnStatus returnStatus) {
  }

  /**
   * Helper function to add a single function {@code fObj} in the given {@code pkg}.
   * If the function is already installed, and not with '-FORCE', the function will be
   * skipped.
   */
  private void addSingleFunctionInPackage(Packages pkg, FunctionInfo fObj, boolean force) {
    if (force || !fObj.installed) {
      funcInfoList4Compile.add(fObj);
    }
  }

  /**
   * Helper function to add all direct functions in the given {@code pkg}
   * @param pkg
   * @param force if true, recompile even if it's already installed.
   */
  private void addAllDirectFuncsInPackage(Packages pkg, boolean force) {
    Map<String, CatalogObject> objectMap = pkg.getObjectMap();
    for (String objName : Util.asSortedList(objectMap.keySet())) {
      CatalogObject obj = objectMap.get(objName);
      if (obj instanceof FunctionInfo) {
        FunctionInfo fObj = (FunctionInfo) obj;
        addSingleFunctionInPackage(pkg, fObj, force);
      }
    }
  }

  /**
   * GSQL Function Install entry point. Will install and update the catalog package with the
   * specified functions
   * <p>
   * *IMPORTANT*
   * </p>
   * <p>
   * Caller is responsible for holding the necessary locks on:
   * TODO: Make sure we know the full list here ( PACKAGES )
   * </p>
   * @param funcQ collection of functions represented as Queries
   * @return success or failure
   */
  private boolean installFunctionInternal(List<String> funcQNames) {
    boolean success;
    // Setup InstallQueryQb
    InstallQueryQb iqb = new InstallQueryQb();
    iqb.single = true;
    iqb.force = this.force;
    iqb.debug = this.debug;
    // skip QUERY permission check
    iqb.skipPermissionCheck = true;
    iqb.QueryNames = funcQNames;

    // Generate the compiled function
    InstallQueryOperation iqo = new InstallQueryOperation(CatalogManager.getGlobalCatalog(), iqb);
    // collect existing installed GSQL functions in its QueryMap
    // for the linking `libpkgudf_*.so` file.
    Queries allFuncsInPkg = GSQLFunctionGraph.getAllFuncFromPackages(this.pkgNameList);
    success = iqo.InstallQueryForFunction(allFuncsInPkg);

    // Need to update the FunctionInfos for these functions in the package manually,
    // no matter the install process is success or not.
    FunctionInfo.updateFunctionInfo(this.pkgNameList, allFuncsInPkg);

    return success;
  }
}
