[GTEST_BEGIN]
Using graph 'poc_graph'
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "pAttr": "nCount",
      "\"qGetAttrWhereSrc()\"": "qGetAttrWhereSrc()"
    },
    {"res": [
      {
        "v_id": "c3",
        "attributes": {
          "company_name": "com3",
          "nCount": 1,
          "id": "c3",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c1",
        "attributes": {
          "company_name": "com1",
          "nCount": 1,
          "id": "c1",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c5",
        "attributes": {
          "company_name": "com5",
          "nCount": 1,
          "id": "c5",
          "label": true
        },
        "v_type": "company"
      }
    ]},
    {"res": [
      {
        "v_id": "c3",
        "attributes": {
          "company_name": "com3",
          "nCount": 1,
          "id": "c3",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c1",
        "attributes": {
          "company_name": "com1",
          "nCount": 1,
          "id": "c1",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c5",
        "attributes": {
          "company_name": "com5",
          "nCount": 1,
          "id": "c5",
          "label": true
        },
        "v_type": "company"
      }
    ]},
    {"res": [
      {
        "v_id": "c3",
        "attributes": {
          "company_name": "com3",
          "nCount": 1,
          "id": "c3",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c1",
        "attributes": {
          "company_name": "com1",
          "nCount": 1,
          "id": "c1",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c5",
        "attributes": {
          "company_name": "com5",
          "nCount": 1,
          "id": "c5",
          "label": true
        },
        "v_type": "company"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrWhereSrc (TYP-172): line 422, col 10
Attribute 'nCoun' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "\"qGetAttrWhereTgt()\"": "qGetAttrWhereTgt()",
      "pAttr": "company_name"
    },
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m8",
        "attributes": {
          "registrationDate": 7,
          "id": "m8",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m10",
        "attributes": {
          "registrationDate": 9,
          "id": "m10",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      }
    ]},
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m8",
        "attributes": {
          "registrationDate": 7,
          "id": "m8",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m10",
        "attributes": {
          "registrationDate": 9,
          "id": "m10",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      }
    ]},
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m8",
        "attributes": {
          "registrationDate": 7,
          "id": "m8",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m10",
        "attributes": {
          "registrationDate": 9,
          "id": "m10",
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrWhereTgt (TYP-172): line 446, col 10
Attribute 'company_nam' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrWhereInternal (TYP-172): line 469, col 10
Attribute 'gsql_sys_tag' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "\"qGetAttrWhereEdge()\"": "qGetAttrWhereEdge()",
      "pAttr": "startTime"
    },
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]},
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]},
    {"res": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "id": "m6",
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrWhereEdge (TYP-172): line 488, col 10
Attribute 'startTim' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "pAttrE2": "positionId",
      "pAttrT1": "registrationDate",
      "pAttrS1": "company_name",
      "pAttrE1": "industryId",
      "\"qGetAttrWhereComplex()\"": "qGetAttrWhereComplex()"
    },
    {"res": [
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "\"qGetAttrAccum()\"": "qGetAttrAccum()",
      "pAttr2": "startTime",
      "pAttr1": "registrationDate"
    },
    {"@@ga": 66},
    {"res": [{
      "v_id": "c1",
      "attributes": {
        "company_name": "com1",
        "nCount": 1,
        "@va2": 0,
        "id": "c1",
        "label": true,
        "@va": 42
      },
      "v_type": "company"
    }]},
    {"res": [{
      "v_id": "c1",
      "attributes": {
        "company_name": "com1",
        "nCount": 1,
        "@va2": 24,
        "id": "c1",
        "label": true,
        "@va": 42
      },
      "v_type": "company"
    }]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrAccumInternal (TYP-172): line 560, col 18
Attribute 'gsql_sys_tag' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "pAttr": "registrationDate",
      "\"qGetAttrPostAccum()\"": "qGetAttrPostAccum()"
    },
    {"@@ga": 66},
    {"@@ga2": 24}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrPostAccumInternal (TYP-172): line 600, col 23
Attribute 'gsql_sys_tag' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "\"qGetAttrHaving()\"": "qGetAttrHaving()",
      "pAttr2": "company_name",
      "pAttr1": "registrationDate"
    },
    {"res": [{
      "v_id": "c1",
      "attributes": {
        "company_name": "com1",
        "nCount": 1,
        "@va2": 0,
        "id": "c1",
        "label": true,
        "@va": 24
      },
      "v_type": "company"
    }]},
    {"res": [{
      "v_id": "c1",
      "attributes": {
        "company_name": "com1",
        "nCount": 1,
        "@va2": 24,
        "id": "c1",
        "label": true,
        "@va": 24
      },
      "v_type": "company"
    }]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrHavingInternal (TYP-172): line 643, col 11
Attribute 'gsql_sys_tag' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "\"qGetAttrOrderBy()\"": "qGetAttrOrderBy()",
      "pAttr2": "registrationDate",
      "pAttr1": "company_name"
    },
    {"res": [
      {
        "v_id": "c1",
        "attributes": {
          "company_name": "com1",
          "nCount": 1,
          "id": "c1",
          "label": true
        },
        "v_type": "company"
      },
      {
        "v_id": "c2",
        "attributes": {
          "company_name": "com2",
          "nCount": 0,
          "id": "c2",
          "label": false
        },
        "v_type": "company"
      },
      {
        "v_id": "c3",
        "attributes": {
          "company_name": "com3",
          "nCount": 1,
          "id": "c3",
          "label": true
        },
        "v_type": "company"
      }
    ]},
    {"res": [
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "id": "m1",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "id": "m2",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m3",
        "attributes": {
          "registrationDate": 2,
          "id": "m3",
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrOrderByInternal (TYP-172): line 682, col 13
Attribute 'gsql_sys_tag' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "pAttr2": "nCount",
      "pAttr1": "company_name",
      "pAttr3": "label",
      "\"qGetAttrPrintVSet()\"": "qGetAttrPrintVSet()"
    },
    {"res": [{
      "v_id": "c1",
      "attributes": {
        "res.getAttr(pAttr3,\"BOOL\")": true,
        "res.getAttr(pAttr1,\"STRING\")": "com1",
        "res.getAttr(pAttr2,\"INT\")": 1
      },
      "v_type": "company"
    }]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrPrintVSet (TYP-172): line 707, col 6
Attribute 'labe' does not exist.
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]
{
  "error": false,
  "message": "",
  "version": {
    "schema": 2,
    "edition": "enterprise",
    "api": "v2"
  },
  "results": [
    {
      "company_name": "company_name",
      "registrationDate": "registrationDate",
      "startTime": "startTime",
      "label": "label",
      "\"qGetAttrGlobal()\"": "qGetAttrGlobal()"
    },
    {"vs": [{
      "v_id": "m1",
      "attributes": {
        "registrationDate": 0,
        "@va2": [],
        "id": "m1",
        "@va": [],
        "profileIndustryId": "i1"
      },
      "v_type": "members"
    }]},
    {"vs": [
      {
        "v_id": "c1",
        "attributes": {
          "company_name": "com1",
          "nCount": 1,
          "@va2": [],
          "id": "c1",
          "label": true,
          "@va": [1]
        },
        "v_type": "company"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "@va2": [],
          "id": "m1",
          "@va": [1],
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      }
    ]},
    {"vs": [
      {
        "v_id": "m6",
        "attributes": {
          "registrationDate": 5,
          "@va2": ["com2"],
          "id": "m6",
          "@va": [],
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m2",
        "attributes": {
          "registrationDate": 1,
          "@va2": ["com2"],
          "id": "m2",
          "@va": [],
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m9",
        "attributes": {
          "registrationDate": 8,
          "@va2": ["com2"],
          "id": "m9",
          "@va": [],
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m4",
        "attributes": {
          "registrationDate": 3,
          "@va2": ["com2"],
          "id": "m4",
          "@va": [],
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m11",
        "attributes": {
          "registrationDate": 10,
          "@va2": ["com4"],
          "id": "m11",
          "@va": [],
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m1",
        "attributes": {
          "registrationDate": 0,
          "@va2": ["com2"],
          "id": "m1",
          "@va": [1],
          "profileIndustryId": "i1"
        },
        "v_type": "members"
      },
      {
        "v_id": "m10",
        "attributes": {
          "registrationDate": 9,
          "@va2": ["com4"],
          "id": "m10",
          "@va": [],
          "profileIndustryId": "i3"
        },
        "v_type": "members"
      },
      {
        "v_id": "m5",
        "attributes": {
          "registrationDate": 4,
          "@va2": ["com2"],
          "id": "m5",
          "@va": [],
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      },
      {
        "v_id": "m7",
        "attributes": {
          "registrationDate": 6,
          "@va2": [
            "com4",
            "com2"
          ],
          "id": "m7",
          "@va": [],
          "profileIndustryId": "i2"
        },
        "v_type": "members"
      }
    ]},
    {"vs": [
      {
        "v_id": "c3",
        "attributes": {"vs.getAttr(label,\"BOOL\")": true},
        "v_type": "company"
      },
      {
        "v_id": "c1",
        "attributes": {"vs.getAttr(label,\"BOOL\")": true},
        "v_type": "company"
      }
    ]}
  ]
}
[GTEST_JSON_END]
[GTEST_JSON_BEGIN]

Type Check Error in query qGetAttrTypesNotConsistent (TYP-518): line 755, col 10
Attribute types are not consistent
[GTEST_JSON_END]
[GTEST_END]
