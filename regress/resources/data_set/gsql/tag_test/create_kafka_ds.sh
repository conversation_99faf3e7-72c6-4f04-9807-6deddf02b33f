#!/bin/bash
REGRESS_HOME=$1

source $REGRESS_HOME/resources/common/util.sh
TOPIC_NAME=tag-test

$REGRESS_HOME/resources/kafka_loading/kafka_data_generator/create_topic.sh $TOPIC_NAME
wait_topic_create $TOPIC_NAME
cat > /tmp/kafka_topic.json << EOL
{
  "topic": "$TOPIC_NAME",
  "default_start_offset": -2,
  "partition_list": []
}
EOL

KAFKA_CONF_DIR=$(gadmin config get System.DataRoot)/$(gadmin config get Controller.ConfigRepoRelativePath)/kafka/conf
ZK_CONF=$(grep "zookeeper.connect=" $KAFKA_CONF_DIR/server.properties | cut -d'=' -f2)
BROKER=$(echo "$ZK_CONF" | sed 's/19999/30002/g')
cat > /tmp/kafka_broker.json <<EOL
{
  "broker": "$BROKER"
}
EOL

gsql 'CREATE DATA_SOURCE KAFKA ka = "/tmp/kafka_broker.json"'
