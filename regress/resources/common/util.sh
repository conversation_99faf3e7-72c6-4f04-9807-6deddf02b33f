#!/usr/bin/env bash
###########################################################
## Some utility bash functions for gle regression tests. ##
###########################################################

# Download file from NAS.
# The remote file path should start with forward slash.
# The remote file should be a tar file, either raw or gzipped.
# $1 - the remote file path
# $2 - the target extract path
function download_and_extract () {
  local -r remote_file_path="$1"
  local -r extract_path="$2"

  # We could extend this to support other extensions.
  local tar_opt=''
  if [[ "${remote_file_path}" == *.gz ]]; then
    tar_opt='--gzip'
  fi

  mkdir -p "${extract_path}"

  # GLE-2304 move dependency files to ftp.
  curl --silent --fail "ftp://ftp.graphtiger.com${remote_file_path}" \
    | tar "${tar_opt}" -xf - -C "${extract_path}"
}

# Download LDBC SNB SF-0.1 raw data from NAS.
# $1 - the (optional) target extract path
function download_ldbc_snb_small () {
  local -r remote_file_path='/data_set/ldbc/ldbc_snb_data-small.tar.gz'
  local -r default_extract_path='./resources/data_set/ldbc_snb'
  local -r extract_path="${1:-${default_extract_path}}"

  if [ ! -d "${extract_path}/ldbc_snb_data-small" ]; then
    echo 'Downloading LDBC SNB SF-0.1 data.'
    download_and_extract "${remote_file_path}" "${extract_path}"
  else
    echo 'Already have LDBC SNB SF-0.1 data; skipping download.'
  fi
}

# Download algo_library_test raw data from NAS.
# $1 - the (optional) target extract path
function download_algo_library_test () {
  local -r remote_file_path='/gle/algo_library_test.tar.gz'
  local -r default_extract_path='./resources/data_set/algo_library'
  local -r extract_path="${1:-${default_extract_path}}"

  if [ ! -d "${extract_path}/algo_library_test" ]; then
    echo 'Downloading algo_library_test data.'
    download_and_extract "${remote_file_path}" "${extract_path}"
  else
    echo 'Already have algo_library_test data; skipping download.'
  fi
}

# Download TPC-H tools from NAS.
# $1 - the (optional) target extract path
function download_tpch_tools () {
  local -r remote_file_path='/data_set/tpch/tpch_2_17_0.tar.gz'
  local -r default_extract_path='./resources/data_set/tpch'
  local -r extract_path="${1:-${default_extract_path}}"

  if [ ! -d "${extract_path}/tpch_2_17_0" ]; then
    echo 'Downloading TPC-H tools.'
    download_and_extract "${remote_file_path}" "${extract_path}"
  else
    echo 'Already have TPC-H tools; skipping download.'
  fi
}

# Download TPC-H SF-1 data from NAS.
# $1 - the (optional) target extract path
function download_tpch_sf1 () {
  local -r remote_file_path='/data_set/tpch/tpch_sf1.tar.gz'
  local -r default_extract_path='./resources/data_set/tpch/sf-1'
  local -r extract_path="${1:-${default_extract_path}}"

  if [ ! -d "${extract_path}" ]; then
    echo 'Downloading TPC-H SF-1 data.'
    download_and_extract "${remote_file_path}" "${extract_path}"
  else
    echo 'Already have TPC-H SF-1 data; skipping download.'
  fi
}

# Download pocs from NAS.
# $1 - the (optional) target extract path
function download_pocs () {
  local -r remote_file_path='/gle/pocs.tar'
  local -r default_extract_path='./resources/data_set/gsql'
  local -r extract_path="${1:-${default_extract_path}}"

  if [ ! -d "${extract_path}/pocs" ]; then
    echo 'Downloading POCS data.'
    download_and_extract "${remote_file_path}" "${extract_path}"
  else
    echo 'Already have POCS data; skipping download.'
  fi
}

# Skip lines before the first "create query".
# This is for test case queries with the schema embedded in the same file.
# $1 - the file to print
function skip_before_first_query () {
  local file="$1"
  # Suppress the normal printing behavior with -n flag.
  # The sed script translates to:
  # From the line that matches the regex to the last line, print the lines.
  sed -nE '/create( or replace)?( batch| distributed)? query/I,$p' "${file}"
}

# Enable/disable RESTPP auth.
# $1 - true to enable; false to disable
function set_restpp_auth () {
  echo "[GTEST_IB]"
  # enable/disable RESTPP auth
  gadmin config set RESTPP.Factory.EnableAuth $1
  gadmin config apply -y
  # restart all affected services
  # wait until restpp is online
  gadmin restart gsql gui nginx restpp -y --wait-online
  echo "[GTEST_IE]"
}

# Enable/Disable UDF Policy
function set_udf_policy () {
  echo "[GTEST_IB]"
  gadmin config set GSQL.UDF.Policy.Enable $1
  gadmin config apply -y
  gadmin restart gsql -y --wait-online
  echo "[GTEST_IE]"
}

# put powerful UDF file that violates UDF code scan policy
# The "ExprUtil.hpp" is fixed
# For "ExprFunctions.hpp":
#   if no parameter is given, then put from the "resources/gsql/powerfulUDF/"
#   if there is parameter, say 'regress802', then put from  "resources/gsql/regress802/"
function put_powerful_udf () {
  # temporarily disable UDF policy to allow this UDF file
  udfPolicyEnabled=$(gadmin config get GSQL.UDF.Policy.Enable)
  if [[ $udfPolicyEnabled == "true" ]]; then
    set_udf_policy false
  fi
  pathToUdf=
  if [ $# -eq 0 ]; then
    pathToUdf="resources/gsql/powerfulUDF/"
  else
    pathToUdf="resources/gsql/$1/"
  fi

  # update ExprUtil.hpp and ExprFunctions.hpp
  gsql 'PUT ExprUtil FROM "resources/common/QueryUdf/ExprUtil.hpp"'
  gsql "PUT ExprFunctions FROM \"$pathToUdf\""

  # re-enable udf policy if needed
  if [[ $udfPolicyEnabled == "true" ]]; then
    set_udf_policy true
  fi
}

# remove udf/tokenbank under DataRoot/gsql
function remove_udf () {
  echo "[GTEST_IB]"
  local gsql_data_root
  gsql_data_root="$(gadmin config get System.DataRoot)/$(gadmin config get GSQL.DataRelativePath)"
  echo "Removing ExprFunctions/ExprUtil..."
  grun gsql "rm -f $gsql_data_root/udf/*.hpp"
  echo "Removing TokenBank..."
  grun gsql "rm -f $gsql_data_root/tokenbank/*.cpp"
  echo "[GTEST_IE]"
}

# Set GSQL.BasicConfig.Env
function set_env() {
  gadmin config set GSQL.BasicConfig.Env "$1"
  gadmin config apply -y
  # restart gsql, and wait until online
  gadmin restart gsql -y --wait-online
}

# wait until all connectors to be deleted
function wait_until_connector_deleted() {
  local NUM_RETRY=0
  local RETRY_MAX=10
  while [ $NUM_RETRY -lt $RETRY_MAX ]
  do
    res=$(curl -s -w '\n' localhost:30003/connectors)
    if [ "$res" = "[]" ]; then
      break;
    else
      (( NUM_RETRY++ ))
      sleep 10;
    fi
  done
  if [ "$res" != "[]" ]; then
    echo "failed to delete kafka connector."
    echo $res
  fi
}

# check if given topic is ready, if is ready, return 0, else return 1
function check_topic_is_ready {
    local topic="$1"
    if [ -z "${topic}" ]; then
        echo "Please provide topic name to check"
        exit 1
    fi
    kafka_bin=$(gadmin config get System.Approot)/kafka/bin
    broker="$(gmyip):$(gadmin config get Kafka.port)"
    $kafka_bin/kafka-topics.sh  --bootstrap-server $broker  --topic $topic --describe 2>/dev/null |grep -q Leader
    return $?
}

# This function is used to wait for topic creation. Kafka topic creation
# is async, this operation is needed when there is dependency on the topic
# creation.
# $1 - topic name
# $2 - number of retries (default 30). 1s sleep between each retry.
function wait_topic_create {
    local topic="$1"
    local retry="$2"
    if [ -z "${topic}" ]; then
        echo "Please provide topic name to check"
        exit 1
    fi
    if [ -z "${retry}" ]; then
        retry=30
    fi
    # check topic with retry
    local i=0
    for ((i = 0; i < "${retry}"; i++)); do
        check_topic_is_ready $topic
        ec=$?
        if [[ $ec -eq 0 ]]; then
            return 0
        fi
        sleep 1
    done
    if [[ $ec -eq 0 ]]; then
        return 0
    fi
    echo "Wait for topic create timeout."
    exit 1
}