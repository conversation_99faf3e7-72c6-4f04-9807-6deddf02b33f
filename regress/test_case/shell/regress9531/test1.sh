#!/bin/bash
#############################################################
# Positive Tests Kafka loading
# Tester: <PERSON><PERSON>
# Details:
#############################################################

TOPIC_NAME=kiwi-1
PRODUCER_FIX=producer_fix.py

fresh_start

echo "[GTEST_IB]"
source resources/shell/utils/regress9531.util
source resources/common/util.sh

# only show the topic
function readProgress() {
  cat "$1" | sed 's/ //g' | awk -F'|' '{print $2}'| grep "$2" | sort | uniq | tail -n 1
}

# 1. use the Kafka server in TigerGraph platform
# 2. test data source
# 3. test run Kafka loading
# 4. test check status
# 5. test abort loading
# 6. test resume job
KAFKA_CONF_DIR=$(gadmin config get System.DataRoot)/$(gadmin config get Controller.ConfigRepoRelativePath)/kafka/conf
zk_broker=$(grep "zookeeper.connect=" $KAFKA_CONF_DIR/server.properties | cut -d'=' -f2)
broker=$(echo "$zk_broker" | sed 's/19999/30002/g')
echo "kafka broker: $broker"

cat > /tmp/kafka_config.json <<EOL
{
  "broker": "$broker"
}
EOL

cat > /tmp/topic_partition_config.json << EOL
{
  "topic": "$TOPIC_NAME",
  "partition_list": []
}
EOL

# setup schema, create data source and loading job
gsql '
  drop edge PersonConn

  CREATE VERTEX Person (PRIMARY_ID id uint, com int, com2 string)

  CREATE UNDIRECTED EDGE PersonConn (FROM Person, TO Person, strength string)
  CREATE GRAPH test_graph(*)'

echo "[GTEST_IE]"

gsql '
  USE GRAPH test_graph
  drop job load_person
  drop data_source k1, k2

  create data_source kafka k1 for graph test_graph
  set k1 = "/tmp/kafka_config.json"
  create data_source kafka k2 = "/tmp/kafka_config.json"

  CREATE LOADING JOB load_person FOR GRAPH test_graph {
    DEFINE FILENAME f1 = "$k1:/tmp/topic_partition_config.json";
    LOAD f1
        TO VERTEX Person VALUES ($2, $0, $1),
        TO EDGE PersonConn VALUES ($2, $2, $1)
        USING SEPARATOR=",";
  }'

echo "[GTEST_IB]"
# publish data to kafka broker
cd resources/kafka_loading/kafka_data_generator/
export PYTHONPATH=./thirdparties:$PYTHONPATH
./create_topic.sh $TOPIC_NAME
wait_topic_create $TOPIC_NAME

# test run Kafka loading
gsql -g test_graph 'RUN LOADING JOB load_person USING f1, EOF="false"' &> /tmp/shell9531.txt &
# check RUNNING or partitions is not reliable, no better alternatives
# there is chance that some data is not loaded, extend the wait time to start the loading job
sleep 30

# generate fixed data
python $PRODUCER_FIX $broker $TOPIC_NAME
wait_until_data_loaded 4
echo "[GTEST_IE]"

# compare results
gsql -g test_graph 'select * from Person limit 50'

jobId=$(grep "Jobid:" /tmp/shell9531.txt | awk '{print $2}')
# limit the grep output to be 1, since 2 nodes will print two summary tables
gsql -g test_graph "ABORT LOADING JOB $jobId" | grep -m1 "ABORT_SUCCESS"
echo "[GTEST_IB]"
gsql -g test_graph "RESUME LOADING JOB $jobId" &>/dev/null
# same as above
sleep 10
# add more data
python $PRODUCER_FIX $broker $TOPIC_NAME True
wait_until_data_loaded 7
echo "[GTEST_IE]"

gsql -g test_graph 'select * from Person limit 50'

gsql -g test_graph 'ABORT LOADING JOB ALL' | grep -m1 "ABORT_SUCCESS"

cd - &>/dev/null
# The 5 partitions are divided randomly, only show one line summary
gsql -g test_graph "show loading status $jobId" > /tmp/shell9531_1b.txt
readProgress /tmp/shell9531_1b.txt $TOPIC_NAME

# delete kafka records
./resources/kafka_loading/kafka_data_generator/clear.sh $TOPIC_NAME
