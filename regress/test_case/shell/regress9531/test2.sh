#!/bin/bash
#############################################################
# Positive Tests Kafka loading
# Tester: <PERSON><PERSON>
# Details:
#############################################################

# always create a different topic to avoid legacy data
# since remove Kafka data needs to delete the log folder
TOPIC_NAME="zzz$(date +%s%N)"
LOAD_SCRIPT=$PWD/resources/shell/kafka_load.gsql
PRODUCER_FIX=producer_fix.py

fresh_start

echo "[GTEST_IB]"
source resources/shell/utils/regress9531.util
source resources/common/util.sh

gadmin start all

# 1. use the Kafka server in TigerGraph platform
# 2. test data source
# 3. test run Kafka loading
# 4. test check status
# 5. test abort loading
# 6. test resume job

KAFKA_CONF_DIR=$(gadmin config get System.DataRoot)/$(gadmin config get Controller.ConfigRepoRelativePath)/kafka/conf
zk_broker=$(grep "zookeeper.connect=" $KAFKA_CONF_DIR/server.properties | cut -d'=' -f2)
broker=$(echo "$zk_broker" | sed 's/19999/30002/g')
echo "kafka broker: $broker"

# setup schema, create data source and loading job
gsql '
  CREATE VERTEX Person (PRIMARY_ID id uint, com int, com2 string)

  CREATE UNDIRECTED EDGE PersonConn (FROM Person, TO Person, strength string)
  CREATE GRAPH test_graph(*)'

echo "[GTEST_IE]"

# set broker in gsql script, run, and then rollback
sed -i.bak "s/broker_json/$broker/g" $LOAD_SCRIPT
sed -i "s/zzz/$TOPIC_NAME/g" $LOAD_SCRIPT
gsql $LOAD_SCRIPT
mv $LOAD_SCRIPT.bak $LOAD_SCRIPT

echo "[GTEST_IB]"
# publish data to kafka broker
cd resources/kafka_loading/kafka_data_generator
export PYTHONPATH=./thirdparties:$PYTHONPATH
./create_topic.sh $TOPIC_NAME
wait_topic_create $TOPIC_NAME

# test run Kafka loading
gsql -g test_graph 'RUN LOADING JOB load_person USING f1, EOF="false"' &> /tmp/shell9531b.txt &
# check RUNNING or partitions is not reliable, no better alternatives
sleep 30

# generate fixed data
python $PRODUCER_FIX $broker $TOPIC_NAME
wait_until_data_loaded 4
echo "[GTEST_IE]"

# compare results
gsql -g test_graph 'select * from Person limit 50'

jobId=$(grep "Jobid:" /tmp/shell9531b.txt | awk '{print $2}')
# limit the grep output to be 1, since 2 nodes will print two summary tables
gsql -g test_graph "ABORT LOADING JOB $jobId" | grep -m1 "ABORT_SUCCESS"
echo "[GTEST_IB]"
gsql -g test_graph "RESUME LOADING JOB $jobId" &>/dev/null
sleep 10

# add more data
python $PRODUCER_FIX $broker $TOPIC_NAME True
wait_until_data_loaded 7
echo "[GTEST_IE]"

gsql -g test_graph 'select * from Person limit 50'

gsql -g test_graph 'ABORT LOADING JOB ALL' | grep -m1 "ABORT_SUCCESS"
