#!/bin/bash
#############################################################
# Positive Tests data loading control API
# Tester: Haocheng Zhao
# Details:
#    1. start loading job using Kafka dataSource (make use of the regress9561 creating dataSource)
#############################################################

TOPIC_NAME=kiwi
fresh_start

source resources/common/util.sh

echo "[GTEST_IB]"
KAFKA_CONF_DIR=$(gadmin config get System.DataRoot)/$(gadmin config get Controller.ConfigRepoRelativePath)/kafka/conf
zk_broker=$(grep "zookeeper.connect=" $KAFKA_CONF_DIR/server.properties | cut -d'=' -f2)
broker=$(echo "$zk_broker" | sed 's/19999/30002/g')
echo "kafka broker: $broker"

cat > /tmp/kafka_config.json <<EOL
{
  "broker": "$broker",
  "kafka_config": {"group.id":"tigergraph"}
}
EOL

# create topic
./resources/kafka_loading/kafka_data_generator/create_topic.sh $TOPIC_NAME
wait_topic_create $TOPIC_NAME
echo "[GTEST_IE]"

gsql '
CREATE VERTEX Person (PRIMARY_ID id uint, com int, com2 string)
CREATE UNDIRECTED EDGE PersonConn (FROM Person, TO Person, strength string)

create data_source kafka k1 = "/tmp/kafka_config.json"

CREATE GRAPH test_graph(*)
'

# only define the file name and we will pass in the json configuration when starting the loading job
gsql -g test_graph '
CREATE LOADING JOB load_person FOR GRAPH test_graph {
  DEFINE FILENAME f1;
  LOAD f1
      TO VERTEX Person VALUES ($2, $0, $1),
      TO EDGE PersonConn VALUES ($2, $2, $1)
      USING SEPARATOR=",";
}'

echo "[GTEST_JSON_BEGIN]"
# use API to start loading job and pass in the configuration json directly in string
curl --user tigergraph:tigergraph  -d '
[
   {
      "name":"load_person",
      "dataSources":[
         {
            "filename":"f1",
            "name":"k1",
            "path":"",
            "config":{
               "topic":"kiwi",
               "partition_list":[
                  {
                     "start_offset":-2,
                     "partition":0
                  }
               ]
            }
         }
      ],
      "streaming":false
   }
]
' -X POST "http://localhost:8123/gsql/loading-jobs?graph=test_graph&action=start"  | jq ".error, .results.load_person.error"
echo "[GTEST_JSON_END]"
gsql -g test_graph 'abort loading job all'
