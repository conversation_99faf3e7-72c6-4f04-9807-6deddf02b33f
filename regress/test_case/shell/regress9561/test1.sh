#!/bin/bash
#############################################################
# Positive Tests test create graph with datasource
# Tester: <PERSON><PERSON>
# Details:
#############################################################

TOPIC_NAME=kiwi

fresh_start

source resources/common/util.sh

echo "[GTEST_IB]"
KAFKA_CONF_DIR=$(gadmin config get System.DataRoot)/$(gadmin config get Controller.ConfigRepoRelativePath)/kafka/conf
zk_broker=$(grep "zookeeper.connect=" $KAFKA_CONF_DIR/server.properties | cut -d'=' -f2)
broker=$(echo "$zk_broker" | sed 's/19999/30002/g')
echo "kafka broker: $broker"

cat > /tmp/kafka_config.json <<EOL
{
  "broker": "$broker",
  "kafka_config": {"group.id":"tigergraph"}
}
EOL

cat > /tmp/topic_partition1.json << EOL
{
  "topic": "kiwi",
  "default_start_offset": 0,
  "partition_list": []
}
EOL

cat > /tmp/topic_partition2.json << EOL
{
  "topic": "$TOPIC_NAME"
}
EOL
# create topic
./resources/kafka_loading/kafka_data_generator/create_topic.sh $TOPIC_NAME
wait_topic_create $TOPIC_NAME
echo "[GTEST_IE]"

gsql '
CREATE VERTEX Person (PRIMARY_ID id uint, com int, com2 string)
CREATE UNDIRECTED EDGE PersonConn (FROM Person, TO Person, strength string)

create data_source kafka k1 = "/tmp/kafka_config.json"

CREATE GRAPH test_graph(*)
'

# check the empty partition_list message
gsql -g test_graph '
CREATE LOADING JOB load_person FOR GRAPH test_graph {
  DEFINE FILENAME f1 = "$k1:/tmp/topic_partition1.json";
  DEFINE FILENAME f2 = "$k1:/tmp/topic_partition2.json";
  LOAD f1
      TO VERTEX Person VALUES ($2, $0, $1),
      TO EDGE PersonConn VALUES ($2, $2, $1)
      USING SEPARATOR=",";
}'
# create the file first, otherwise tail may not find this file
touch /tmp/shell9561.txt
gsql -g test_graph 'RUN LOADING JOB load_person' &> /tmp/shell9561.txt &
#remove sleep which has randomness, implment it in a deterministic approach
#block until a string is matched in a file (tail + grep with blocking)
grep -m1 'TOPIC PARTITION' <(tail -n +1 -f /tmp/shell9561.txt)

echo "[GTEST_IB]"
gsql -g test_graph 'abort loading job all'
echo "[GTEST_IE]"
