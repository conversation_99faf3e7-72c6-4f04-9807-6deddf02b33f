# Install Query Success Output Enhancement

## Overview

This enhancement adds functionality to print the names of successfully installed queries and functions after executing the "install query" and "install function" commands.

## Changes Made

### 1. InstallQueryOperation.java

**File**: `gsql-server/src/main/java/com/tigergraph/schema/operation/InstallQueryOperation.java`

**Changes**:
- Modified the `handleSuccess()` method to print the names of successfully installed queries
- Added logic to check if `installQb.newlyInstalledQueries` is not empty
- If there are newly installed queries, they are sorted alphabetically and displayed in a formatted message

**Code Added**:
```java
// Print the names of successfully installed queries
if (!installQb.newlyInstalledQueries.isEmpty()) {
  List<String> sortedQueries = new ArrayList<>(installQb.newlyInstalledQueries);
  Collections.sort(sortedQueries);
  Util.printlnToStream(String.format("The following queries were successfully installed:\n[%s]",
      String.join(", ", sortedQueries)));
}
```

### 2. InstallFunctionOperation.java

**File**: `gsql-server/src/main/java/com/tigergraph/schema/operation/InstallFunctionOperation.java`

**Changes**:
- Modified the `handleSuccess()` method to print the names of successfully installed functions
- Added logic to check which functions in `funcInfoList4Compile` were successfully installed
- Functions are sorted alphabetically and displayed in a formatted message
- Added import for `Collections`

**Code Added**:
```java
// Print the names of successfully installed functions
if (!funcInfoList4Compile.isEmpty()) {
  List<String> installedFunctions = new ArrayList<>();
  for (FunctionInfo fi : funcInfoList4Compile) {
    if (fi.installed) {
      installedFunctions.add(fi.FunctionName);
    }
  }
  if (!installedFunctions.isEmpty()) {
    Collections.sort(installedFunctions);
    Util.printlnToStream(String.format("The following functions were successfully installed:\n[%s]",
        String.join(", ", installedFunctions)));
  }
}
```

### 3. Test Enhancements

**Files**: 
- `gsql-server/src/test/java/com/tigergraph/schema/operation/InstallQueryOperationTest.java`
- `gsql-server/src/test/java/com/tigergraph/schema/operation/InstallFunctionOperationTest.java`

**Changes**:
- Added test cases to verify that the success messages with query/function names are properly displayed
- Tests check for the presence of the success message format and specific query/function names

## Expected Output

### Before Changes
```
Query installation finished.
```

### After Changes
```
Query installation finished.
The following queries were successfully installed:
[query1, query2, query3]
```

### For Functions
```
Function installation finished for package 'packageName'.
The following functions were successfully installed:
[function1, function2, function3]
```

## Benefits

1. **Better User Experience**: Users can now see exactly which queries/functions were successfully installed
2. **Debugging Aid**: Helps users identify which specific queries/functions failed if only some succeed
3. **Consistency**: Provides similar output format for both queries and functions
4. **Sorted Output**: Query and function names are displayed in alphabetical order for better readability

## Backward Compatibility

These changes are fully backward compatible. The existing success messages are preserved, and the new functionality only adds additional information without changing existing behavior.
